'use client'

import * as React from 'react'

import { cn } from '@/lib/utils'

const rand = (min: number, max: number): number =>
  Math.random() * (max - min) + min

const randInt = (min: number, max: number): number =>
  Math.floor(Math.random() * (max - min) + min)

const randColor = (): string => `hsl(${randInt(0, 360)}, 100%, 50%)`

type ParticleType = {
  x: number
  y: number
  color: string
  speed: number
  direction: number
  vx: number
  vy: number
  gravity: number
  friction: number
  alpha: number
  decay: number
  size: number
  update: () => void
  draw: (ctx: CanvasRenderingContext2D) => void
  isAlive: () => boolean
}

function createParticle(
  x: number,
  y: number,
  color: string,
  speed: number,
  direction: number,
  gravity: number,
  friction: number,
  size: number
): ParticleType {
  const vx = Math.cos(direction) * speed
  const vy = Math.sin(direction) * speed
  const alpha = 1
  const decay = rand(0.005, 0.02)

  return {
    x,
    y,
    color,
    speed,
    direction,
    vx,
    vy,
    gravity,
    friction,
    alpha,
    decay,
    size,
    update() {
      this.vx *= this.friction
      this.vy *= this.friction
      this.vy += this.gravity
      this.x += this.vx
      this.y += this.vy
      this.alpha -= this.decay
    },
    draw(ctx: CanvasRenderingContext2D) {
      ctx.save()
      ctx.globalAlpha = this.alpha
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
      ctx.fillStyle = this.color
      ctx.fill()
      ctx.restore()
    },
    isAlive() {
      return this.alpha > 0
    },
  }
}

type FireworkType = {
  x: number
  y: number
  targetY: number
  color: string
  speed: number
  size: number
  angle: number
  vx: number
  vy: number
  trail: { x: number; y: number }[]
  trailLength: number
  exploded: boolean
  update: () => boolean // 返回 false 表示需要移除
  explode: () => void
  draw: (ctx: CanvasRenderingContext2D) => void
}

function createFirework(
  x: number,
  y: number,
  targetY: number,
  color: string,
  speed: number,
  size: number,
  particleSpeed: { min: number; max: number } | number,
  particleSize: { min: number; max: number } | number,
  onExplode: (particles: ParticleType[]) => void
): FireworkType {
  const angle = -Math.PI / 2 + rand(-0.3, 0.3)
  const vx = Math.cos(angle) * speed
  const vy = Math.sin(angle) * speed
  const trail: { x: number; y: number }[] = []
  const trailLength = randInt(10, 25)

  return {
    x,
    y,
    targetY,
    color,
    speed,
    size,
    angle,
    vx,
    vy,
    trail,
    trailLength,
    exploded: false, // 标记是否已爆炸，主要用于 explode 方法只执行一次
    update() {
      if (this.exploded) return false // 如果已经标记爆炸（粒子已生成），则直接移除发射器

      this.trail.push({ x: this.x, y: this.y })
      if (this.trail.length > this.trailLength) {
        this.trail.shift()
      }
      this.x += this.vx
      this.y += this.vy
      this.vy += 0.02 // gravity for firework rocket

      if (this.vy >= 0 || this.y <= this.targetY) {
        // 到达顶点或目标点
        this.explode()
        this.exploded = true // 标记已爆炸
        return false // 告诉 animate 循环这个烟花发射器可以移除了
      }
      return true
    },
    explode() {
      const numParticles = randInt(50, 150)
      const particles: ParticleType[] = []
      for (let i = 0; i < numParticles; i++) {
        const particleAngle = rand(0, Math.PI * 2)
        const localParticleSpeed = getValueByRange(particleSpeed)
        const localParticleSize = getValueByRange(particleSize)
        particles.push(
          createParticle(
            this.x,
            this.y,
            this.color,
            localParticleSpeed,
            particleAngle,
            0.05, // particle gravity
            0.98, // particle friction
            localParticleSize
          )
        )
      }
      onExplode(particles)
    },
    draw(ctx: CanvasRenderingContext2D) {
      ctx.save()
      ctx.beginPath()
      if (this.trail.length > 1) {
        ctx.moveTo(this.trail[0]?.x ?? this.x, this.trail[0]?.y ?? this.y)
        for (const point of this.trail) {
          ctx.lineTo(point.x, point.y)
        }
      } else {
        // 处理 trail 为空或只有一个点的情况
        ctx.moveTo(this.x - this.vx, this.y - this.vy) // 模拟一个小尾巴
        ctx.lineTo(this.x, this.y)
      }
      ctx.strokeStyle = this.color
      ctx.lineWidth = this.size
      ctx.lineCap = 'round'
      ctx.stroke()
      ctx.restore()
    },
  }
}

function getValueByRange(range: { min: number; max: number } | number): number {
  if (typeof range === 'number') {
    return range
  }
  return rand(range.min, range.max)
}

function getColor(color: string | string[] | undefined): string {
  if (Array.isArray(color)) {
    return color[randInt(0, color.length)] ?? randColor()
  }
  return color ?? randColor()
}

type FireworksBackgroundProps = Omit<React.ComponentProps<'div'>, 'color'> & {
  ref?: React.Ref<HTMLDivElement>
  canvasProps?: React.ComponentProps<'canvas'>
  population?: number
  color?: string | string[]
  fireworkSpeed?: { min: number; max: number } | number
  fireworkSize?: { min: number; max: number } | number
  particleSpeed?: { min: number; max: number } | number
  particleSize?: { min: number; max: number } | number
}

function FireworksBackground({
  ref,
  className,
  canvasProps,
  population = 1,
  color,
  fireworkSpeed = { min: 4, max: 8 },
  fireworkSize = { min: 2, max: 5 },
  particleSpeed = { min: 2, max: 7 },
  particleSize = { min: 1, max: 5 },
  ...props
}: FireworksBackgroundProps) {
  const canvasRef = React.useRef<HTMLCanvasElement>(null)
  const containerRef = React.useRef<HTMLDivElement>(null)
  React.useImperativeHandle(ref, () => containerRef.current as HTMLDivElement)

  // Refs to store timeout and animation frame IDs
  const launchTimeoutIdRef = React.useRef<NodeJS.Timeout | null>(null)
  const animationFrameIdRef = React.useRef<number | null>(null)

  // Refs to store fireworks and particles to persist them across re-renders if needed
  // and to be accessible in callbacks that might have stale closures.
  // For this specific effect, re-initializing them on prop change (via useEffect) is intended.
  const fireworksRef = React.useRef<FireworkType[]>([])
  const explosionsRef = React.useRef<ParticleType[]>([])

  React.useEffect(() => {
    const canvas = canvasRef.current
    const container = containerRef.current
    if (!canvas || !container) return
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    let maxX = window.innerWidth
    let ratio =
      container.offsetHeight > 0 && container.offsetWidth > 0
        ? container.offsetHeight / container.offsetWidth
        : window.innerHeight / window.innerWidth
    let maxY = maxX * ratio
    canvas.width = maxX
    canvas.height = maxY

    const setCanvasSize = () => {
      // Ensure container has dimensions before calculating ratio
      if (container.offsetWidth > 0 && container.offsetHeight > 0) {
        maxX = window.innerWidth
        // Update ratio based on potentially changed container dimensions
        ratio = container.offsetHeight / container.offsetWidth
        maxY = maxX * ratio
        canvas.width = maxX
        canvas.height = maxY
      } else {
        // Fallback if container has no dimensions (e.g., display: none)
        maxX = window.innerWidth
        maxY = window.innerHeight
        canvas.width = maxX
        canvas.height = maxY
      }
    }
    window.addEventListener('resize', setCanvasSize)
    setCanvasSize() // Initial size set

    // Reset fireworks and explosions on prop change or initial setup
    fireworksRef.current = []
    explosionsRef.current = []

    const handleExplosion = (particles: ParticleType[]) => {
      explosionsRef.current.push(...particles)
    }

    const launchFirework = () => {
      // If tab is hidden, don't launch or schedule next
      if (document.hidden) {
        return
      }

      const x = rand(maxX * 0.1, maxX * 0.9)
      const y = maxY
      const targetY = rand(maxY * 0.1, maxY * 0.4)
      const fireworkColor = getColor(color)
      const speed = getValueByRange(fireworkSpeed)
      const size = getValueByRange(fireworkSize)
      fireworksRef.current.push(
        createFirework(
          x,
          y,
          targetY,
          fireworkColor,
          speed,
          size,
          particleSpeed,
          particleSize,
          handleExplosion
        )
      )

      // Clear previous timeout before setting a new one
      if (launchTimeoutIdRef.current) {
        clearTimeout(launchTimeoutIdRef.current)
      }
      const timeoutDuration = rand(300, 800) / (population > 0 ? population : 1)
      launchTimeoutIdRef.current = setTimeout(launchFirework, timeoutDuration)
    }

    const animate = () => {
      // If tab is hidden, don't animate
      if (document.hidden) {
        // We will restart animation via handleVisibilityChange when tab is visible again
        return
      }

      ctx.clearRect(0, 0, maxX, maxY)

      // Update and draw fireworks
      for (let i = fireworksRef.current.length - 1; i >= 0; i--) {
        const firework = fireworksRef.current[i]
        if (!firework?.update()) {
          // update() returns false if firework exploded and should be removed
          fireworksRef.current.splice(i, 1)
        } else {
          firework.draw(ctx)
        }
      }

      // Update and draw explosions (particles)
      for (let i = explosionsRef.current.length - 1; i >= 0; i--) {
        const particle = explosionsRef.current[i]
        particle?.update()
        if (particle?.isAlive()) {
          particle.draw(ctx)
        } else {
          explosionsRef.current.splice(i, 1)
        }
      }

      animationFrameIdRef.current = requestAnimationFrame(animate)
    }

    // Start animations only if page is visible
    if (!document.hidden) {
      launchFirework()
      animationFrameIdRef.current = requestAnimationFrame(animate)
    }

    const handleClick = (event: MouseEvent) => {
      // Ensure canvas and container are still valid, and page is visible
      if (!canvasRef.current || !containerRef.current || document.hidden) return

      const rect = canvasRef.current.getBoundingClientRect()
      const x = event.clientX - rect.left // Click position relative to canvas
      const localTargetY = event.clientY - rect.top // Click position relative to canvas

      // Launch from bottom of canvas towards click point
      const startY = maxY
      const fireworkColor = getColor(color)
      const speed = getValueByRange(fireworkSpeed)
      const size = getValueByRange(fireworkSize)

      fireworksRef.current.push(
        createFirework(
          x,
          startY,
          localTargetY,
          fireworkColor,
          speed,
          size,
          particleSpeed,
          particleSize,
          handleExplosion
        )
      )
    }
    container.addEventListener('click', handleClick)

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Clear timeout for launching fireworks
        if (launchTimeoutIdRef.current) {
          clearTimeout(launchTimeoutIdRef.current)
          launchTimeoutIdRef.current = null
        }
        // Cancel animation frame
        if (animationFrameIdRef.current) {
          cancelAnimationFrame(animationFrameIdRef.current)
          animationFrameIdRef.current = null
        }
      } else {
        // Tab is visible again, restart fireworks and animation
        // Ensure canvas size is up-to-date
        setCanvasSize()
        // Clear any potentially old particles/fireworks if a very clean restart is desired
        // fireworksRef.current = [];
        // explosionsRef.current = [];

        // Restart launching and animation
        launchFirework()
        if (!animationFrameIdRef.current) {
          // Only start if not already running (e.g. from initial load)
          animationFrameIdRef.current = requestAnimationFrame(animate)
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup function
    return () => {
      window.removeEventListener('resize', setCanvasSize)
      container.removeEventListener('click', handleClick)
      document.removeEventListener('visibilitychange', handleVisibilityChange)

      if (launchTimeoutIdRef.current) {
        clearTimeout(launchTimeoutIdRef.current)
      }
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current)
      }
    }
  }, [
    // Dependencies: if these change, the effect re-runs
    population,
    color,
    fireworkSpeed,
    fireworkSize,
    particleSpeed,
    particleSize,
  ]) // Removed animate and launchFirework from deps as they are defined inside useEffect or stable

  return (
    <div
      ref={containerRef}
      data-slot="fireworks-background"
      className={cn('size-full relative overflow-hidden', className)}
      {...props}
    >
      <canvas
        {...canvasProps}
        ref={canvasRef}
        className={cn('size-full absolute inset-0', canvasProps?.className)}
      />
    </div>
  )
}

export { FireworksBackground, type FireworksBackgroundProps }
