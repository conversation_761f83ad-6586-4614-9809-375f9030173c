'use client'

import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'

import { cn } from '@/lib/utils'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

const links = [
  {
    name: 'Area Charts',
    href: '/charts/area#charts',
  },
  {
    name: 'Bar Charts',
    href: '/charts/bar#charts',
  },
  {
    name: 'Line Charts',
    href: '/charts/line#charts',
  },
  {
    name: 'Pie Charts',
    href: '/charts/pie#charts',
  },
]

interface SmoothNavLinkProps {
  href: string
  isActive: boolean
  children: React.ReactNode
}

function SmoothNavLink({ href, isActive, children }: SmoothNavLinkProps) {
  const router = useRouter()
  const pathname = usePathname()

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()

    const [path, hash] = href.split('#')

    // If we're already on the target page, just scroll
    if (pathname === path) {
      const target = document.getElementById(hash)
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }
    } else {
      // Navigate to the page first, then scroll after navigation
      router.push(path)
      // Use setTimeout to ensure navigation completes before scrolling
      setTimeout(() => {
        const target = document.getElementById(hash)
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          })
        }
      }, 100)
    }
  }

  return (
    <button
      onClick={handleClick}
      data-active={isActive}
      className={cn(
        'text-muted-foreground hover:text-primary data-[active=true]:text-primary flex h-7 shrink-0 cursor-pointer items-center justify-center px-4 text-center text-base font-medium transition-colors'
      )}
    >
      {children}
    </button>
  )
}

export function ChartsNav({
  className,
  ...props
}: React.ComponentProps<'div'>) {
  const pathname = usePathname()

  return (
    <div className={cn('flex items-center gap-1', className)} {...props}>
      {links.map((link) => (
        <SmoothNavLink
          key={link.href}
          href={link.href}
          isActive={link.href.startsWith(pathname)}
        >
          {link.name}
        </SmoothNavLink>
      ))}
    </div>
  )
}
