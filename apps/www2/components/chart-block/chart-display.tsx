import * as React from 'react'
import { registryItemSchema } from '@imd/shadcn/registry'
import { z } from 'zod'

import { cn } from '@/lib/utils'
import { ChartToolbar } from '@/components/chart-block/chart-toolbar'

export type Chart = z.infer<typeof registryItemSchema>

export async function ChartDisplay({
  name,
  children,
  className,
}: { name: string } & React.ComponentProps<'div'>) {
  return (
    <div
      className={cn(
        'themes-wrapper group relative flex flex-col overflow-hidden rounded-xl border transition-all duration-200 ease-in-out hover:z-30',
        className
      )}
    >
      <ChartToolbar
        name={name}
        className="bg-card text-card-foreground relative z-20 flex justify-end border-b px-3 py-2.5"
      >
        {children}
      </ChartToolbar>
      <div className="relative z-10 p-4 [&>div]:rounded-none [&>div]:border-none [&>div]:shadow-none">
        {children}
      </div>
    </div>
  )
}

ChartDisplay.displayName = 'ChartDisplay'
