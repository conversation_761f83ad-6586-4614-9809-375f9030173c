'use client'

import { useState } from 'react'
import { Palette, Repeat } from 'lucide-react'

import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/registry/default/ui/ui-select'

import { THEMES } from '../../chart-theme/theme'
import { ThemesStyle } from './themes-styles'

export const ThemesSwitcher = (props: { className?: string }) => {
  const { className } = props
  const [activeTheme, setActiveTheme] = useState(THEMES[0])
  const options = THEMES.map((theme) => ({
    value: theme.name,
    label: theme.name,
  }))
  const handleThemeChange = (value: string) => {
    const theme = THEMES.find((theme) => theme.name === value)
    if (!theme) return
    setActiveTheme(theme)
  }

  return (
    <div className={cn('flex items-center justify-end ', className)}>
      <ThemesStyle activeTheme={activeTheme} />
      <div className="flex items-center gap-2">
        <Select
          dropdownMatchSelectWidth={false}
          value={activeTheme.name}
          onChange={handleThemeChange}
          variant="outlined"
        >
          <SelectTrigger className="p-3">
            <Palette className="flex h-4 w-4" />
            <SelectValue placeholder="Select Size" />
          </SelectTrigger>
          <SelectContent side="bottom" align="end" className="w-[160px]">
            {options.map((item) => (
              <SelectItem key={item.value} value={item.value}>
                {item.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
