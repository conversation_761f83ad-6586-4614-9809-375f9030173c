'use client'

import { Theme } from '../../chart-theme/theme'

export function ThemesStyle({ activeTheme }: { activeTheme: Theme }) {
  if (!activeTheme) return null

  return (
    <style>
      {`
        [data-chart-block] {
          ${Object.entries(activeTheme.cssVars.light)
            .map(([key, value]) => `${key}: ${value};`)
            .join('\n')}
        }
        /* 暗色模式 */
        .dark [data-chart-block] {
          ${Object.entries(activeTheme.cssVars.dark)
            .map(([key, value]) => `${key}: ${value};`)
            .join('\n')}
        }

      `}
    </style>
  )
}
