'use client'

import * as React from 'react'
import { DynamicCodeBlock } from 'fumadocs-ui/components/dynamic-codeblock'
import { LoaderIcon } from 'lucide-react'

import { cn } from '@/lib/utils'
// import { getFileContent } from '@/lib/getFileContent'
import { useMediaQuery } from '@/hooks/use-media-query'
import { Button } from '@/registry/default/ui/ui-button'
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerOverlay,
  DrawerTitle,
  DrawerTrigger,
} from '@/registry/default/ui/ui-drawer'

export function ChartCodeViewer({
  className,
  children,
  name,
  code = '',
}: {
  code?: string
  name: string
} & React.ComponentProps<'div'>) {
  const isDesktop = useMediaQuery('(min-width: 768px)')
  const [isLoading, setIsLoading] = React.useState(false)
  const [fetchedCode, setFetchedCode] = React.useState('')

  const fetchCode = React.useCallback(async () => {
    if (code || fetchedCode) return

    setIsLoading(true)
    try {
      const res = await fetch(`/r/styles/default/${name}.json`)
      const json = await res.json()
      const content = json?.files?.[0]?.content ?? ''
      setFetchedCode(content)
    } catch (error) {
      console.error('Failed to fetch code:', error)
    } finally {
      setIsLoading(false)
    }
  }, [name, code, fetchedCode])

  const displayCode = code || fetchedCode

  const button = (
    <Button
      size="small"
      color="reset"
      variant="outlined"
      style={{ fontSize: '12px' }}
      onClick={fetchCode}
    >
      View Code
    </Button>
  )

  // fumadocs 最新之后: "[&_.bg-fd-secondary]:bg-background flex min-h-0 flex-1 flex-col gap-0 [&_.bg-fd-secondary]:max-h-full [&_code]:h-[calc(100vh_-_440px)] [&_code]:overflow-auto [&_figure]:mt-4"
  const content = (
    <div className="flex min-h-0 flex-1 flex-col gap-0 [&_.bg-fd-secondary]:max-h-full [&_code]:h-[calc(100vh_-_440px)] [&_code]:overflow-auto [&_figure]:mt-4 [&_figure]:bg-[#f8f8f8]">
      <div className="theme-container hidden sm:block [&_[data-chart]]:max-h-[35vh]">
        {children}
      </div>
      {isLoading ? (
        <div className="flex h-40 items-center justify-center">
          <LoaderIcon className="text-muted-foreground h-6 w-6 animate-spin" />
          <span className="text-muted-foreground ml-2 text-sm">
            Loading code...
          </span>
        </div>
      ) : (
        <DynamicCodeBlock code={displayCode} lang="tsx" />
      )}
    </div>
  )

  if (!isDesktop) {
    return (
      <Drawer dismissible placement="right">
        <DrawerTrigger asChild>{button}</DrawerTrigger>
        <DrawerOverlay />
        <DrawerContent className={cn(className, 'w-full')}>
          <DrawerHeader>
            <DrawerTitle>Code</DrawerTitle>
          </DrawerHeader>
          <div className="flex h-full flex-col overflow-auto p-4">
            {content}
          </div>
        </DrawerContent>
      </Drawer>
    )
  }

  return (
    <Drawer dismissible placement="right">
      <DrawerTrigger asChild>{button}</DrawerTrigger>
      <DrawerOverlay />
      <DrawerContent size="large" className={className}>
        <DrawerHeader>
          <DrawerTitle>Code</DrawerTitle>
        </DrawerHeader>
        <div className="p-4">{content}</div>
      </DrawerContent>
    </Drawer>
  )
}
