'use client'

import * as React from 'react'
import { CheckIcon, ClipboardIcon } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/registry/default/ui/ui-button'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/registry/default/ui/ui-tooltip'

export function ChartCopyButton({
  name,
  code,
  className,
  ...props
}: {
  name: string
  code?: string
} & React.ComponentProps<typeof Button>) {
  const [hasCopied, setHasCopied] = React.useState(false)

  React.useEffect(() => {
    setTimeout(() => {
      setHasCopied(false)
    }, 2000)
  }, [hasCopied])

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          size="small"
          color="reset"
          variant="text"
          style={{ fontSize: '12px', color: '#666' }}
          className={cn(
            '[&_svg]-h-3.5 h-7 w-7 rounded-[6px] [&_svg]:w-3.5',
            className,
            'px-1'
          )}
          onClick={async () => {
            if (code) {
              navigator.clipboard.writeText(code)
            } else {
              const res = await fetch(`/r/styles/default/${name}.json`)
              const json = await res.json()
              const code = json?.files?.[0]?.content ?? ''
              navigator.clipboard.writeText(code)
            }
            setHasCopied(true)
          }}
          {...props}
        >
          <span className="sr-only">Copy</span>
          {hasCopied ? <CheckIcon /> : <ClipboardIcon />}
        </Button>
      </TooltipTrigger>
      <TooltipContent className="bg-black text-white">Copy code</TooltipContent>
    </Tooltip>
  )
}
