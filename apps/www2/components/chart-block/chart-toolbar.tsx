import {
  AreaChartIcon,
  BarChartBigIcon,
  HexagonIcon,
  LineChartIcon,
  PieChartIcon,
  RadarIcon,
} from 'lucide-react'

import { cn } from '@/lib/utils'
import { ChartCodeViewer } from '@/components/chart-block/chart-code-viewer'
import { ChartCopyButton } from '@/components/chart-block/chart-copy-button'

export async function ChartToolbar({
  name,
  className,
  children,
}: { name: string } & React.ComponentProps<'div'>) {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      <div className="text-muted-foreground flex items-center gap-1.5 pl-1 text-[13px] [&>svg]:h-[0.9rem] [&>svg]:w-[0.9rem]">
        <ChartTitle name={name} />
      </div>
      <div className="ml-auto flex items-center gap-2 [&>form]:flex">
        <ChartCopyButton name={name} />
        <ChartCodeViewer name={name}>{children}</ChartCodeViewer>
      </div>
    </div>
  )
}

function ChartTitle({ name }: { name: string }) {
  if (name.includes('chart-line')) {
    return (
      <>
        <LineChartIcon /> 折线图
      </>
    )
  }

  if (name.includes('chart-bar')) {
    return (
      <>
        <BarChartBigIcon /> 柱状图
      </>
    )
  }

  if (name.includes('chart-pie')) {
    return (
      <>
        <PieChartIcon /> 饼图
      </>
    )
  }

  if (name.includes('chart-area')) {
    return (
      <>
        <AreaChartIcon /> 面积图
      </>
    )
  }

  if (name.includes('chart-radar')) {
    return (
      <>
        <HexagonIcon /> Radar Chart
      </>
    )
  }

  if (name.includes('chart-radial')) {
    return (
      <>
        <RadarIcon /> Radial Chart
      </>
    )
  }

  return name
}
