import { type HTMLAttributes } from 'react'
import { highlight } from 'fumadocs-core/highlight'
import type { CodeBlockProps } from 'fumadocs-ui/components/codeblock'
import * as Base from 'fumadocs-ui/components/codeblock'

import { cn } from '@/lib/utils'

export async function CodeBlock({
  code,
  language,
  className,
  ...rest
}: HTMLAttributes<HTMLElement> &
  CodeBlockProps & {
    code: string
    language: string
  }) {
  const rendered = await highlight(code, {
    lang: language,
    components: {
      pre: (props) => <Base.Pre {...props} />,
    },
    themes: {
      light: 'github-light',
      dark: 'github-dark',
    },
  })

  return (
    <Base.CodeBlock className={cn('my-0', className)} {...rest}>
      {rendered}
    </Base.CodeBlock>
  )
}
