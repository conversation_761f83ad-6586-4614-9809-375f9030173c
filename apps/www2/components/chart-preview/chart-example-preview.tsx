import React from 'react'
import { BoxIcon, CodeIcon, EyeIcon } from 'lucide-react'

import { cn } from '@/lib/utils'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import ThemeWrapper from '@/components/theme/wrapper'

import componentCache from '../../.generated/component-cache'
import { ChartPreview } from './chart-preview'
import { CodeBlock } from './codeblock'
import { SourceScope } from './types'

type PreviewProps = {
  children?: React.ReactNode
  /**
   * 组件名
   */
  componentName: string
  /**
   * demo名
   */
  demoName: string
  /**
   * 完整路径：./registry/default/[scope]/[src]
   * @default 'default'
   */
  style?: 'default'
  /**
   * @default 'ui'
   */
  scope?: SourceScope
  /**
   * 是否显示源码
   * @default true
   */
  showSource?: 'true' | 'false'
  __DemoCode__?: string
  __SourceComponents__?: string
}

export const ChartExamplePreview = async ({
  componentName,
  demoName,
  style = 'default',
  scope = 'ui',
  showSource = 'true',
  __DemoCode__,
  __SourceComponents__,
}: PreviewProps) => {
  // const { currentTheme, updateThemeVariable, setTheme, resetTheme } =
  //   useThemeManager()

  // console.log({ currentTheme })

  // 暂时为了性能考虑，rehype 那边处理读取，损失了热更新
  // const code = await readFile(
  //   join(
  //     process.cwd(),
  //   './registry/default/examples',
  //   `${componentName}/${demoName}.tsx`
  // ),
  // 'utf-8'
  // )

  return (
    <div
      className={cn(
        'example-preview',
        'not-prose bg-background w-full overflow-hidden rounded-lg border'
      )}
    >
      <Tabs defaultValue="preview" className="size-full gap-0">
        <TabsList className="w-full rounded-none border-b">
          {/* {showSource === 'true' && (
            <TabsTrigger value="source">
              <BoxIcon size={16} className="text-muted-foreground" />
              Source
            </TabsTrigger>
          )}
           */}
          <TabsTrigger value="code">
            <CodeIcon size={16} className="text-muted-foreground" />
            Code
          </TabsTrigger>
          <TabsTrigger value="preview">
            <EyeIcon size={16} className="text-muted-foreground" />
            Preview
          </TabsTrigger>
        </TabsList>
        {/* {showSource === 'true' && (
          <TabsContent value="source" className="size-full bg-background">
            <ExampleSource source={JSON.parse(__SourceComponents__ || '')} />
          </TabsContent>
        )}
        */}
        <TabsContent value="code" className="size-full bg-background">
          <CodeBlock
            language="tsx"
            code={
              componentCache.examples[`${scope}/${componentName}/${demoName}`]
                ?.demoCode || ''
            }
            className="bg-background rounded-none border-0"
          />
        </TabsContent>
        <TabsContent value="preview" className="size-full overflow-hidden">
          <ThemeWrapper>
            <ChartPreview
              className={cn(`border-none [opacity:var(--opacity)]`)}
              componentName={componentName}
              demoName={demoName}
              style={style}
              scope={`examples/${scope}`}
            />
          </ThemeWrapper>
        </TabsContent>
      </Tabs>
    </div>
  )
}
