import dynamic from 'next/dynamic'
import Link from 'next/link'
import { ExternalLink } from 'lucide-react'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/button'

import { SourceScopeWithExamples } from './types'

function getComponentName(name: string) {
  return name
    .replace('charts/', '')
    .split(/[-/]/)
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
    .join('')
}

export interface ComponentLoaderProps {
  className?: string
  /**
   * 组件名
   */
  componentName: string
  /**
   * demo名
   */
  demoName: string
  /**
   * @default 'default'
   */
  style?: 'default'
  /**
   * @default 'examples/ui'
   */
  scope?: SourceScopeWithExamples
  /**
   * @default 'ui'
   */
  type?: 'ui' | 'base'
}

export const ComponentLoader = (props: ComponentLoaderProps) => {
  const {
    style = 'default',
    scope = 'examples/ui',
    componentName,
    demoName,
  } = props

  const Component = dynamic(() =>
    import(`../../registry/${style}/${scope}/${componentName}/${demoName}.tsx`)
      .then((mod) => mod.default || mod[getComponentName(componentName)])
      .catch(() => {
        const NotFoundComponent = () => (
          <p className="text-muted-foreground text-sm">
            Component
            <code className="bg-muted relative rounded px-[0.3rem] py-[0.2rem] font-mono text-sm">
              {componentName}
            </code>
            not found in registry.
          </p>
        )
        NotFoundComponent.displayName = `${componentName}NotFound`
        return NotFoundComponent
      })
  )
  if (Component) {
    return <Component />
  }
  return <p>component not found</p>
}

export const PreviewContainer = ({
  children,
  href,
  className,
}: {
  children: React.ReactNode
  href: string
  className?: string
}) => {
  return (
    <div
      className={cn(
        'size-full h-min-[200px] bg-background group relative flex h-[unset] flex-col items-center justify-center gap-4 overflow-hidden rounded-lg border p-8',
        className
      )}
      style={
        {
          // '--primary': 'oklch(0.205 0 0)',
          // '--primary-foreground': 'oklch(0.985 0 0)',
        } as React.CSSProperties
      }
    >
      <div className="border-border/50 absolute inset-x-0 top-8 -translate-y-px border border-dashed" />
      <div className="border-border/50 absolute inset-x-0 bottom-8 translate-y-px border border-dashed" />
      <div className="border-border/50 absolute inset-y-0 left-8 -translate-x-px border border-dashed" />
      <div className="border-border/50 absolute inset-y-0 right-8 translate-x-px border border-dashed" />
      {children}
      <Link
        href={href}
        target="_blank"
        className={cn(
          buttonVariants({
            color: 'ghost',
          }),
          'cursor-pointer',
          '[&_svg]:!size-[14px] absolute right-2 top-2 !p-2 opacity-0 transition-opacity group-hover:opacity-100'
        )}
      >
        <ExternalLink />
      </Link>
    </div>
  )
}

export const ChartPreview = (props: ComponentLoaderProps) => {
  const {
    className,
    style = 'default',
    scope = 'examples/ui',
    componentName,
    demoName,
  } = props

  return (
    <PreviewContainer
      href={`/preview/${style}/${scope.replace('examples/', '')}/${componentName}/${demoName}`}
      className={className}
    >
      <ComponentLoader
        scope={scope}
        style={style}
        componentName={componentName}
        demoName={demoName}
      />
    </PreviewContainer>
  )
}
