'use client'

import { Theme } from '../../chart-theme/theme'

export function ThemesStyle({
  activeTheme,
  id,
}: {
  activeTheme: Theme
  id: string
}) {
  if (!activeTheme) return null

  return (
    <style>
      {`
        /* 针对特定ID的data-chart-preview及其内部data-chart元素 */
        [data-chart-preview="${id}"] {
          ${Object.entries(activeTheme.cssVars.light)
            .map(([key, value]) => `${key}: ${value};`)
            .join('\n')}
        }
        /* 暗色模式 */
        .dark [data-chart-preview="${id}"] {
          ${Object.entries(activeTheme.cssVars.dark)
            .map(([key, value]) => `${key}: ${value};`)
            .join('\n')}
        }

      `}
    </style>
  )
}
