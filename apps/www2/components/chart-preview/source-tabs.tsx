import { promises as fs } from 'fs'
import path from 'path'
import { Tab, Tabs } from 'fumadocs-ui/components/tabs'

import { CodeBlock } from './codeblock'
import { SourceScope } from './types'

interface SourceTabsProps {
  /**
   * 源代码的作用域
   */
  scope: SourceScope
  /**
   * 源文件或文件夹的路径
   */
  src: string
}

interface SourceFile {
  /**
   * 文件名
   */
  name: string
  /**
   * 文件内容
   */
  content: string
  /**
   * 文件扩展名
   */
  language: string
}

/**
 * 获取源文件内容
 */
async function getSource(
  scope: SourceScope = 'ui',
  src: string
): Promise<SourceFile[]> {
  // 获取完整路径
  const fullPath = path.join(process.cwd(), 'registry/default', scope, src)

  try {
    // 检查路径的状态
    const stats = await fs.stat(fullPath)

    // 如果是文件夹
    if (stats.isDirectory()) {
      // 读取文件夹下的所有文件
      const files = await fs.readdir(fullPath)

      // 读取每个文件的内容
      const sourceFiles = await Promise.all(
        files.map(async (file) => {
          const filePath = path.join(fullPath, file)
          const fileStats = await fs.stat(filePath)

          // 忽略子文件夹
          if (fileStats.isDirectory()) {
            return null
          }

          const content = await fs.readFile(filePath, 'utf-8')
          const extension = path.extname(file).slice(1)

          return {
            name: file,
            content,
            language: extension || 'txt',
          }
        })
      )

      // 过滤掉目录，只保留文件
      return sourceFiles.filter(Boolean) as SourceFile[]
    } else {
      // 如果是单个文件
      const content = await fs.readFile(fullPath, 'utf-8')
      const fileName = path.basename(fullPath)
      const extension = path.extname(fileName).slice(1)

      return [
        {
          name: fileName,
          content,
          language: extension || 'txt',
        },
      ]
    }
  } catch (error) {
    console.error(`Error reading source: ${error}`)
    return [
      {
        name: 'Error',
        content: `Failed to load source: ${error}`,
        language: 'txt',
      },
    ]
  }
}

export const SourceTabs = async ({ scope, src }: SourceTabsProps) => {
  const sourceFiles = await getSource(scope, src)

  // 如果没有找到文件，显示错误信息
  if (sourceFiles.length === 0) {
    return (
      <div className="text-red-500">
        No source files found at {scope}/{src}
      </div>
    )
  }

  return (
    <div>
      <Tabs
        items={sourceFiles.map((file) => file.name)}
        defaultIndex={
          sourceFiles.findIndex((file) => /index\.tsx?$/.test(file.name)) || 0
        }
      >
        {sourceFiles.map((file) => (
          <Tab
            key={file.name}
            value={file.name}
            className="max-h-[200px] overflow-y-auto"
          >
            <CodeBlock
              code={file.content}
              language={file.language}
              className="bg-background"
            />
          </Tab>
        ))}
      </Tabs>
    </div>
  )
}
