import { CodeBlock } from './codeblock'
import { SourceScopeWithExamples } from './types'

export interface SourceProps {
  /**
   * 主题
   * @default 'default'
   */
  style?: 'default'
  /**
   * 完整路径：./registry/default/[scope]/[src]
   * @default 'examples/ui'
   */
  scope?: SourceScopeWithExamples
  /**
   * 完整路径：./registry/default/[scope]/[src]
   */
  src: string
  /**
   * 源码内容，插件自动传入
   */
  __DemoCode__?: string
}

export function Source({ __DemoCode__ = '' }: SourceProps) {
  return <CodeBlock code={__DemoCode__} language="tsx" />
}
