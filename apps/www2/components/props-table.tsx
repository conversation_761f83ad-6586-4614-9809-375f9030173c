'use client'

import React from 'react'

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  Too<PERSON><PERSON><PERSON>rov<PERSON>,
  TooltipTrigger,
} from './ui/tooltip'

interface PropDef {
  name: string
  required?: boolean
  type: string
  typeSimple?: string
  default?: string
  description?: React.ReactNode
}

interface PropsTableProps {
  data: PropDef[]
}

const InfoIcon = () => (
  <svg
    width="15"
    height="15"
    viewBox="0 0 15 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.5 0.875C3.84375 0.875 0.875 3.84375 0.875 7.5C0.875 11.1562 3.84375 14.125 7.5 14.125C11.1562 14.125 14.125 11.1562 14.125 7.5C14.125 3.84375 11.1562 0.875 7.5 0.875ZM8.3125 10.625H6.6875V7.1875H8.3125V10.625ZM8.3125 5.9375H6.6875V4.375H8.3125V5.9375Z"
      fill="currentColor"
    />
  </svg>
)

export const PropsTable: React.FC<PropsTableProps> = ({ data }) => {
  return (
    <TooltipProvider delayDuration={0}>
      <div className="mt-2 overflow-hidden rounded-lg border border-gray-200 shadow-sm">
        <table className="!m-0 w-full border-collapse text-sm">
          <thead>
            <tr className="bg-gray-50">
              <th className="border-b border-gray-200 px-4 py-2 text-left font-semibold">
                Prop
              </th>
              <th className="border-b border-gray-200 px-4 py-2 text-left font-semibold">
                描述
              </th>
              <th className="border-b border-gray-200 px-4 py-2 text-left font-semibold">
                类型
              </th>
              <th className="border-b border-gray-200 px-4 py-2 text-left font-semibold">
                默认值
              </th>
            </tr>
          </thead>
          <tbody className="bg-background">
            {data.map((prop) => (
              <tr key={prop.name} className="border-b">
                <td className="px-4 py-2">
                  <div className="flex items-center">
                    <span className="rounded bg-blue-50 px-2 py-1 font-semibold text-blue-400">
                      {prop.name}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-2">{prop.description}</td>
                <td className="px-4 py-2">
                  <div className="flex items-center">
                    <span className="inline-block rounded bg-gray-100 px-2 py-1 font-mono text-xs text-gray-800">
                      {prop.typeSimple || prop.type}
                    </span>
                    {prop.typeSimple && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="ml-1 inline-flex cursor-help text-gray-400 focus:outline-none">
                            <InfoIcon />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="max-w-[240px] whitespace-pre-wrap font-mono text-xs">
                            {prop.type}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </td>
                <td className="px-4 py-2 text-gray-600">
                  {prop.default || '-'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </TooltipProvider>
  )
}
