'use client'

import React, { useEffect, useMemo, useState } from 'react'

import { cn } from '@/lib/utils'
import { KanbanCard, KanbanGroup } from '@/components/ui/kanban-group'
import { Button } from '@/registry/default/ui/pro-button'
import { Select } from '@/registry/default/ui/pro-select'
import { TextField } from '@/registry/default/ui/pro-textField'
import {
  Alert,
  AlertContent,
  AlertDescription,
  AlertTypeIcon,
} from '@/registry/default/ui/ui-alert'
import { Loading } from '@/registry/default/ui/ui-loading'

// 配置常量 - 可定期修改
const GITLAB_CONFIG = {
  API_URL: 'https://git.imile.com/api/v4',
  ACCESS_TOKEN: 'w5ytYLhQ7f8Bw51UnrTH',
  START_COMMIT_SHA: '96fb57cc4', // 可定期修改的起始 commit
  // START_COMMIT_SHA: 'c863a1041', // 可定期修改的起始 commit
  PROJECT_ID: '621', // 项目 ID，可根据需要修改
}

// 约定式提交类型定义
interface ConventionalCommit {
  id: string
  type: string
  scope: string
  description: string
  author: string
  authorEmail: string
  createdAt: string
  sha: string
  webUrl: string
}

// GitLab API 提交数据结构
interface GitLabCommit {
  id: string
  message: string
  author_name: string
  author_email: string
  created_at: string
  web_url: string
}

// 组件 Props
interface GitLabCommitsProps {
  className?: string
}

// 提交类型配置
const COMMIT_TYPES = [
  { value: 'feat', label: 'feat', color: 'bg-green-500' },
  { value: 'fix', label: 'fix', color: 'bg-red-500' },
  { value: 'docs', label: 'docs', color: 'bg-blue-500' },
  { value: 'style', label: 'style', color: 'bg-purple-500' },
  { value: 'refactor', label: 'refactor', color: 'bg-yellow-500' },
  { value: 'test', label: 'test', color: 'bg-orange-500' },
  { value: 'chore', label: 'chore', color: 'bg-gray-500' },
]

// 看板视图类型
type KanbanViewType = 'type' | 'scope'

// 约定式提交解析正则表达式
// 更宽松的版本，允许可选的空格和多种格式
const CONVENTIONAL_COMMIT_REGEX =
  /^(feat|fix|docs|style|refactor|test|chore)\s*\(\s*([^)]+)\s*\)\s*:\s*(.+)$/i

export function GitLabCommits({ className }: GitLabCommitsProps) {
  const [commits, setCommits] = useState<ConventionalCommit[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedScope, setSelectedScope] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [viewType, setViewType] = useState<KanbanViewType>('type')

  // 解析约定式提交
  const parseConventionalCommit = (
    gitlabCommit: GitLabCommit
  ): ConventionalCommit | null => {
    // 获取提交消息的第一行（因为有些提交消息可能有多行）
    const firstLine = gitlabCommit.message.split('\n')[0].trim()
    const match = firstLine.match(CONVENTIONAL_COMMIT_REGEX)
    if (!match) return null

    const [, type, scope, description] = match
    return {
      id: gitlabCommit.id,
      type: type.toLowerCase(),
      scope,
      description,
      author: gitlabCommit.author_name,
      authorEmail: gitlabCommit.author_email,
      createdAt: gitlabCommit.created_at,
      sha: gitlabCommit.id,
      webUrl: gitlabCommit.web_url,
    }
  }

  // 获取 GitLab 提交记录
  const fetchCommits = async () => {
    setLoading(true)
    setError(null)

    try {
      // 先获取指定 commit 的详情来获取其时间
      const startCommitResponse = await fetch(
        `${GITLAB_CONFIG.API_URL}/projects/${GITLAB_CONFIG.PROJECT_ID}/repository/commits/${GITLAB_CONFIG.START_COMMIT_SHA}`,
        {
          headers: {
            Authorization: `Bearer ${GITLAB_CONFIG.ACCESS_TOKEN}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!startCommitResponse.ok) {
        throw new Error(
          `获取起始提交信息失败: ${startCommitResponse.status} ${startCommitResponse.statusText}`
        )
      }

      const startCommit = await startCommitResponse.json()
      const sinceDate = startCommit.created_at

      // 使用起始提交的时间来查询之后的提交
      const response = await fetch(
        `${GITLAB_CONFIG.API_URL}/projects/${GITLAB_CONFIG.PROJECT_ID}/repository/commits?since=${sinceDate}&per_page=100&order_by=created_at`,
        {
          headers: {
            Authorization: `Bearer ${GITLAB_CONFIG.ACCESS_TOKEN}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        throw new Error(
          `API 请求失败: ${response.status} ${response.statusText}`
        )
      }

      const data: GitLabCommit[] = await response.json()

      // 过滤掉起始提交本身，只要之后的提交
      const filteredData = data.filter(
        (commit) => commit.id !== GITLAB_CONFIG.START_COMMIT_SHA
      )

      // 解析并过滤约定式提交
      const conventionalCommits = filteredData
        .map(parseConventionalCommit)
        .filter((commit): commit is ConventionalCommit => commit !== null)
        // 按时间倒序排序
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )

      setCommits(conventionalCommits)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取提交记录失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取所有可用的 scope
  const availableScopes = useMemo(() => {
    const scopes = [...new Set(commits.map((commit) => commit.scope))]
    return scopes.sort()
  }, [commits])

  // 获取所有可用的类型
  const availableTypes = useMemo(() => {
    const types = [...new Set(commits.map((commit) => commit.type))]
    return types.sort()
  }, [commits])

  // 过滤提交记录
  const filteredCommits = useMemo(() => {
    return commits.filter((commit) => {
      const matchesType = selectedType === 'all' || commit.type === selectedType
      const matchesScope =
        selectedScope === 'all' || commit.scope === selectedScope
      const matchesSearch =
        searchQuery === '' ||
        commit.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        commit.author.toLowerCase().includes(searchQuery.toLowerCase())

      return matchesType && matchesScope && matchesSearch
    })
  }, [commits, selectedType, selectedScope, searchQuery])

  // 按类型分组的提交
  const commitsByType = useMemo(() => {
    const grouped = availableTypes.reduce<Record<string, ConventionalCommit[]>>(
      (acc, type) => {
        acc[type] = filteredCommits
          .filter((commit) => commit.type === type)
          .sort(
            (a, b) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          )
        return acc
      },
      {}
    )
    return grouped
  }, [filteredCommits, availableTypes])

  // 按范围分组的提交
  const commitsByScope = useMemo(() => {
    const grouped = availableScopes.reduce<
      Record<string, ConventionalCommit[]>
    >((acc, scope) => {
      acc[scope] = filteredCommits
        .filter((commit) => commit.scope === scope)
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
      return acc
    }, {})
    return grouped
  }, [filteredCommits, availableScopes])

  // 获取提交类型颜色
  const getTypeColor = (type: string) => {
    return COMMIT_TYPES.find((t) => t.value === type)?.color || 'bg-gray-500'
  }

  // 获取状态颜色
  const getStatusColor = (type: string) => {
    switch (type) {
      case 'feat':
        return 'bg-green-500'
      case 'fix':
        return 'bg-red-500'
      case 'docs':
        return 'bg-blue-500'
      case 'style':
        return 'bg-purple-500'
      case 'refactor':
        return 'bg-yellow-500'
      case 'test':
        return 'bg-orange-500'
      case 'chore':
        return 'bg-gray-500'
      default:
        return 'bg-gray-400'
    }
  }

  // 组件挂载时获取数据
  useEffect(() => {
    fetchCommits()
  }, [])

  return (
    <div className={cn('p-4', className)}>
      {/* 头部标题和刷新按钮 */}
      <div className="mb-4 flex items-start justify-between">
        <div>
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <span>起始提交:</span>
            <code className="inline-flex items-center rounded bg-gray-100 px-1.5 py-0.5 font-mono text-xs text-gray-800">
              {GITLAB_CONFIG.START_COMMIT_SHA}
            </code>
          </div>
        </div>
        <Button
          onClick={fetchCommits}
          loading={loading}
          variant="outlined"
          size="small"
          leftIcon={
            <svg
              className="h-3.5 w-3.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          }
        >
          刷新
        </Button>
      </div>

      {/* 过滤器和视图切换 */}
      <div className="z-99 bg-background sticky top-0 mb-4 rounded-lg border border-gray-200 p-3 shadow-sm">
        <div className="flex flex-wrap items-center gap-3">
          <div className="min-w-34 flex-1">
            <TextField
              placeholder="搜索提交描述或作者..."
              value={searchQuery}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearchQuery(e.target.value)
              }
              prefix={
                <svg
                  className="h-3.5 w-3.5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              }
            />
          </div>
          <div className="flex gap-3">
            <Select
              value={selectedType}
              onChange={(value: string) => setSelectedType(value)}
              options={[
                { value: 'all', label: '全部类型' },
                ...COMMIT_TYPES.map((type) => ({
                  value: type.value,
                  label: type.label,
                })),
              ]}
              placeholder="选择提交类型"
              className="min-w-28"
            />
            <Select
              value={selectedScope}
              onChange={(value: string) => setSelectedScope(value)}
              options={[
                { value: 'all', label: '全部范围' },
                ...availableScopes.map((scope) => ({
                  value: scope,
                  label: scope,
                })),
              ]}
              placeholder="选择范围"
              className="min-w-28"
            />
            <Select
              value={viewType}
              onChange={(value: string) => setViewType(value as KanbanViewType)}
              options={[
                { value: 'type', label: 'kanban by 类型' },
                { value: 'scope', label: 'kanban by 范围' },
              ]}
              placeholder="选择视图"
              className="min-w-36"
            />
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert status="error" className="mb-4">
          <AlertTypeIcon />
          <AlertContent>
            <AlertDescription>{error}</AlertDescription>
          </AlertContent>
        </Alert>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <Loading className="h-6 w-6" />
          <span className="ml-2 text-sm text-gray-600">
            正在加载提交记录...
          </span>
        </div>
      )}

      {/* 提交统计 */}
      {!loading && commits.length > 0 && (
        <div className="mb-4 flex items-center justify-between text-xs text-gray-600">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              总计{' '}
              <span className="font-medium text-gray-900">
                {commits.length}
              </span>{' '}
              个提交
            </span>
            <span className="flex items-center gap-1">
              显示{' '}
              <span className="font-medium text-gray-900">
                {filteredCommits.length}
              </span>{' '}
              个
            </span>
          </div>
          <div className="flex items-center gap-3">
            <span>
              <span className="font-medium text-gray-900">
                {
                  COMMIT_TYPES.filter((type) =>
                    commits.some((c) => c.type === type.value)
                  ).length
                }
              </span>{' '}
              种类型
            </span>
            <span>
              <span className="font-medium text-gray-900">
                {availableScopes.length}
              </span>{' '}
              个范围
            </span>
          </div>
        </div>
      )}

      {/* 看板视图 - Linear 风格 */}
      {!loading && filteredCommits.length > 0 && (
        <div className="overflow-x-auto overscroll-x-contain pb-4">
          <div className="flex min-w-max gap-3">
            {/* 按类型分组 */}
            {viewType === 'type' &&
              Object.entries(commitsByType)
                .filter(([type, typeCommits]) => typeCommits.length > 0)
                .map(([type, typeCommits]) => (
                  <KanbanGroup
                    key={type}
                    title={type}
                    count={typeCommits.length}
                    dotColor={getStatusColor(type)}
                  >
                    {typeCommits.map((commit) => (
                      <KanbanCard
                        key={commit.id}
                        title={commit.description}
                        date={`${new Date(commit.createdAt).toLocaleDateString(
                          'zh-CN',
                          {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric',
                            second: 'numeric',
                          }
                        )}`}
                        badge={{ text: commit.scope }}
                        author={commit.author}
                        webUrl={commit.webUrl}
                      />
                    ))}
                  </KanbanGroup>
                ))}

            {/* 按范围分组 */}
            {viewType === 'scope' &&
              Object.entries(commitsByScope)
                .filter(([_, scopeCommits]) => scopeCommits.length > 0)
                .map(([scope, scopeCommits]) => (
                  <KanbanGroup
                    key={scope}
                    title={scope}
                    count={scopeCommits.length}
                    dotColor="bg-amber-500"
                  >
                    {scopeCommits.map((commit) => (
                      <KanbanCard
                        key={commit.id}
                        title={commit.description}
                        date={`${new Date(commit.createdAt).toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })} - ${new Date(commit.createdAt).toLocaleDateString('zh-CN', { year: 'numeric', month: 'numeric', day: 'numeric' })}`}
                        badge={{
                          text: commit.type,
                          color: getTypeColor(commit.type).concat(
                            ' text-white'
                          ),
                        }}
                        author={commit.author}
                        webUrl={commit.webUrl}
                      />
                    ))}
                  </KanbanGroup>
                ))}
          </div>
        </div>
      )}

      {/* 空状态 */}
      {!loading && commits.length === 0 && (
        <div className="py-8 text-center">
          <svg
            className="mx-auto mb-3 h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="mb-1 text-base font-medium text-gray-900">
            暂无约定式提交记录
          </h3>
          <p className="text-sm text-gray-500">
            在指定的起始提交之后没有找到符合约定式提交格式的记录
          </p>
        </div>
      )}

      {/* 无搜索结果 */}
      {!loading && commits.length > 0 && filteredCommits.length === 0 && (
        <div className="py-8 text-center">
          <svg
            className="mx-auto mb-3 h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <h3 className="mb-1 text-base font-medium text-gray-900">
            未找到匹配的提交
          </h3>
          <p className="text-sm text-gray-500">
            请尝试调整搜索条件或过滤器设置
          </p>
        </div>
      )}
    </div>
  )
}
