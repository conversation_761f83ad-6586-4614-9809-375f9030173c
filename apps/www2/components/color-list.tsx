'use client'

import { useState } from 'react'
import { type VariantProps } from 'class-variance-authority'
import { CheckIcon, ClipboardCopyIcon } from 'lucide-react'

export const ColorList = (props: {
  data: { [key: string]: string }
  className?: string
}) => {
  const { data, className } = props
  const [copied, setCopied] = useState<string | null>(null)

  // Copy color value to clipboard
  const copyToClipboard = (key: string, value: string) => {
    navigator.clipboard.writeText(value)
    setCopied(key)
    setTimeout(() => setCopied(null), 2000)
  }

  return (
    <div
      className={`grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3 ${className || ''}`}
    >
      {Object.keys(data).map((key) => (
        <div
          key={key}
          className="border-border group flex cursor-pointer items-center rounded-md border p-3 transition-all hover:shadow-md"
          style={{ background: 'var(--background)' }}
          onClick={() => copyToClipboard(key, data[key])}
          title="点击复制颜色值"
        >
          <div
            className="mr-3 h-10 w-10 shrink-0 rounded-md shadow-sm transition-transform group-hover:scale-110 group-active:scale-95"
            style={{ backgroundColor: data[key] }}
          />
          <div className="relative flex flex-1 flex-col">
            <div className="text-sm font-medium">{key}</div>
            <div className="text-muted-foreground font-mono text-xs">
              <span className="w-full">{data[key]}</span>
              <span className="absolute right-0 top-1/2 flex h-5 w-5 -translate-y-1/2 items-center justify-center">
                {copied === key ? (
                  <CheckIcon className="animate-in fade-in zoom-in h-3.5 w-3.5 text-green-500" />
                ) : (
                  <ClipboardCopyIcon className="text-muted-foreground/50 h-3.5 w-3.5 opacity-0 transition-opacity group-hover:opacity-100" />
                )}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export type ColorListProps = VariantProps<typeof ColorList>
