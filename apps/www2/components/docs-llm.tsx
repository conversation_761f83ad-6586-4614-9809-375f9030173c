import Link from 'next/link'

import { source } from '@/lib/source'
import { cn } from '@/lib/utils'

import { StopPropagation } from './stop-propagation'

interface DocsTypeTagsProps {
  componentSlug: string[] | undefined
}

export function DocsLLM({ componentSlug }: DocsTypeTagsProps) {
  if (
    !componentSlug ||
    componentSlug.length < 2 ||
    !['base', 'ui', 'pro'].includes(componentSlug?.[0])
  )
    return null

  const slug = [...componentSlug]
  const last = slug.pop()!
  const page = source.getPage([...slug, last + '-llm'])
  const className = cn(
    'inline-flex items-center gap-2 rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors',
    'ring-offset-background border-transparent',
    'bg-secondary text-secondary-foreground'
  )
  const isLLM = page && page.data.title

  return (
    <StopPropagation className={cn('flex gap-2 font-mono text-sm font-normal')}>
      {isLLM && (
        <Link
          href={`/llms.txt/${componentSlug.join('-')}?origin=1`}
          className={className}
        >
          llms.txt(原文档)
        </Link>
      )}
      <Link href={`/llms.txt/${componentSlug.join('-')}`} className={className}>
        llms.txt
      </Link>
      {isLLM && (
        <>
          <Link href={page.url} className={className}>
            llm.mdx
          </Link>
        </>
      )}
    </StopPropagation>
  )
}
