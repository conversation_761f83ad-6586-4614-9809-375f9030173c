// import {  } from '@/registry/registry-base-colors'
import { baseColorsV4 } from '@/scripts/registry-base-colors'
import { atom } from 'jotai'
import { atomWithStorage, createJSONStorage } from 'jotai/utils'

export const defaultThemeVariables = {
  name: 'imileBlue',
  cssVars: baseColorsV4.imileBlue.light as Record<string, string>,
}

type ThemeVariables = typeof defaultThemeVariables

const getLocalStorage = () => {
  if (typeof window !== 'undefined') {
    return localStorage
  }
  return {
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
    length: 0,
    key: () => null,
  }
}
const storage = createJSONStorage<ThemeVariables>(() => getLocalStorage())

export const themeVariablesAtom = atomWithStorage(
  'customTheme',
  defaultThemeVariables,
  storage
)

export const resetThemeToDefaultAtom = atom(null, (get, set) => {
  set(themeVariablesAtom, defaultThemeVariables)
})
