import { useAtom, useSet<PERSON>tom } from 'jotai'

import {
  defaultThemeVariables,
  resetThemeToDefaultAtom,
  themeVariablesAtom,
} from './themeAtoms'

/**
 * 自定义 Hook: useThemeManager
 * 用于管理主题变量的读取、更新和重置。
 * @returns {object} 包含当前主题变量和操作函数的对象
 * - currentTheme: 当前激活的主题变量对象
 * - updateThemeVariable: (key: string, value: string) => void - 更新单个主题变量的函数
 * - setTheme: (newTheme: object) => void - 设置全新的主题变量对象 (会覆盖旧的)
 * - resetTheme: () => void - 将主题重置为默认值的函数
 * - defaultTheme: 默认主题变量对象
 */
export const useThemeManager = () => {
  const [currentTheme, setThemeVariablesInternal] = useAtom(themeVariablesAtom)
  const resetThemeInternal = useSetAtom(resetThemeToDefaultAtom)

  const updateThemeVariable = (key: string, value: string) => {
    setThemeVariablesInternal((prevTheme) => ({
      ...prevTheme,
      cssVars: {
        ...prevTheme.cssVars,
        [key]: value,
      },
    }))
  }

  return {
    currentTheme,
    updateThemeVariable,
    setTheme: setThemeVariablesInternal,
    resetTheme: resetThemeInternal,
    defaultTheme: defaultThemeVariables,
  }
}
