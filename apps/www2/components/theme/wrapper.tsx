'use client'

import React, { useRef } from 'react'

import { useApplyThemeToElement } from '@/components/theme/useApplyThemeToElement'
import { useThemeManager } from '@/components/theme/useThemeManager'

/**
 * 这个组件包裹一个服务器组件（或其他组件），并为其应用动态主题。
 */
const ThemeWrapper: React.FC<{
  children: React.ReactNode
  overridesCssVars?: Record<string, string>
}> = ({ children, overridesCssVars }) => {
  const { currentTheme: globalCustomTheme } = useThemeManager()
  const wrapperRef = useRef(null)

  // 决定最终应用到此实例的主题变量
  // 优先使用实例特定的覆盖，然后是全局自定义主题，最后是 Jotai atom 中的静态默认值（间接通过 globalCustomTheme）
  const activeThemeVariables = overridesCssVars
    ? { ...globalCustomTheme.cssVars, ...overridesCssVars }
    : globalCustomTheme.cssVars

  // 使用自定义 Hook 将 activeThemeVariables 应用到 wrapperRef.current 的 style 上
  useApplyThemeToElement(wrapperRef, activeThemeVariables)

  const [mount, setMount] = React.useState(false)

  React.useLayoutEffect(() => {
    setMount(true)
  }, [])

  return (
    <div
      ref={wrapperRef}
      className="example-preview-container"
      style={{
        // @ts-expect-error
        '--opacity': mount ? 1 : 0,
      }}
      suppressHydrationWarning
    >
      {children}
    </div>
  )
}

export default ThemeWrapper
