import Script from 'next/script'
import { baseColorsV4 } from '@/scripts/registry-base-colors'

/**
 * 刷新页面防样式闪烁。
 * 为了尽可能快的显示 localStorage 中的主题，使用 beforeInteractive 仍然不够早，但比不用会快好多
 * scriptDefaultTheme 最好合 apps/www2/components/theme/themeAtoms.ts一致
 * :root 是为了 和 main 配合，保证弹框内的 dom 的样式在初始化后符合主题
 */
export function ThemeScript() {
  return (
    <Script id="theme-switcher" strategy="beforeInteractive">
      {`
      const scriptDefaultTheme = ${JSON.stringify(baseColorsV4.imileBlue.light)}
      let activeTheme = { ...scriptDefaultTheme };

      try {
        const storedThemeString = localStorage.getItem('customTheme');
        if (storedThemeString) {
          const storedTheme = JSON.parse(storedThemeString);
          activeTheme = { ...scriptDefaultTheme, ...storedTheme.cssVars };
        }
      } catch (error) {
        console.error('应用初始主题时出错 (内联脚本 - 读取 localStorage):', error);
      }


      let cssVariablesString = ':root, .example-preview-container {\\n';
      for (const [key, value] of Object.entries(activeTheme)) {
        if (value !== null && value !== undefined) {
          cssVariablesString += \`  --\${key}: \${value};\\n\`;
        }
      }
      cssVariablesString += '}';

      try {
        const styleElement = document.createElement('style');
        styleElement.id = 'dynamic-preview-theme-variables';
        styleElement.textContent = cssVariablesString;

        if (document.head) {
          document.head.appendChild(styleElement);
          setTimeout(() => {
            document.querySelector('.example-preview-container')?.style.setProperty('--opacity', '1')
          }, 0)
        } else {
          const tempObserver = new MutationObserver(function(mutations, obs) {
            if (document.head) {
              document.head.appendChild(styleElement);
              setTimeout(() => {
                document.querySelector('.example-preview-container')?.style.setProperty('--opacity', '1')
              }, 0)
              obs.disconnect();
            }
          });
          tempObserver.observe(document.documentElement, { childList: true });
        }
      } catch (e) {
        console.error('应用初始主题时出错 (内联脚本 - 注入 style):', e);
      }


      setTimeout(() => {
        //document.querySelector('#dynamic-preview-theme-variables')?.remove()
      }, 3000)

    `}
    </Script>
  )
}
