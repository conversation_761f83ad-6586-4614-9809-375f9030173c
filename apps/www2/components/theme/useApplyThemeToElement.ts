import { useLayoutEffect } from 'react'

/**
 * 将给定的主题变量作为 CSS 自定义属性应用到指定的 DOM 元素上。
 * @param {React.RefObject<HTMLElement>} elementRef - 指向要应用主题的 DOM 元素的 React ref。
 * @param {object} themeVariables - 包含 CSS 变量键值对的主题对象。
 */
export const useApplyThemeToElement = (
  elementRef: React.RefObject<HTMLElement | null>,
  themeVariables: Record<string, string | null | undefined>
) => {
  useLayoutEffect(() => {
    const element = elementRef.current
    if (element && themeVariables) {
      Object.entries(themeVariables).forEach(([key, value]) => {
        const cssVarName = `--${key}`
        if (value !== null && value !== undefined) {
          element.style.setProperty(cssVarName, value)
        } else {
          element.style.removeProperty(cssVarName)
        }
      })
    }
  }, [elementRef, themeVariables])
}
