'use client'

import * as React from 'react'
import { baseColorsV4 } from '@/scripts/registry-base-colors'
import { Repeat } from 'lucide-react'
import { useTheme } from 'next-themes'

import { cn } from '@/lib/utils'
// import { useConfig } from '@/hooks/use-config'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Skeleton } from '@/components/ui/skeleton'
import { Slider } from '@/components/ui/slider'

import { useThemeManager } from './useThemeManager'

export function ThemeSwitcher() {
  const { currentTheme, updateThemeVariable, setTheme, resetTheme } =
    useThemeManager()

  return (
    <div className="flex items-center gap-2">
      <div className="hidden items-center md:flex">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="group/toggle h-8 w-8 px-0"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-palette"
                aria-hidden="true"
              >
                <path d="M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z"></path>
                <circle cx="13.5" cy="6.5" r=".5" fill="currentColor"></circle>
                <circle cx="17.5" cy="10.5" r=".5" fill="currentColor"></circle>
                <circle cx="6.5" cy="12.5" r=".5" fill="currentColor"></circle>
                <circle cx="8.5" cy="7.5" r=".5" fill="currentColor"></circle>
              </svg>
            </Button>
          </PopoverTrigger>
          <PopoverContent
            align="start"
            id="theme-switcher-popover"
            className="z-40 w-[340px] rounded-[12px] bg-white p-6 dark:bg-zinc-950"
          >
            <Customizer />
          </PopoverContent>
        </Popover>
      </div>
      {/* <CopyCodeButton variant="ghost" size="sm" className="[&_svg]:hidden" /> */}
    </div>
  )
}

function Customizer() {
  const [mounted, setMounted] = React.useState(false)
  const { setTheme: setMode, resolvedTheme: mode } = useTheme()
  const { currentTheme, updateThemeVariable, setTheme, resetTheme } =
    useThemeManager()
  // const [config, setConfig] = useConfig()

  React.useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <div>
      <div className="flex items-start pt-4 md:pt-0">
        <div className="space-y-1 pr-2">
          <div className="font-semibold leading-none tracking-tight">
            Theme Customizer
          </div>
          <div className="text-muted-foreground text-xs">
            Customize your components colors.
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="ml-auto rounded-[0.5rem]"
          onClick={() => {
            resetTheme()
            // setConfig({
            //   ...config,
            //   theme: 'imileBlue',
            //   radius: 0.125,
            // })
          }}
        >
          <Repeat />
          <span className="sr-only">Reset</span>
        </Button>
      </div>
      <div className="flex flex-1 flex-col space-y-4 md:space-y-6">
        <div className="space-y-1.5">
          <Label className="text-xs">Color</Label>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(baseColorsV4).map(([name, cssVars]) => {
              if (!['imileBlue', 'zinc', 'red'].includes(name)) return null
              const cssVars2 = cssVars[mode ?? 'light'] as Record<
                string,
                string
              >
              const isActive = currentTheme.name === name
              return mounted ? (
                <Button
                  variant={'outline'}
                  size="sm"
                  key={name}
                  onClick={() => {
                    setTheme({
                      name,
                      cssVars: cssVars2,
                    })
                    Object.entries(cssVars2).forEach(([key, value]) => {
                      document.documentElement.style.setProperty(
                        `--${key}`,
                        value
                      )
                    })
                    // setConfig({
                    //   ...config,
                    //   theme: theme.name,
                    // })
                    // !apps/www/app/layout.tsx 那边是服务端渲染，不方便动态获取 useConfig 的 theme
                    // document.documentElement.dataset.baseColor = theme.name
                  }}
                  className={cn(
                    'justify-start',
                    isActive && 'border-primary ring-2'
                  )}
                  style={
                    {
                      '--theme-primary': cssVars.light.primary,
                    } as React.CSSProperties
                  }
                >
                  <span
                    className={cn(
                      'mr-1 flex h-5 w-5 shrink-0 -translate-x-1 items-center justify-center rounded-full',
                      'bg-[var(--theme-primary)]'
                    )}
                  >
                    {/* {isActive && <Check className="h-4 w-4 text-white" />} */}
                  </span>
                  {name}
                </Button>
              ) : (
                <Skeleton className="h-8 w-full" key={name} />
              )
            })}
          </div>
        </div>
        <div className="space-y-1.5">
          <Label className="text-xs">Radius</Label>
          <div className="flex gap-2">
            <Slider
              min={0}
              max={8}
              step={1}
              value={[+currentTheme.cssVars.radius.replace(/[^\d.]+/, '')]}
              onValueChange={(value) => {
                updateThemeVariable('radius', `${value[0]}px`)
                document.documentElement.style.setProperty(
                  '--radius',
                  `${value[0]}px`
                )
              }}
            />
            <div className="text-xs">{currentTheme.cssVars.radius}</div>
          </div>
        </div>
        {/* <div className="space-y-1.5">
          <Label className="text-xs">Mode</Label>
          <div className="grid grid-cols-3 gap-2">
            {mounted ? (
              <>
                <Button
                  variant={'outline'}
                  size="sm"
                  onClick={() => setMode('light')}
                  className={cn(mode === 'light' && 'border-primary border-2')}
                >
                  <Sun className="mr-1 -translate-x-1" />
                  Light
                </Button>
                <Button
                  variant={'outline'}
                  size="sm"
                  onClick={() => setMode('dark')}
                  className={cn(mode === 'dark' && 'border-primary border-2')}
                >
                  <Moon className="mr-1 -translate-x-1" />
                  Dark
                </Button>
              </>
            ) : (
              <>
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </>
            )}
          </div>
        </div> */}
      </div>
    </div>
  )
}
