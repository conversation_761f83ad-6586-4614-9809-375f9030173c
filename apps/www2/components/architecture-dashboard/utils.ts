export const getLayerTheme = (layer: string) => {
  const themes = {
    UI: 'bg-blue-50 text-blue-700 border-blue-200',
    Pro: 'bg-purple-50 text-purple-700 border-purple-200',
    Base: 'bg-green-50 text-green-700 border-green-200',
    hooks: 'bg-orange-50 text-orange-700 border-orange-200',
    lib: 'bg-gray-50 text-gray-700 border-gray-200',
    Template: 'bg-pink-50 text-pink-700 border-pink-200',
    unknown: 'bg-gray-100 text-gray-600 border-gray-300',
  }
  return themes[layer as keyof typeof themes] || themes.unknown
}

export const getStabilityColor = (instability: number) => {
  if (instability < 0.3) return 'text-green-600'
  if (instability < 0.7) return 'text-yellow-600'
  return 'text-red-600'
}

export const getLayerIcon = (layerName: string) => {
  const icons = {
    UI: '🎨',
    Pro: '⭐',
    Base: '🏗️',
    hooks: '🪝',
    lib: '📚',
    Template: '📋',
  }
  return icons[layerName as keyof typeof icons] || '📦'
}
