import React from 'react'

import { Card, CardContent, CardHeader, CardTitle } from './card'
import type { ArchitectureAsset } from './types'
import { getStabilityColor } from './utils'

interface MetricsOverviewProps {
  assets: ArchitectureAsset[]
}

export const MetricsOverview: React.FC<MetricsOverviewProps> = ({ assets }) => {
  const totalAssets = assets.length
  const avgInstability =
    assets.reduce((sum, asset) => sum + asset.instability, 0) / totalAssets
  const stableAssets = assets.filter((asset) => asset.instability < 0.3).length
  const highCouplingAssets = assets.filter(
    (asset) => asset.ca + asset.ce > 10
  ).length

  const metrics = [
    {
      title: '总组件数',
      value: totalAssets,
      icon: '📦',
      gradient: 'from-blue-500 to-blue-600',
      bgGradient: 'from-blue-50 to-blue-100',
      textColor: 'text-blue-700',
      description: '项目总组件数量',
    },
    {
      title: '平均不稳定性',
      value: `${(avgInstability * 100).toFixed(1)}%`,
      icon: '⚖️',
      gradient: 'from-purple-500 to-purple-600',
      bgGradient: 'from-purple-50 to-purple-100',
      textColor: getStabilityColor(avgInstability),
      description: '组件稳定性指标',
    },
    {
      title: '稳定组件',
      value: stableAssets,
      subtitle: `占比 ${((stableAssets / totalAssets) * 100).toFixed(1)}%`,
      icon: '✅',
      gradient: 'from-green-500 to-green-600',
      bgGradient: 'from-green-50 to-green-100',
      textColor: 'text-green-600',
      description: '不稳定性 < 30% 的组件',
    },
    {
      title: '高耦合组件',
      value: highCouplingAssets,
      subtitle: `占比 ${((highCouplingAssets / totalAssets) * 100).toFixed(1)}%`,
      icon: '🔗',
      gradient: 'from-orange-500 to-orange-600',
      bgGradient: 'from-orange-50 to-orange-100',
      textColor: 'text-orange-600',
      description: '耦合度 > 10 的组件',
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => (
        <Card
          key={metric.title}
          className={`relative overflow-hidden border-0 bg-gradient-to-br shadow-md transition-all duration-300 hover:shadow-lg ${metric.bgGradient}`}
        >
          <CardHeader className="px-3 py-2.5 pb-1">
            <div className="flex items-center justify-between">
              <CardTitle className="!m-0 text-xs font-medium text-gray-600">
                {metric.title}
              </CardTitle>
              <div
                className={`bg-gradient-to-r text-base ${metric.gradient} bg-clip-text text-transparent`}
              >
                {metric.icon}
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-3 py-2 pt-1">
            <div className="space-y-1">
              <div className={`text-2xl font-bold ${metric.textColor}`}>
                {metric.value}
              </div>
              {metric.subtitle && (
                <div className="text-xs font-medium text-gray-500">
                  {metric.subtitle}
                </div>
              )}
              <div className="text-xs text-gray-400">{metric.description}</div>
            </div>
          </CardContent>

          {/* 装饰性元素 */}
          <div
            className={`absolute -right-1 -top-1 h-12 w-12 bg-gradient-to-br ${metric.gradient} opacity-8 rounded-full blur-lg`}
          ></div>
          <div
            className={`absolute -bottom-0.5 -left-0.5 h-6 w-6 bg-gradient-to-tr ${metric.gradient} opacity-15 rounded-full`}
          ></div>
        </Card>
      ))}
    </div>
  )
}
