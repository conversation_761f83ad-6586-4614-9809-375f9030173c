import React from 'react'

import { Badge } from './badge'
import { Card, CardContent, CardHeader } from './card'
import { Progress } from './progress'
import type { LayerSummary } from './types'
import { getLayerIcon, getLayerTheme, getStabilityColor } from './utils'

interface LayerHealthOverviewProps {
  layers: Record<string, LayerSummary>
}

export const LayerHealthOverview: React.FC<LayerHealthOverviewProps> = ({
  layers,
}) => {
  return (
    <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4">
      {Object.entries(layers).map(([layerName, summary]) => {
        if (layerName === 'unknown') return null
        const layerTheme = getLayerTheme(layerName)
        const stabilityColor = getStabilityColor(summary.averageInstability)
        const instabilityValue = summary.averageInstability * 100

        return (
          <Card
            key={layerName}
            className="relative overflow-hidden border border-gray-200 transition-all duration-200 hover:border-gray-300"
          >
            <CardHeader className="px-3 py-2.5 pb-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-base">{getLayerIcon(layerName)}</span>
                  <Badge className={`${layerTheme} px-2 py-0.5 text-xs`}>
                    {layerName}
                  </Badge>
                </div>
                <span className="text-xs font-medium text-gray-500">
                  {summary.assetCount} 个
                </span>
              </div>
            </CardHeader>
            <CardContent className="px-3 py-2 pt-1">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">稳定性</span>
                  <span className={`text-sm font-semibold ${stabilityColor}`}>
                    {instabilityValue === 0
                      ? '0.0%'
                      : `${instabilityValue.toFixed(1)}%`}
                  </span>
                </div>
                <div className="w-full">
                  {instabilityValue > 0 ? (
                    <Progress value={instabilityValue} className="h-1.5" />
                  ) : (
                    <div className="h-1.5 rounded-full bg-green-200" />
                  )}
                </div>
              </div>
            </CardContent>

            {/* 装饰性条纹 */}
            <div
              className={`absolute inset-x-0 top-0 h-0.5${
                layerTheme.includes('blue')
                  ? 'bg-blue-400'
                  : layerTheme.includes('purple')
                    ? 'bg-purple-400'
                    : layerTheme.includes('green')
                      ? 'bg-green-400'
                      : layerTheme.includes('orange')
                        ? 'bg-orange-400'
                        : layerTheme.includes('gray')
                          ? 'bg-gray-400'
                          : 'bg-pink-400'
              }`}
            ></div>
          </Card>
        )
      })}
    </div>
  )
}
