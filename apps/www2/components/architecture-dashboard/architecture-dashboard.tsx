'use client'

import React, { useEffect, useState } from 'react'

import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import {
  HighRiskComponents,
  LayerHealthOverview,
  MetricsOverview,
  getLayerIcon,
  getLayerTheme,
  getStabilityColor,
  type ArchitectureAsset,
  type ArchitectureSummaryData,
  type LayerSummary,
} from './'
import { Badge } from './badge'
import { Card, CardContent, CardHeader, CardTitle } from './card'

export const ArchitectureDashboard: React.FC = () => {
  const [summaryData, setSummaryData] =
    useState<ArchitectureSummaryData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetch('/r/architecture-summary.json')
      .then((res) => {
        if (!res.ok) throw new Error('Failed to fetch')
        return res.json()
      })
      .then((data: ArchitectureSummaryData) => {
        data.assets = data.assets.filter(
          (a) => a.layer !== 'unknown' && a.type !== 'registry:example'
        )
        setSummaryData(data)
        setLoading(false)
      })
      .catch((error) => {
        console.error('Failed to load architecture summary:', error)
        setError('无法加载架构数据')
        setLoading(false)
      })
  }, [])

  if (loading) {
    return (
      <div className="min-h-64 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className="border-primary h-6 w-6 animate-spin rounded-full border-2 border-t-transparent" />
          <span>正在加载架构健康度数据...</span>
        </div>
      </div>
    )
  }

  if (error || !summaryData) {
    return (
      <Card className="border-destructive/50">
        <CardHeader>
          <CardTitle className="text-destructive">加载失败</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            {error || '无法加载架构健康度数据，请检查数据文件是否存在。'}
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue="overview" className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="overview">总览</TabsTrigger>
            <TabsTrigger value="layers">层级分析</TabsTrigger>
            <TabsTrigger value="risks">风险聚焦</TabsTrigger>
          </TabsList>
          <Badge variant="outline" className="text-xs">
            最后更新:{' '}
            {new Date(summaryData.generatedAt).toLocaleString('zh-CN')}
          </Badge>
        </div>
        <TabsContent value="overview" className="space-y-4">
          <MetricsOverview assets={summaryData.assets} />
          <HighRiskComponents assets={summaryData.assets} />
        </TabsContent>

        <TabsContent value="layers" className="space-y-4">
          <LayerHealthOverview layers={summaryData.layers} />

          <Card className="border border-gray-200">
            <CardHeader className="border-b border-gray-100 px-4 py-3">
              <CardTitle className="!m-0 text-lg font-semibold text-gray-800">
                层级详细分析
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-3">
                {Object.entries(summaryData.layers).map(
                  ([layerName, summary]: [string, LayerSummary]) => {
                    if (layerName === 'unknown') return null
                    const layerAssets = summaryData.assets.filter(
                      (asset) => asset.layer === layerName
                    )

                    return (
                      <div
                        key={layerName}
                        className="relative rounded-lg border border-gray-200 bg-gradient-to-r from-gray-50 to-white p-3 transition-all duration-200 hover:shadow-sm"
                      >
                        <div className="mb-3 flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">
                              {getLayerIcon(layerName)}
                            </span>
                            <Badge
                              className={`${getLayerTheme(layerName)} px-3 py-1 text-sm`}
                            >
                              {layerName} 层
                            </Badge>
                          </div>
                          <div className="rounded bg-gray-100 px-2 py-1 text-sm font-medium text-gray-500">
                            {summary.assetCount} 个组件
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-3 text-sm md:grid-cols-4">
                          <div className="rounded border border-gray-100 bg-white p-2">
                            <div className="mb-1 text-xs font-medium text-gray-700">
                              平均不稳定性
                            </div>
                            <div
                              className={`text-lg font-bold ${getStabilityColor(summary.averageInstability)}`}
                            >
                              {(summary.averageInstability * 100).toFixed(1)}%
                            </div>
                          </div>

                          <div className="rounded border border-gray-100 bg-white p-2">
                            <div className="mb-1 text-xs font-medium text-gray-700">
                              类型分布
                            </div>
                            <div className="line-clamp-2 text-xs text-gray-600">
                              {Object.entries(
                                layerAssets.reduce(
                                  (
                                    acc: Record<string, number>,
                                    asset: ArchitectureAsset
                                  ) => {
                                    const typeName =
                                      asset.type?.replace('registry:', '') ||
                                      'N/A'
                                    acc[typeName] = (acc[typeName] || 0) + 1
                                    return acc
                                  },
                                  {} as Record<string, number>
                                )
                              )
                                .map(([type, count]) => `${type}:${count}`)
                                .join(', ')}
                            </div>
                          </div>

                          <div className="rounded border border-gray-100 bg-white p-2">
                            <div className="mb-1 text-xs font-medium text-gray-700">
                              最稳定组件
                            </div>
                            <div className="truncate text-sm font-medium text-green-600">
                              {
                                layerAssets.reduce(
                                  (
                                    min: ArchitectureAsset,
                                    asset: ArchitectureAsset
                                  ) =>
                                    asset.instability < min.instability
                                      ? asset
                                      : min,
                                  layerAssets[0] || {
                                    name: 'N/A',
                                    layer: '',
                                    type: '',
                                    ca: 0,
                                    ce: 0,
                                    instability: 1,
                                  }
                                ).name
                              }
                            </div>
                          </div>

                          <div className="rounded border border-gray-100 bg-white p-2">
                            <div className="mb-1 text-xs font-medium text-gray-700">
                              最不稳定组件
                            </div>
                            <div className="truncate text-sm font-medium text-red-600">
                              {
                                layerAssets.reduce(
                                  (
                                    max: ArchitectureAsset,
                                    asset: ArchitectureAsset
                                  ) =>
                                    asset.instability > max.instability
                                      ? asset
                                      : max,
                                  layerAssets[0] || {
                                    name: 'N/A',
                                    layer: '',
                                    type: '',
                                    ca: 0,
                                    ce: 0,
                                    instability: 0,
                                  }
                                ).name
                              }
                            </div>
                          </div>
                        </div>

                        {/* 装饰性左边框 */}
                        <div
                          className={`absolute inset-y-0 left-0 w-1 rounded-l-lg${
                            getLayerTheme(layerName).includes('blue')
                              ? 'bg-blue-400'
                              : getLayerTheme(layerName).includes('purple')
                                ? 'bg-purple-400'
                                : getLayerTheme(layerName).includes('green')
                                  ? 'bg-green-400'
                                  : getLayerTheme(layerName).includes('orange')
                                    ? 'bg-orange-400'
                                    : getLayerTheme(layerName).includes('gray')
                                      ? 'bg-gray-400'
                                      : 'bg-pink-400'
                          }`}
                        ></div>
                      </div>
                    )
                  }
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risks" className="space-y-4">
          <HighRiskComponents assets={summaryData.assets} />

          <Card className="border border-gray-200">
            <CardHeader className="border-b border-gray-100 px-4 py-3">
              <CardTitle className="!m-0 text-lg font-semibold text-gray-800">
                架构建议
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-3 text-sm">
                <div className="rounded-lg border border-green-200 bg-green-50 p-3">
                  <div className="mb-1 font-medium text-green-800">
                    ✅ 良好实践
                  </div>
                  <div className="text-green-700">
                    Base 层组件保持高稳定性，为整个系统提供了可靠的基础
                  </div>
                </div>

                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3">
                  <div className="mb-1 font-medium text-yellow-800">
                    ⚠️ 需要关注
                  </div>
                  <div className="text-yellow-700">
                    部分 Pro 层组件不稳定性较高，建议优化依赖关系
                  </div>
                </div>

                <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
                  <div className="mb-1 font-medium text-blue-800">💡 建议</div>
                  <div className="text-blue-700">
                    定期监控高依赖组件的变更，确保向后兼容性
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ArchitectureDashboard
