'use client'

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import {
  CartesianGrid,
  ChartTooltip,
  Customized,
  ReferenceArea,
  ReferenceLine,
  ResponsiveContainer,
  <PERSON>atter,
  <PERSON>atter<PERSON>hart,
  XAxis,
  YAxis,
} from '@/registry/default/ui/ui-chart'

import { Badge } from './badge'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import type { ArchitectureAsset } from './types'
import { getLayerTheme } from './utils'

interface HighRiskComponentsProps {
  assets: ArchitectureAsset[]
}

export const HighRiskComponents: React.FC<HighRiskComponentsProps> = ({
  assets,
}) => {
  const [tooltipData, setTooltipData] = useState<{
    mode: 'slice' | 'point' | null
    points: ArchitectureAsset[]
    position: { x: number; y: number; instability: number } | null
  }>({ mode: null, points: [], position: null })

  const animationFrameRef = useRef<number | null>(null)
  const lastMouseEventRef = useRef<any>(null)
  const hideTooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const processedAssets = useMemo(() => {
    return assets.map((asset) => ({
      ...asset,
      instability: Number(asset.instability) || 0,
      ca: Number(asset.ca) || 0,
    }))
  }, [assets])

  const yMax = useMemo(
    () => Math.max(...processedAssets.map((a) => a.ca), 10),
    [processedAssets]
  )

  const getQuadrantInfo = (asset: ArchitectureAsset) => {
    const instabilityThreshold = 0.5
    const caThreshold = 10

    if (asset.instability < instabilityThreshold && asset.ca >= caThreshold) {
      return {
        quadrant: '稳定区',
        color: '#10B981',
        description: '低风险核心组件',
      }
    }
    if (asset.instability >= instabilityThreshold && asset.ca >= caThreshold) {
      return {
        quadrant: '危险区',
        color: '#EF4444',
        description: '高风险核心组件',
      }
    }
    if (asset.instability >= instabilityThreshold && asset.ca < caThreshold) {
      return {
        quadrant: '变更区',
        color: '#F59E0B',
        description: '隔离变更组件',
      }
    }
    return {
      quadrant: '工具区',
      color: '#3B82F6',
      description: '独立工具组件',
    }
  }

  const CustomShape = (props: any) => {
    const { cx, cy, payload } = props
    const color = getQuadrantInfo(payload).color
    const size = 5
    switch (payload.type) {
      case 'registry:hook':
        return (
          <path
            d={`M${cx},${cy - size} L${cx - size},${cy + size} L${
              cx + size
            },${cy + size} Z`}
            fill={color}
            stroke="rgba(255, 255, 255, 0.8)"
            strokeWidth={1}
          />
        )
      case 'registry:lib':
        return (
          <path
            d={`M${cx},${cy - size} L${cx + size},${cy} L${cx},${
              cy + size
            } L${cx - size},${cy} Z`}
            fill={color}
            stroke="rgba(255, 255, 255, 0.8)"
            strokeWidth={1}
          />
        )
      default:
        return (
          <circle
            cx={cx}
            cy={cy}
            r={size}
            fill={color}
            stroke="rgba(255, 255, 255, 0.8)"
            strokeWidth={1}
          />
        )
    }
  }

  const renderQuadrantLabels = () => {
    const positions = {
      stable: { x: '25%', y: '25%' },
      danger: { x: '75%', y: '25%' },
      utility: { x: '25%', y: '75%' },
      change: { x: '75%', y: '75%' },
    }
    return (
      <Customized
        component={() => (
          <g style={{ pointerEvents: 'none' }}>
            <text
              x={positions.stable.x}
              y={positions.stable.y}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="rgba(16, 185, 129, 0.7)"
              fontSize="16"
              fontWeight="600"
            >
              稳定区
            </text>
            <text
              x={positions.danger.x}
              y={positions.danger.y}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="rgba(239, 68, 68, 0.7)"
              fontSize="16"
              fontWeight="600"
            >
              危险区
            </text>
            <text
              x={positions.utility.x}
              y={positions.utility.y}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="rgba(59, 130, 246, 0.7)"
              fontSize="16"
              fontWeight="600"
            >
              工具区
            </text>
            <text
              x={positions.change.x}
              y={positions.change.y}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="rgba(245, 158, 11, 0.7)"
              fontSize="16"
              fontWeight="600"
            >
              变更区
            </text>
          </g>
        )}
      />
    )
  }

  const calculateTooltipData = useCallback(
    (xValue: number, yValue: number, chartX: number, chartY: number) => {
      let nearestPoint: ArchitectureAsset | null = null
      let minDistance = Infinity
      const xRange = 1,
        yRange = yMax

      for (const asset of processedAssets) {
        const dx = (asset.instability - xValue) / xRange
        const dy = (asset.ca - yValue) / yRange
        const distance = Math.sqrt(dx * dx + dy * dy)
        if (distance < minDistance) {
          minDistance = distance
          nearestPoint = asset
        }
      }

      const proximityThreshold = 0.05

      if (nearestPoint && minDistance <= proximityThreshold) {
        return {
          mode: 'point' as const,
          points: [nearestPoint],
          position: { x: chartX, y: chartY, instability: xValue },
        }
      } else {
        const sliceWidth = 0.02
        const pointsInSlice = processedAssets.filter(
          (asset) => Math.abs(asset.instability - xValue) <= sliceWidth / 2
        )
        pointsInSlice.sort((a, b) => b.ca - a.ca)
        return {
          mode: 'slice' as const,
          points: pointsInSlice,
          position: { x: chartX, y: chartY, instability: xValue },
        }
      }
    },
    [processedAssets, yMax]
  )

  const updateLoop = useCallback(() => {
    if (!lastMouseEventRef.current) {
      animationFrameRef.current = null
      return
    }
    const e = lastMouseEventRef.current
    const result = calculateTooltipData(e.xValue, e.yValue, e.chartX, e.chartY)
    setTooltipData(result)
    animationFrameRef.current = requestAnimationFrame(updateLoop)
  }, [calculateTooltipData])

  const handleMouseMove = useCallback(
    (e: any) => {
      if (!e || !e.activeCoordinate) return
      if (hideTooltipTimeoutRef.current)
        clearTimeout(hideTooltipTimeoutRef.current)

      lastMouseEventRef.current = {
        xValue: e.xValue,
        yValue: e.yValue,
        chartX: e.chartX,
        chartY: e.chartY,
      }
      if (!animationFrameRef.current) {
        animationFrameRef.current = requestAnimationFrame(updateLoop)
      }
    },
    [updateLoop]
  )

  const handleMouseLeave = useCallback(() => {
    hideTooltipTimeoutRef.current = setTimeout(() => {
      lastMouseEventRef.current = null
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }
      setTooltipData({ mode: null, points: [], position: null })
    }, 200)
  }, [])

  const handleTooltipMouseEnter = () => {
    if (hideTooltipTimeoutRef.current) {
      clearTimeout(hideTooltipTimeoutRef.current)
    }
  }

  useEffect(() => {
    return () => {
      if (animationFrameRef.current)
        cancelAnimationFrame(animationFrameRef.current)
      if (hideTooltipTimeoutRef.current)
        clearTimeout(hideTooltipTimeoutRef.current)
    }
  }, [])

  return (
    <Card className="border border-gray-200 bg-white shadow-lg">
      <CardHeader className="border-b border-gray-100 p-4">
        <CardTitle className="!m-0 flex items-center justify-between text-lg">
          <div className="flex items-center gap-2">
            <div className="flex h-5 w-5 items-center justify-center rounded-md bg-gradient-to-br from-blue-500 to-purple-600">
              <svg
                className="h-3 w-3 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
            <span className="font-semibold text-gray-900">组件风险象限图</span>
          </div>
          <Badge className="border-0 bg-gradient-to-r from-blue-600 to-purple-600 text-xs text-white">
            四象限分析
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="relative h-[480px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart
              margin={{ top: 20, right: 20, bottom: 60, left: 40 }}
              onMouseMove={handleMouseMove}
              onMouseLeave={handleMouseLeave}
            >
              <CartesianGrid
                strokeDasharray="2 4"
                stroke="rgba(156, 163, 175, 0.3)"
              />
              {renderQuadrantLabels()}
              <ReferenceArea
                x1={0}
                x2={0.5}
                y1={10}
                fill="rgba(16, 185, 129, 0.08)"
              />
              <ReferenceArea
                x1={0.5}
                x2={1}
                y1={10}
                fill="rgba(239, 68, 68, 0.08)"
              />
              <ReferenceArea
                x1={0.5}
                x2={1}
                y2={10}
                fill="rgba(245, 158, 11, 0.08)"
              />
              <ReferenceArea
                x1={0}
                x2={0.5}
                y2={10}
                fill="rgba(59, 130, 246, 0.08)"
              />
              <ReferenceLine
                x={0.5}
                stroke="rgba(107, 114, 128, 0.6)"
                strokeWidth={1.5}
                strokeDasharray="4 4"
              />
              <ReferenceLine
                y={10}
                stroke="rgba(107, 114, 128, 0.6)"
                strokeWidth={1.5}
                strokeDasharray="4 4"
              />
              <XAxis
                type="number"
                dataKey="instability"
                name="不稳定性"
                domain={[0, 1]}
                tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                tick={{ fill: '#6B7280', fontSize: 11 }}
                axisLine={{ stroke: '#D1D5DB' }}
                tickLine={{ stroke: '#D1D5DB' }}
                label={{
                  value: '不稳定性 (越往右越不稳定)',
                  position: 'insideBottom',
                  offset: -25,
                  style: { fill: '#6B7280', fontSize: 12 },
                }}
              />
              <YAxis
                type="number"
                dataKey="ca"
                name="被依赖数"
                tick={{ fill: '#6B7280', fontSize: 11 }}
                axisLine={{ stroke: '#D1D5DB' }}
                tickLine={{ stroke: '#D1D5DB' }}
                label={{
                  value: '被依赖数 (越往上越核心)',
                  angle: -90,
                  position: 'insideLeft',
                  style: {
                    textAnchor: 'middle',
                    fill: '#6B7280',
                    fontSize: 12,
                  },
                }}
              />
              {tooltipData.mode === 'slice' && tooltipData.position && (
                <ReferenceLine
                  x={tooltipData.position.instability}
                  stroke="rgba(107, 114, 128, 0.5)"
                  strokeDasharray="3 3"
                />
              )}
              <ChartTooltip active={false} cursor={false} />
              <Scatter
                name="Components"
                data={processedAssets}
                shape={<CustomShape />}
              />
            </ScatterChart>
          </ResponsiveContainer>

          {tooltipData.mode &&
            tooltipData.position &&
            tooltipData.points.length > 0 && (
              <div
                className="absolute z-50 rounded-xl border border-gray-200 bg-white/80 p-4 text-sm shadow-2xl backdrop-blur-md transition-transform duration-100"
                style={{
                  left: `${tooltipData.position.x}px`,
                  top: `${tooltipData.position.y}px`,
                  transform: 'translate(20px, -50%)',
                }}
                onMouseEnter={handleTooltipMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                {tooltipData.mode === 'point' ? (
                  <div className="min-w-[220px]">
                    <div className="mb-3 flex items-center gap-2">
                      <div
                        className="h-3 w-3 rounded-full shadow-lg"
                        style={{
                          backgroundColor: getQuadrantInfo(
                            tooltipData.points[0]
                          ).color,
                          boxShadow: `0 0 8px ${getQuadrantInfo(tooltipData.points[0]).color}30`,
                        }}
                      ></div>
                      <h3 className="text-base font-semibold text-gray-900">
                        {tooltipData.points[0].name}
                      </h3>
                    </div>
                    <div className="mb-3">
                      <div
                        className="inline-flex items-center rounded-full border px-3 py-1 text-sm font-medium"
                        style={{
                          color: getQuadrantInfo(tooltipData.points[0]).color,
                          borderColor: `${getQuadrantInfo(tooltipData.points[0]).color}40`,
                          backgroundColor: `${getQuadrantInfo(tooltipData.points[0]).color}10`,
                        }}
                      >
                        <span className="mr-1.5">●</span>
                        {getQuadrantInfo(tooltipData.points[0]).quadrant}
                      </div>
                      <p className="mt-1 text-xs text-gray-500">
                        {getQuadrantInfo(tooltipData.points[0]).description}
                      </p>
                    </div>
                    <div className="my-3 h-px w-full bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">不稳定性</span>
                        <span className="font-mono text-xs text-gray-900">
                          {(tooltipData.points[0].instability * 100).toFixed(1)}
                          %
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">被依赖数</span>
                        <span className="font-mono text-xs text-gray-900">
                          {tooltipData.points[0].ca}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">类型</span>
                        <Badge variant="outline" className="text-xs">
                          {tooltipData.points[0].type.replace('registry:', '')}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">层级</span>
                        <Badge
                          className={getLayerTheme(tooltipData.points[0].layer)}
                        >
                          {tooltipData.points[0].layer}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="min-w-[240px]">
                    <div className="font-bold text-gray-800">
                      不稳定性 ≈{' '}
                      {(tooltipData.position.instability * 100).toFixed(0)}%
                    </div>
                    <div className="mb-3 text-xs text-gray-500">
                      附近共有 {tooltipData.points.length} 个组件
                    </div>
                    <div className="max-h-40 space-y-2 overflow-y-auto pr-2">
                      {tooltipData.points.map((point, idx) => (
                        <div
                          key={`${point.name}-${idx}`}
                          className="flex items-center justify-between border-b border-gray-100 text-xs last:border-b-0"
                        >
                          <span
                            className="mr-2 truncate font-medium"
                            style={{ color: getQuadrantInfo(point).color }}
                          >
                            {point.name}
                          </span>
                          <span className="shrink-0 text-xs text-gray-500">
                            被依赖: {point.ca}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
        </div>
      </CardContent>
    </Card>
  )
}
