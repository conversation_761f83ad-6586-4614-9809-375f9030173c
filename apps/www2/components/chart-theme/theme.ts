import { themeColorsToCssVariables } from './utils'

const _THEMES = [
  {
    name: 'chart-theme-lt10',
    id: 'default-chart-theme-lt10',
    colors: {
      'chart-1': 'hsl(229, 100%, 66%)',
      'chart-2': 'hsl(175, 67%, 55%)',
      'chart-3': 'hsl(35, 98%, 65%)',
      'chart-4': 'hsl(280, 100%, 74%)',
      'chart-5': 'hsl(253, 100%, 67%)',
      'chart-6': 'hsl(75, 99%, 41%)',
      'chart-7': 'hsl(57, 100%, 32%)',
      'chart-8': 'hsl(323, 99%, 70%)',
      'chart-9': 'hsl(185, 79%, 36%)',
      'chart-10': 'hsl(175, 67%, 55%)',
    },
    colorsDark: {
      'chart-1': 'hsl(229, 100%, 66%)',
      'chart-2': 'hsl(175, 67%, 55%)',
      'chart-3': 'hsl(35, 98%, 65%)',
      'chart-4': 'hsl(280, 100%, 74%)',
      'chart-5': 'hsl(253, 100%, 67%)',
      'chart-6': 'hsl(75, 99%, 41%)',
      'chart-7': 'hsl(57, 100%, 32%)',
      'chart-8': 'hsl(323, 99%, 70%)',
      'chart-9': 'hsl(185, 79%, 36%)',
      'chart-10': 'hsl(175, 67%, 55%)',
    },
  },
  {
    name: 'chart-theme-lt20',
    id: 'default-chart-theme-lt20',
    colors: {
      'chart-1': 'hsl(229, 100%, 66%)',
      'chart-2': 'hsl(225, 95%, 87%)',
      'chart-3': 'hsl(175, 67%, 55%)',
      'chart-4': 'hsl(170, 65%, 80%)',
      'chart-5': 'hsl(35, 98%, 65%)',
      'chart-6': 'hsl(35, 92%, 80%)',
      'chart-7': 'hsl(280, 100%, 74%)',
      'chart-8': 'hsl(280, 92%, 86%)',
      'chart-9': 'hsl(253, 100%, 67%)',
      'chart-10': 'hsl(253, 92%, 90%)',
      'chart-11': 'hsl(75, 99%, 41%)',
      'chart-12': 'hsl(75, 65%, 75%)',
      'chart-13': 'hsl(57, 100%, 32%)',
      'chart-14': 'hsl(58, 50%, 80%)',
      'chart-15': 'hsl(323, 99%, 70%)',
      'chart-16': 'hsl(323, 100%, 92%)',
      'chart-17': 'hsl(185, 79%, 36%)',
      'chart-18': 'hsl(190, 75%, 80%)',
      'chart-19': 'hsl(111, 89%, 40%)',
      'chart-20': 'hsl(112, 75%, 85%)',
    },
    colorsDark: {
      'chart-1': 'hsl(229, 100%, 66%)',
      'chart-2': 'hsl(225, 95%, 87%)',
      'chart-3': 'hsl(175, 67%, 55%)',
      'chart-4': 'hsl(170, 65%, 80%)',
      'chart-5': 'hsl(35, 98%, 65%)',
      'chart-6': 'hsl(35, 92%, 80%)',
      'chart-7': 'hsl(280, 100%, 74%)',
      'chart-8': 'hsl(280, 92%, 86%)',
      'chart-9': 'hsl(253, 100%, 67%)',
      'chart-10': 'hsl(253, 92%, 90%)',
      'chart-11': 'hsl(75, 99%, 41%)',
      'chart-12': 'hsl(75, 65%, 75%)',
      'chart-13': 'hsl(57, 100%, 32%)',
      'chart-14': 'hsl(58, 50%, 80%)',
      'chart-15': 'hsl(323, 99%, 70%)',
      'chart-16': 'hsl(323, 100%, 92%)',
      'chart-17': 'hsl(185, 79%, 36%)',
      'chart-18': 'hsl(190, 75%, 80%)',
      'chart-19': 'hsl(111, 89%, 40%)',
      'chart-20': 'hsl(112, 75%, 85%)',
    },
  },
  {
    name: 'chart-theme-lt30',
    id: 'default-chart-theme-lt30',
    colors: {
      'chart-1': 'hsl(229, 100%, 66%)',
      'chart-2': 'hsl(225, 95%, 87%)',
      'chart-3': 'hsl(235, 90%, 40%)',
      'chart-4': 'hsl(175, 67%, 55%)',
      'chart-5': 'hsl(170, 65%, 80%)',
      'chart-6': 'hsl(181, 90%, 30%)',
      'chart-7': 'hsl(35, 98%, 65%)',
      'chart-8': 'hsl(35, 92%, 80%)',
      'chart-9': 'hsl(35, 90%, 40%)',
      'chart-10': 'hsl(280, 100%, 74%)',
      'chart-11': 'hsl(280, 92%, 86%)',
      'chart-12': 'hsl(285, 90%, 30%)',
      'chart-13': 'hsl(253, 100%, 67%)',
      'chart-14': 'hsl(253, 92%, 90%)',
      'chart-15': 'hsl(253, 90%, 40%)',
      'chart-16': 'hsl(75, 99%, 41%)',
      'chart-17': 'hsl(75, 65%, 75%)',
      'chart-18': 'hsl(70, 90%, 25%)',
      'chart-19': 'hsl(57, 100%, 32%)',
      'chart-20': 'hsl(58, 50%, 80%)',
      'chart-21': 'hsl(50, 80%, 25%)',
      'chart-22': 'hsl(323, 99%, 70%)',
      'chart-23': 'hsl(323, 100%, 92%)',
      'chart-24': 'hsl(323, 90%, 40%)',
      'chart-25': 'hsl(185, 79%, 36%)',
      'chart-26': 'hsl(190, 75%, 80%)',
      'chart-27': 'hsl(185, 80%, 25%)',
      'chart-28': 'hsl(111, 89%, 40%)',
      'chart-29': 'hsl(112, 75%, 85%)',
      'chart-30': 'hsl(111, 80%, 25%)',
    },
    colorsDark: {
      'chart-1': 'hsl(229, 100%, 66%)',
      'chart-2': 'hsl(225, 95%, 87%)',
      'chart-3': 'hsl(235, 90%, 40%)',
      'chart-4': 'hsl(175, 67%, 55%)',
      'chart-5': 'hsl(170, 65%, 80%)',
      'chart-6': 'hsl(181, 90%, 30%)',
      'chart-7': 'hsl(35, 98%, 65%)',
      'chart-8': 'hsl(35, 92%, 80%)',
      'chart-9': 'hsl(35, 90%, 40%)',
      'chart-10': 'hsl(280, 100%, 74%)',
      'chart-11': 'hsl(280, 92%, 86%)',
      'chart-12': 'hsl(285, 90%, 30%)',
      'chart-13': 'hsl(253, 100%, 67%)',
      'chart-14': 'hsl(253, 92%, 90%)',
      'chart-15': 'hsl(253, 90%, 40%)',
      'chart-16': 'hsl(75, 99%, 41%)',
      'chart-17': 'hsl(75, 65%, 75%)',
      'chart-18': 'hsl(70, 90%, 25%)',
      'chart-19': 'hsl(57, 100%, 32%)',
      'chart-20': 'hsl(58, 50%, 80%)',
      'chart-21': 'hsl(50, 80%, 25%)',
      'chart-22': 'hsl(323, 99%, 70%)',
      'chart-23': 'hsl(323, 100%, 92%)',
      'chart-24': 'hsl(323, 90%, 40%)',
      'chart-25': 'hsl(185, 79%, 36%)',
      'chart-26': 'hsl(190, 75%, 80%)',
      'chart-27': 'hsl(185, 80%, 25%)',
      'chart-28': 'hsl(111, 89%, 40%)',
      'chart-29': 'hsl(112, 75%, 85%)',
      'chart-30': 'hsl(111, 80%, 25%)',
    },
  },
  {
    name: 'chart-theme-order-2',
    id: 'default-chart-theme-order-2',
    colors: {
      'chart-1': 'hsl(225, 94%, 87%)',
      'chart-2': 'hsl(229, 100%, 65%)',
    },
    colorsDark: {
      'chart-1': 'hsl(225, 94%, 87%)',
      'chart-2': 'hsl(229, 100%, 65%)',
    },
  },
  {
    name: 'chart-theme-order-3',
    id: 'default-chart-theme-order-3',
    colors: {
      'chart-1': 'hsl(225, 94%, 87%)',
      'chart-2': 'hsl(227, 100%, 74%)',
      'chart-3': 'hsl(231, 100%, 57%)',
    },
    colorsDark: {
      'chart-1': 'hsl(225, 94%, 87%)',
      'chart-2': 'hsl(227, 100%, 74%)',
      'chart-3': 'hsl(231, 100%, 57%)',
    },
  },
  {
    name: 'chart-theme-order-4',
    id: 'default-chart-theme-order-4',
    colors: {
      'chart-1': 'hsl(223, 100%, 93%)',
      'chart-2': 'hsl(225, 94%, 87%)',
      'chart-3': 'hsl(229, 100%, 65%)',
      'chart-4': 'hsl(231, 100%, 57%)',
    },
    colorsDark: {
      'chart-1': 'hsl(225, 94%, 87%)',
      'chart-2': 'hsl(223, 100%, 93%)',
      'chart-3': 'hsl(229, 100%, 65%)',
      'chart-4': 'hsl(231, 100%, 57%)',
    },
  },
  {
    name: 'chart-theme-order-5',
    id: 'default-chart-theme-order-5',
    colors: {
      'chart-1': 'hsl(223, 100%, 93%)',
      'chart-2': 'hsl(225, 94%, 87%)',
      'chart-3': 'hsl(227, 100%, 74%)',
      'chart-4': 'hsl(229, 100%, 65%)',
      'chart-5': 'hsl(231, 100%, 57%)',
    },
    colorsDark: {
      'chart-1': 'hsl(223, 100%, 93%)',
      'chart-2': 'hsl(225, 94%, 87%)',
      'chart-3': 'hsl(227, 100%, 74%)',
      'chart-4': 'hsl(229, 100%, 65%)',
      'chart-5': 'hsl(231, 100%, 57%)',
    },
  },
] as const

export const THEMES = _THEMES.map((theme) => ({
  ...theme,
  cssVars: {
    light: themeColorsToCssVariables(theme.colors),
    dark: themeColorsToCssVariables(theme.colorsDark),
  },
}))

export type Theme = (typeof THEMES)[number]
