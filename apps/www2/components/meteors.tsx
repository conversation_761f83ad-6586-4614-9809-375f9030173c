'use client'

import { useEffect, useState } from 'react'

import { cn } from '@/lib/utils'

interface MeteorsProps {
  number?: number
  className?: string
}
export const Meteors = ({ number = 20, className }: MeteorsProps) => {
  const [meteorStyles, setMeteorStyles] = useState<Array<React.CSSProperties>>(
    []
  )

  useEffect(() => {
    const styles = Array.from({ length: number }).map(() => ({
      top: -5,
      left: Math.floor(Math.random() * window.innerWidth) + 'px',
      animationDelay: Math.random() * 1 + 0.2 + 's',
      animationDuration: Math.floor(Math.random() * 8 + 2) + 's',
    }))
    setMeteorStyles(styles)
  }, [number])

  return (
    <>
      {meteorStyles.map((style, idx) => (
        <span
          key={idx}
          className={cn(
            'size-0.5 animate-meteor pointer-events-none absolute left-1/2 top-1/2 rotate-[195deg] rounded-full bg-slate-500 opacity-0 shadow-[0_0_0_1px_#ffffff10]',
            className
          )}
          style={style}
        >
          <div className="pointer-events-none absolute top-1/2 -z-10 h-px w-[50px] -translate-y-1/2 -translate-x-full bg-gradient-to-l from-slate-500 to-transparent" />
        </span>
      ))}
    </>
  )
}
