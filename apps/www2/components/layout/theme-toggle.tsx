'use client'

import { useLayoutEffect, useState, type HTMLAttributes } from 'react'
import { baseColorsV4 } from '@/scripts/registry-base-colors'
import { cva } from 'class-variance-authority'
import { useTheme } from 'next-themes'

import { useThemeManager } from '@/components/theme/useThemeManager'

import { cn } from '../../lib/cn'
import { Airplay, Moon, Sun } from '../icons'

const itemVariants = cva(
  'size-6.5 text-fd-muted-foreground rounded-full p-1.5',
  {
    variants: {
      active: {
        true: 'bg-fd-accent text-fd-accent-foreground',
        false: 'text-fd-muted-foreground',
      },
    },
  }
)

const full = [
  ['light', Sun] as const,
  ['dark', Moon] as const,
  ['system', Airplay] as const,
]

export function ThemeToggle({
  className,
  mode = 'light-dark',
  ...props
}: HTMLAttributes<HTMLElement> & {
  mode?: 'light-dark' | 'light-dark-system'
}) {
  const { setTheme: setLightDarkTheme, theme, resolvedTheme } = useTheme()
  const { currentTheme, setTheme } = useThemeManager()
  const [mounted, setMounted] = useState(false)

  useLayoutEffect(() => {
    setMounted(true)
  }, [])

  const container = cn(
    'inline-flex items-center rounded-full border p-1',
    className
  )

  if (mode === 'light-dark') {
    const value = mounted ? resolvedTheme : null

    return (
      <button
        className={container}
        aria-label={`Toggle Theme`}
        data-theme-toggle=""
        onClick={() => {
          const mode = value === 'light' ? 'dark' : 'light'
          const cssVars = baseColorsV4[currentTheme.name][mode] as Record<
            string,
            string
          >
          setLightDarkTheme(mode)
          setTheme((p) => ({ ...p, cssVars }))
          Object.entries(cssVars).forEach(([key, value]) => {
            document.documentElement.style.setProperty(`--${key}`, value)
          })
        }}
        {...props}
      >
        {full.map(([key, Icon]) => {
          if (key === 'system') return

          return (
            <Icon
              key={key}
              fill="currentColor"
              className={cn(itemVariants({ active: value === key }))}
            />
          )
        })}
      </button>
    )
  }

  const value = mounted ? theme : null

  return (
    <div className={container} data-theme-toggle="" {...props}>
      {full.map(([key, Icon]) => (
        <button
          key={key}
          aria-label={key}
          className={cn(itemVariants({ active: value === key }))}
          onClick={() => setLightDarkTheme(key)}
        >
          <Icon className="size-full" fill="currentColor" />
        </button>
      ))}
    </div>
  )
}
