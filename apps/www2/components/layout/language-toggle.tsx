'use client'

import { useState, type ButtonHTMLAttributes, type HTMLAttributes } from 'react'

import { useTranslation } from '@/components/i18n-provider'

import { cn } from '../../lib/cn'
import { buttonVariants } from '../fuma-ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover'

export type LanguageSelectProps = ButtonHTMLAttributes<HTMLButtonElement>

export function LanguageToggle(props: LanguageSelectProps): React.ReactElement {
  const { language, changeLanguage } = useTranslation()
  const [open, setOpen] = useState(false)

  const locales = [
    { locale: 'zh-CN', name: '中文' },
    { locale: 'en-US', name: 'English' },
  ]

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        aria-label="选择语言"
        {...props}
        className={cn(
          buttonVariants({
            color: 'ghost',
            className: 'gap-1.5 p-1.5',
          }),
          props.className
        )}
      >
        {props.children}
      </PopoverTrigger>
      <PopoverContent className="flex w-40 flex-col overflow-hidden p-0">
        <p className="text-fd-muted-foreground mb-1 p-2 text-xs font-medium">
          {language === 'zh-CN' ? '选择语言' : 'Choose Language'}
        </p>
        {locales.map((item) => (
          <button
            key={item.locale}
            type="button"
            className={cn(
              'p-2 text-start text-sm',
              item.locale === language
                ? 'bg-fd-primary/10 text-fd-primary font-medium'
                : 'hover:bg-fd-accent hover:text-fd-accent-foreground text-fd-muted-foreground'
            )}
            onClick={() => {
              changeLanguage?.(item.locale)
              setOpen(false)
            }}
          >
            {item.name}
          </button>
        ))}
      </PopoverContent>
    </Popover>
  )
}

export function LanguageToggleText(
  props: HTMLAttributes<HTMLSpanElement>
): React.ReactElement {
  const { language } = useTranslation()

  const locales = [
    { locale: 'zh-CN', name: '中文' },
    { locale: 'en-US', name: 'English' },
  ]

  const text = locales.find((item) => item.locale === language)?.name || '中文'

  return <span {...props}>{text}</span>
}
