'use client'

import React from 'react'

interface StopPropagationProps {
  children: React.ReactNode
  className?: string
}

/**
 * 阻止冒泡 csc，保持父子还是 rsc
 */
export function StopPropagation({ children, className }: StopPropagationProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  return (
    <div className={className} onClick={handleClick}>
      {children}
    </div>
  )
}
