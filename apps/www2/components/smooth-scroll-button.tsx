'use client'

import { But<PERSON> } from '@/components/ui/button'

interface SmoothScrollButtonProps {
  targetId: string
  children: React.ReactNode
  className?: string
  size?: 'sm' | 'default' | 'lg'
}

export function SmoothScrollButton({
  targetId,
  children,
  className,
  size = 'sm',
}: SmoothScrollButtonProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    const target = document.getElementById(targetId.replace('#', ''))
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      })
    }
  }

  return (
    <Button onClick={handleClick} className={className} size={size}>
      {children}
    </Button>
  )
}
