import Link from 'next/link'

import { source } from '@/lib/source'
import { cn } from '@/lib/utils'

import { StopPropagation } from './stop-propagation'

interface DocsTypeTagsProps {
  componentSlug: string[] | undefined
}

const types = ['base', 'ui', 'pro']

export function DocsTypeTags({ componentSlug }: DocsTypeTagsProps) {
  if (
    !componentSlug ||
    componentSlug.length < 2 ||
    !['base', 'ui', 'pro'].includes(componentSlug?.[0])
  )
    return null

  const otherTypes = types.filter((type) => {
    return (
      type === componentSlug[0] ||
      !!source.getPage([type, ...(componentSlug?.slice(1) || [])])
    )
  })

  return (
    <StopPropagation className="flex gap-2 font-mono text-sm font-normal">
      {otherTypes.map((type) => (
        <Link
          key={type}
          href={`/docs/${type}/${componentSlug[1]}`}
          className={cn(
            'inline-flex items-center gap-2 rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors',
            'ring-offset-background border-transparent',
            'bg-secondary text-secondary-foreground',
            type === componentSlug[0] && 'bg-primary text-primary-foreground'
          )}
        >
          {type}
        </Link>
      ))}
    </StopPropagation>
  )
}
