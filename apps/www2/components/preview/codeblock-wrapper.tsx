'use client'

import * as React from 'react'

import { cn } from '@/lib/utils'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'

interface CodeBlockProps extends React.HTMLAttributes<HTMLDivElement> {
  expandButtonTitle?: string
}

export function CodeBlockWrapper({
  expandButtonTitle = 'View Code',
  className,
  children,
  ...props
}: CodeBlockProps) {
  const [isOpened, setIsOpened] = React.useState(false)

  return (
    <Collapsible open={isOpened} onOpenChange={setIsOpened}>
      <div className={cn('relative overflow-hidden', className)} {...props}>
        <CollapsibleContent
          key={isOpened ? 'expanded' : 'collapsed'}
          forceMount
          className={cn('overflow-hidden', !isOpened && 'max-h-42')}
        >
          <div
            className={cn(
              '[&_figure]:!rounded-none [&_figure]:!border-none',
              '[&_[data-radix-scroll-area-viewport]]:max-h-unset',
              '[&_pre]:pb-[40px]',
              !isOpened ? '[&_pre]:overflow-hidden' : '[&_pre]:overflow-auto]'
            )}
          >
            {children}
          </div>
        </CollapsibleContent>
        <div
          className={cn(
            'dark:from-dark-50/10 dark:to-dark-50/90 from-fd-secondary/50 to-background absolute flex items-end justify-center bg-gradient-to-b p-2',
            isOpened
              ? 'from-fd-secondary to-fd-secondary inset-x-0 bottom-0 h-12'
              : 'inset-0'
          )}
        >
          <CollapsibleTrigger asChild>
            <button
              className={cn(
                'bg-fd-secondary dark:bg-dark-200 dark:text-dark-900 flex w-auto cursor-pointer items-center justify-center rounded-md px-3 py-1 text-sm'
              )}
            >
              {isOpened ? 'Collapse' : expandButtonTitle}
            </button>
          </CollapsibleTrigger>
        </div>
      </div>
    </Collapsible>
  )
}
