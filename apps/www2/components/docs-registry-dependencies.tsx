'use client'

import { useCallback, useEffect, useState } from 'react'

import { cn } from '@/lib/utils'

interface DocsRegistryDependenciesProps {
  componentName: string
}

export function DocsRegistryDependencies({
  componentName,
}: DocsRegistryDependenciesProps) {
  const [registryItem, setRegistryItem] = useState<any>(null)
  const fetchDependencies = useCallback(async () => {
    console.log(componentName, 11111)

    if (!componentName) return
    try {
      const response = await fetch(`/r/${componentName}`).then((res) =>
        res.json()
      )
      const index = await fetch(`/r/index.json`).then((res) => res.json())
      console.log(index, 22222)

      setRegistryItem(response)
    } catch (error) {
      console.error('Error fetching dependencies:', error)
      return null
    }
  }, [componentName])

  useEffect(() => {
    fetchDependencies()
  }, [componentName, fetchDependencies])

  console.log(registryItem, 21)

  return <></>
}
