import React from 'react'
import { ExternalLink } from 'lucide-react'

import { cn } from '@/lib/utils'

interface KanbanGroupProps {
  title: string
  count: number
  dotColor?: string
  children: React.ReactNode
  className?: string
}

export function KanbanGroup({
  title,
  count,
  dotColor = 'bg-amber-500',
  children,
  className,
}: KanbanGroupProps) {
  return (
    <div className={cn('w-64 shrink-0', className)}>
      <div className="z-10 mb-2 flex items-center gap-2 bg-white py-1">
        <div className={cn('h-2.5 w-2.5 rounded-full', dotColor)} />
        <span className="text-sm font-medium text-gray-900">{title}</span>
        <span className="ml-auto rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-500">
          {count}
        </span>
      </div>
      <div className="bg-fd-secondary space-y-2 rounded-lg p-3">{children}</div>
    </div>
  )
}

interface KanbanCardProps {
  title: string
  date: string
  badge: {
    text: string
    color?: string
  }
  author: string
  webUrl?: string
  className?: string
}

export function KanbanCard({
  title,
  date,
  badge,
  author,
  webUrl,
  className,
}: KanbanCardProps) {
  const handleClick = () => {
    if (webUrl) {
      window.open(webUrl, '_blank')
    }
  }

  return (
    <div
      className={cn(
        'group relative rounded-md border border-gray-200 bg-white p-3 shadow-sm',
        webUrl && 'cursor-pointer transition-shadow hover:shadow-md',
        className
      )}
      onClick={handleClick}
    >
      {webUrl && (
        <div className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100">
          <ExternalLink className="h-3 w-3 text-gray-400" />
        </div>
      )}
      <div className="mb-1.5 line-clamp-2 text-sm font-medium text-gray-900">
        {title}
      </div>
      <div className="mb-2 flex items-center justify-between text-xs text-gray-500">
        {date}
      </div>
      <div className="flex items-center justify-between">
        <span
          className={cn(
            'inline-flex items-center rounded px-1.5 py-0.5 text-xs font-medium',
            badge.color || 'bg-gray-100 text-gray-700'
          )}
        >
          {badge.text}
        </span>
        <div className="flex items-center gap-1">
          <div className="text-xs text-gray-500">{author}</div>
          <div className="flex h-6 w-6 items-center justify-center overflow-hidden rounded-full bg-gray-200 text-xs">
            {author.charAt(0).toUpperCase()}
          </div>
        </div>
      </div>
    </div>
  )
}

interface KanbanEmptyStateProps {
  message?: string
}

export function KanbanEmptyState({
  message = '无提交记录',
}: KanbanEmptyStateProps) {
  return (
    <div className="rounded border border-dashed border-gray-300 bg-white py-4 text-center text-xs text-gray-500">
      {message}
    </div>
  )
}
