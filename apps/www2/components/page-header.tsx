import { cn } from '@/lib/utils'

interface PageHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

export function PageHeader({ className, children, ...props }: PageHeaderProps) {
  return (
    <section className="border-grid">
      <div className="container-wrapper">
        <div
          className={cn(
            'container flex flex-col items-center gap-2 py-8 text-center md:py-16 lg:py-20 xl:gap-4',
            className
          )}
          {...props}
        >
          {children}
        </div>
      </div>
    </section>
  )
}

interface PageHeaderHeadingProps
  extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode
}

export function PageHeaderHeading({
  className,
  children,
  ...props
}: PageHeaderHeadingProps) {
  return (
    <h1
      className={cn(
        'text-primary leading-tighter text-balance max-w-2xl text-4xl font-semibold tracking-tight lg:font-semibold lg:leading-[1.1] xl:text-5xl xl:tracking-tighter',
        className
      )}
      {...props}
    >
      {children}
    </h1>
  )
}

interface PageHeaderDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode
}

export function PageHeaderDescription({
  className,
  children,
  ...props
}: PageHeaderDescriptionProps) {
  return (
    <p
      className={cn(
        'text-foreground text-balance max-w-3xl text-base sm:text-lg',
        className
      )}
      {...props}
    >
      {children}
    </p>
  )
}

interface PageActionsProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

export function PageActions({
  className,
  children,
  ...props
}: PageActionsProps) {
  return (
    <div
      className={cn(
        '**:data-[slot=button]:shadow-none flex w-full items-center justify-center gap-2 pt-2',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}
