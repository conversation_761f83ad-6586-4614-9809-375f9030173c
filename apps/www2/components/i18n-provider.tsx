'use client'

import {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { getLanguageCookie, setLanguageCookie } from '@/utils/cookie-helpers'
import { Provider as JotaiProvider, atom, useAtom } from 'jotai'
import { useHydrateAtoms } from 'jotai/utils'

import { getDefaultTranslations } from '@/registry/default/hooks/use-translation'
import type {
  TranslationParams,
  UseTranslationReturn,
} from '@/registry/default/types/i18n'

// 客户端检测
const isClient = typeof window !== 'undefined'

// 创建语言 atom
const languageAtom = atom<string>('zh-CN')

// 持久化语言 atom，只使用 Cookie
const persistentLanguageAtom = atom(
  (get) => get(languageAtom),
  (_, set, newValue: string) => {
    set(languageAtom, newValue)
    if (isClient) {
      // 使用统一的 cookie 工具函数
      setLanguageCookie(newValue)
    }
  }
)

// 创建 i18n 上下文
const I18nContext = createContext<UseTranslationReturn>({
  t: (key: string) => key,
  language: 'zh-CN',
  changeLanguage: () => {},
})

// Provider 组件接口
interface I18nProviderProps {
  children: ReactNode
  initialLanguage: string
}

// Provider 组件
export function I18nProvider({ children, initialLanguage }: I18nProviderProps) {
  return (
    <JotaiProvider>
      <I18nProviderInner initialLanguage={initialLanguage}>
        {children}
      </I18nProviderInner>
    </JotaiProvider>
  )
}

// 内部 Provider 组件，用于处理语言状态
function I18nProviderInner({ children, initialLanguage }: I18nProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)

  // 初始化语言状态
  const initLanguage = useMemo(() => {
    // 服务端和客户端初始化时都使用传入的初始语言，避免水合不匹配
    return initialLanguage
  }, [initialLanguage])

  // 使用传入的初始语言初始化原子状态
  useHydrateAtoms([[languageAtom, initLanguage]])

  const [language, setLanguage] = useAtom(persistentLanguageAtom)

  // 客户端初始化：从 cookie 同步语言状态
  useEffect(() => {
    if (!isClient || isInitialized) return

    try {
      const cookieLanguage = getLanguageCookie()

      if (cookieLanguage && cookieLanguage !== language) {
        // 如果 cookie 中的语言与当前状态不同，更新状态
        setLanguage(cookieLanguage)
      } else if (!cookieLanguage) {
        // 如果没有 cookie，设置默认值
        setLanguageCookie(language)
      }

      setIsInitialized(true)
    } catch (error) {
      console.warn('Failed to initialize language from cookie:', error)
      // 出错时设置默认 cookie
      setLanguageCookie(language)
      setIsInitialized(true)
    }
  }, [language, setLanguage, isInitialized])

  // 创建翻译函数（使用 useMemo 优化性能）
  const translations = useMemo(() => {
    return getDefaultTranslations(language as 'zh-CN' | 'en-US')
  }, [language])

  // 翻译函数（使用 useCallback 优化性能）
  const t = useCallback(
    (key: string, params?: TranslationParams): string => {
      const template = translations[key] || key

      if (!params) return template

      return template.replace(
        /\{\{(\w+)\}\}/g,
        (match: string, paramKey: string) => {
          return params[paramKey] !== undefined
            ? String(params[paramKey])
            : match
        }
      )
    },
    [translations]
  )

  // 语言切换函数（使用 useCallback 优化性能）
  const changeLanguage = useCallback(
    (newLanguage: string) => {
      if (newLanguage !== language) {
        setLanguage(newLanguage)
      }
    },
    [language, setLanguage]
  )

  // 上下文值（使用 useMemo 优化性能）
  const contextValue = useMemo(
    () => ({
      t,
      language,
      changeLanguage,
    }),
    [t, language, changeLanguage]
  )

  return (
    <I18nContext.Provider value={contextValue}>{children}</I18nContext.Provider>
  )
}

// 自定义 hook
export function useTranslation(): UseTranslationReturn {
  const context = useContext(I18nContext)

  if (!context) {
    throw new Error('useTranslation must be used within an I18nProvider')
  }

  return context
}
