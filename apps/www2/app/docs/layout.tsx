import { useMemo, type ReactNode } from 'react'

import { source } from '@/lib/source'
import { DocsLayout } from '@/components/layouts/docs'
import { baseOptions } from '@/app/layout.config'

export default function Layout({ children }: { children: ReactNode }) {
  const pageTree = useMemo(() => {
    // 图表预览页面的菜单
    const chartPage = {
      type: 'page',
      name: 'Chart Blocks',
      description: 'Chart Blocks',
      url: '/charts',
      children: [],
    }

    return {
      ...source.pageTree,
      children: source.pageTree.children.map((item: any) => {
        return {
          ...item,
          children:
            // 拦截一下，如果是chart的话，新增一个预览页面
            item.$id === 'chart'
              ? [...(item.children || []), chartPage]
              : item.children,
        }
      }),
    }
  }, [])

  return (
    <DocsLayout
      i18n
      {...baseOptions}
      tree={pageTree}
      links={[
        {
          url: 'http://git.imile.com/ux/imd.git',
          type: 'icon',
          icon: (
            <svg viewBox="0 0 24 24">
              <path d="m23.6004 9.5927-.0337-.0862L20.3.9814a.851.851 0 0 0-.3362-.405.8748.8748 0 0 0-.9997.0539.8748.8748 0 0 0-.29.4399l-2.2055 6.748H7.5375l-2.2057-6.748a.8573.8573 0 0 0-.29-.4412.8748.8748 0 0 0-.9997-.0537.8585.8585 0 0 0-.3362.4049L.4332 9.5015l-.0325.0862a6.0657 6.0657 0 0 0 2.0119 7.0105l.0113.0087.03.0213 4.976 3.7264 2.462 1.8633 1.4995 1.1321a1.0085 1.0085 0 0 0 1.2197 0l1.4995-1.1321 2.4619-1.8633 5.006-3.7489.0125-.01a6.0682 6.0682 0 0 0 2.0094-7.003z" />
            </svg>
          ),
          text: 'Gitlub',
        },
      ]}
    >
      {children}
    </DocsLayout>
  )
}
