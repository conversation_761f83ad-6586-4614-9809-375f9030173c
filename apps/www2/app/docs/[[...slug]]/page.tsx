import Link from 'next/link'
import { notFound } from 'next/navigation'
import { getMDXComponents } from '@/mdx-components'
import { createRelativeLink } from 'fumadocs-ui/mdx'
import {
  DocsBody,
  DocsDescription,
  DocsPage,
  DocsTitle,
} from 'fumadocs-ui/page'

import { source } from '@/lib/source'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { DocsLLM } from '@/components/docs-llm'
import { DocsRegistryDependencies } from '@/components/docs-registry-dependencies'
import { DocsTypeTags } from '@/components/docs-type-tags'

function getOriginalDocSlug(slug: string[]) {
  const array = [...slug]
  const last = array.pop()!
  return [...array, last.replace('-llm', '')]
}

export default async function Page(props: {
  params: Promise<{ slug?: string[] }>
}) {
  const params = await props.params
  const slug = params.slug
  const page = source.getPage(slug)

  if (!page || !slug) notFound()

  let Mdx, toc
  if (process.env.ASYNC_MODE === 'true') {
    // @ts-expect-error
    const res = await page.data.load()
    Mdx = res.body
    toc = res.toc
  } else {
    Mdx = page.data.body
    toc = page.data.toc
  }

  const isLLMDoc = slug?.[slug.length - 1]?.endsWith('-llm')

  return (
    <DocsPage
      toc={toc}
      full={page.data.full}
      tableOfContent={{
        style: 'clerk',
        single: false,
      }}
      article={{
        className: 'max-sm:pb-16',
      }}
    >
      {isLLMDoc && (
        <>
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink
                  href={`/docs/${getOriginalDocSlug(slug).join('/')}`}
                >
                  {source.getPage(getOriginalDocSlug(slug))?.data.title}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>LLM doc</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </>
      )}
      <DocsTitle>{page.data.title}</DocsTitle>
      <div className="mb-4 flex items-center justify-between gap-2">
        <DocsTypeTags componentSlug={slug} />
        <DocsLLM componentSlug={slug} />
        {/* <DocsRegistryDependencies componentName={slug?.join('-') || ''} /> */}
      </div>
      <DocsDescription>
        {isLLMDoc
          ? '此文档为 LLM 生成，用于帮助 LLM 理解组件的核心功能、典型用法和完整 API。文档经过精简优化，移除了非必要的演示代码，保留最具代表性的示例。'
          : page.data.description}
      </DocsDescription>
      <DocsBody>
        <Mdx
          components={getMDXComponents({
            // this allows you to link to other pages with relative file paths
            a: createRelativeLink(source, page),
          })}
        />
      </DocsBody>
    </DocsPage>
  )
}

export async function generateStaticParams() {
  return source.generateParams()
}

export async function generateMetadata(props: {
  params: Promise<{ slug?: string[] }>
}) {
  const params = await props.params
  const page = source.getPage(params.slug)
  if (!page) notFound()

  return {
    title: page.data.title,
    description: page.data.description,
  }
}
