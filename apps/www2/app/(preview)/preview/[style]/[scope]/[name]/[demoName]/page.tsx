import {
  ComponentLoader,
  type ComponentLoaderProps,
} from '@/components/preview'
import { SourceScope } from '@/components/preview/types'

/**
 * 路由示例：
 * - /preview/examples/default/ui-hello-world/default-demo
 * - /preview/examples/default/ui/hello-world/default-demo
 */
export default async function HomePage({
  params,
}: {
  params: Promise<{
    scope: SourceScope
    style: ComponentLoaderProps['style']
    name: string
    demoName: string
  }>
}) {
  const { style = 'default', scope = 'ui', name, demoName } = await params
  return (
    <ComponentLoader
      scope={`examples/${scope}`}
      style={style}
      componentName={name}
      demoName={demoName}
    />
  )
}
