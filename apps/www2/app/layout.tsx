import { cookies } from 'next/headers'
import { TooltipProvider } from '@imd/base-tooltip'

import { I18nProvider } from '@/components/i18n-provider'
import { ThemeScript } from '@/components/theme/script'

import { Provider } from './provider'
import './global.css'

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // 从 cookie 中读取语言设置
  const cookieStore = await cookies()
  const initialLanguage = cookieStore.get('i18next-language')?.value || 'zh-CN'

  return (
    <html lang={initialLanguage} suppressHydrationWarning>
      <head>
        <ThemeScript />
      </head>
      <body className="flex min-h-screen flex-col">
        <Provider>
          <I18nProvider initialLanguage={initialLanguage}>
            <TooltipProvider delayDuration={0}>{children}</TooltipProvider>
          </I18nProvider>
        </Provider>
      </body>
    </html>
  )
}
