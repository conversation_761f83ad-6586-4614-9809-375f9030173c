import { notFound } from 'next/navigation'
import { NextResponse, type NextRequest } from 'next/server'
import { Page } from 'fumadocs-core/source'

import { getLLMText, llmProcessor } from '@/lib/llm'
import { source } from '@/lib/source'

export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { searchParams } = new URL(_req.url)
  const originParam = searchParams.get('origin')
  const { slug } = await params
  let page: Page | undefined
  // ui-hello-word => ['ui', 'hello-word']
  // chart-chart-area => ['chart', 'chart-area']
  const slugs =
    slug === 'ui-chart' // 特殊处理，因为 chart 的文档放在新开的文件夹，而 registryDependencies 那边他叫 ui-chart
      ? ['chart']
      : /^base|ui|pro|chart-/.test(slug)
        ? slug.split(/-(.*)/).filter(Boolean)
        : [slug]

  // 显示原文档
  if (originParam) {
    page = source.getPage(slugs)
  } else {
    const slugsCopy = [...slugs]
    const last = slugsCopy.pop()!
    let llmPage = source.getPage([...slugsCopy, last + '-llm'])
    page = llmPage || source.getPage(slugs)
  }
  if (!page) notFound()

  // @ts-ignore
  const res = await getLLMText(page, () => llmProcessor)

  return new NextResponse(
    `# ${res.title || ''}
URL: ${res.url}
description: ${res.description || ''}

${res.content}`
  )
}

export function generateStaticParams() {
  return source
    .getPages()
    .filter(
      (page) =>
        page.slugs.length === 2 && ['base', 'ui', 'pro'].includes(page.slugs[0])
    )
    .map((page) => ({ slug: page.slugs.join('-') }))
}

export const revalidate = 3600
