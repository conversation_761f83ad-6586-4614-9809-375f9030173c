import { groupBy } from 'lodash-es'

import { source } from '@/lib/source'

export async function GET() {
  const pages = source.getPages()

  const groupedPages = groupBy(pages, 'file.dirname')

  const res = Object.entries(groupedPages).map(([dirname, pages]) => {
    return `# ${dirname}

${pages.map((page) => `[${page.data.title} - ${page.data.description}](${page.url})`).join('\n\n')}`
  })

  return new Response(res.join('\n\n'))
}

export const revalidate = 3600
