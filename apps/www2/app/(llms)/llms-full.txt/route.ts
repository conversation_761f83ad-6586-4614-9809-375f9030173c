import { NextRequest } from 'next/server'

import { getLLMText, llmProcessor } from '@/lib/llm'
import rehypeRenderExamplePreviewCodeForLlm from '@/lib/rehype-mdx'
import { source } from '@/lib/source'

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url)
  const componentsParam = searchParams.get('components')
  const requestedComponents = componentsParam ? componentsParam.split(',') : []
  let pages = source.getPages()
  const pageSlugs = pages.map((page) => page.slugs.join('-'))

  if (requestedComponents.length > 0) {
    pages = pages.filter((page) =>
      requestedComponents.some(
        (component) => page.slugs.join('-') === component
      )
    )
    // 特殊处理，因为 chart 的文档放在新开的文件夹，而 registryDependencies 那边他叫 ui-chart
    if (requestedComponents.includes('ui-chart')) {
      const chartPage = source.getPage(['chart'])
      if (chartPage) {
        pages = pages.concat(chartPage)
      }
    }
  }

  const scan = pages.map((page) => {
    const slugs = [...page.slugs]

    const last = slugs.pop()!
    let pageLLM = source.getPage([...slugs, last + '-llm'])

    // @ts-ignore
    return getLLMText(pageLLM || page, () => llmProcessor)
  })
  const scanned = await Promise.all(scan)

  return new Response(
    scanned.length > 0
      ? scanned
          .map(
            (item) =>
              `# ${item.title || ''}
URL: ${item.url}

${item.description || ''}

${item.content}`
          )
          .join('\n\n')
      : `查询不到相关文档，请检查组件名称是否正确
requestedComponents: ${requestedComponents.join(',')}
pages: ${pages.map((page) => page.slugs.join('-')).join(',')}
pageSlugs: ${pageSlugs.join(',')}
      `
  )
}

export const revalidate = 3600
