import { readFile } from 'fs/promises'
import { notFound } from 'next/navigation'
import { NextResponse, type NextRequest } from 'next/server'

import { getLLMText } from '@/lib/llm'
import rehypeInjectDemoCode from '@/lib/rehype-mdx'
import { source } from '@/lib/source'

export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ slug: string[] }> }
) {
  const { slug } = await params

  const page = source.getPage(slug.flatMap((s) => s.split('-')))
  if (!page) notFound()

  const res = await getLLMText(page)

  const content =
    process.env.ASYNC_MODE === 'true'
      ? // @ts-expect-error
        await page.data.content
      : await readFile(page.data._file.absolutePath, 'utf-8')

  return new NextResponse(
    `# ${res.title || ''}
URL: ${res.url}
description: ${res.description || ''}

${content}`
  )
}

export function generateStaticParams() {
  return source.generateParams()
}

export const revalidate = 60
