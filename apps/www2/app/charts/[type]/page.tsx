import { notFound } from 'next/navigation'

import { ChartDisplay } from '@/components/chart-block/chart-display'
import * as Charts from '@/app/charts/charts'

// 定义可用的图表类型和对应的组件
const chartTypes = {
  area: [
    { name: 'chart-area-base', component: Charts.ChartAreaBase },
    { name: 'chart-area-brush', component: Charts.ChartAreaBrush },
    { name: 'chart-area-cross', component: Charts.ChartAreaCross },
    { name: 'chart-area-legend', component: Charts.ChartAreaLegend },
    { name: 'chart-area-linear', component: Charts.ChartAreaLinear },
    {
      name: 'chart-area-referenceLine',
      component: Charts.ChartAreaReferenceLine,
    },
    {
      name: 'chart-area-tooltip-direction',
      component: Charts.ChartAreaTooltipDirection,
    },
    { name: 'chart-area-xaxis-angle', component: Charts.ChartAreaXAxisAngle },
    { name: 'chart-area-yaxis', component: Charts.ChartAreaYAxis },
    { name: 'chart-area-cross-x', component: Charts.ChartAreaCrossX },
  ],
  bar: [
    { name: 'chart-bar-base', component: Charts.ChartBarBase },
    { name: 'chart-bar-auto-width', component: Charts.ChartBarAutoWidth },
    { name: 'chart-bar-interval', component: Charts.ChartBarInterval },
    { name: 'chart-bar-label', component: Charts.ChartBarLabel },
    { name: 'chart-bar-multiple', component: Charts.ChartBarMultiple },
    { name: 'chart-bar-multiple2', component: Charts.ChartBarMultiple2 },
    {
      name: 'chart-bar-stacked-bidirectional',
      component: Charts.ChartBarStackedBidirectional,
    },
    { name: 'chart-bar-stacked-rate', component: Charts.ChartBarStackedRate },
    { name: 'chart-bar-stacked', component: Charts.ChartBarStacked },
    {
      name: 'chart-bar-vertical-interval',
      component: Charts.ChartBarVerticalInterval,
    },
    {
      name: 'chart-bar-vertical-label',
      component: Charts.ChartBarVerticalLabel,
    },
    {
      name: 'chart-bar-vertical-multiple-label',
      component: Charts.ChartBarVerticalMultipleLabel,
    },
    {
      name: 'chart-bar-vertical-multiple',
      component: Charts.ChartBarVerticalMultiple,
    },
    {
      name: 'chart-bar-vertical-stack-rate',
      component: Charts.ChartBarVerticalStackRate,
    },
    {
      name: 'chart-bar-vertical-stack',
      component: Charts.ChartBarVerticalStack,
    },
    {
      name: 'chart-bar-vertical-stacked-bidirectional',
      component: Charts.ChartBarVerticalStackBidirectional,
    },
    { name: 'chart-bar-vertical', component: Charts.ChartBarVertical },
    { name: 'chart-bar-yaxis', component: Charts.ChartBarYAxis },
  ],
  line: [
    { name: 'chart-line-base', component: Charts.ChartLineBase },
    { name: 'chart-line-brush-color', component: Charts.ChartLineBrushColor },
    { name: 'chart-line-brush', component: Charts.ChartLineBrush },
    { name: 'chart-line-cross', component: Charts.ChartLineCross },
    { name: 'chart-line-legend', component: Charts.ChartLineLegend },
    {
      name: 'chart-line-legend-custom',
      component: Charts.ChartLineLegendCustom,
    },
    { name: 'chart-line-linear', component: Charts.ChartLineLinear },
    {
      name: 'chart-line-referenceLine',
      component: Charts.ChartLineReferenceLine,
    },
    {
      name: 'chart-line-tooltip-direction',
      component: Charts.ChartLineTooltipDirection,
    },
    { name: 'chart-line-xaxis-angle', component: Charts.ChartLineXAxisAngle },
    {
      name: 'chart-line-yaxis-type-category',
      component: Charts.ChartLineYAxisTypeCategory,
    },
    { name: 'chart-line-yaxis', component: Charts.ChartLineYAxis },
    { name: 'chart-line-cross-x', component: Charts.ChartLineCrossX },
  ],
  pie: [
    { name: 'chart-pie-base', component: Charts.ChartPieBase },
    { name: 'chart-pie-donut', component: Charts.ChartPieDonut },
    { name: 'chart-pie-donut-active', component: Charts.ChartPieDonutActive },
    { name: 'chart-pie-donut-text', component: Charts.ChartPieDonutText },
    { name: 'chart-pie-interactive', component: Charts.ChartPieInteractive },
    { name: 'chart-pie-label', component: Charts.ChartPieLabel },
    { name: 'chart-pie-label-custom', component: Charts.ChartPieLabelCustom },
    { name: 'chart-pie-label-list', component: Charts.ChartPieLabelList },
    { name: 'chart-pie-legend', component: Charts.ChartPieLegend },
    {
      name: 'chart-pie-separator-none',
      component: Charts.ChartPieSeparatorNone,
    },
    { name: 'chart-pie-stacked', component: Charts.ChartPieStacked },
  ],
} as const

type ChartType = keyof typeof chartTypes

// 类型标题映射
const typeLabels: Record<ChartType, string> = {
  area: 'Area Charts',
  bar: 'Bar Charts',
  line: 'Line Charts',
  pie: 'Pie Charts',
}

export async function generateStaticParams() {
  return Object.keys(chartTypes).map((type) => ({
    type,
  }))
}

interface ChartTypePageProps {
  params: Promise<{
    type: string
  }>
}

export default async function ChartTypePage({ params }: ChartTypePageProps) {
  const { type } = await params

  // 检查类型是否有效
  if (!chartTypes[type as ChartType]) {
    notFound()
  }

  const charts = chartTypes[type as ChartType]
  const typeLabel = typeLabels[type as ChartType]

  return (
    <div className="grid gap-4">
      <div className="gap-6 md:flex md:flex-row-reverse md:items-start">
        <div className="grid flex-1 gap-12">
          <h2 className="sr-only">{typeLabel}</h2>
          <div
            id={`${type}-chart`}
            className="grid flex-1 scroll-mt-20 items-start gap-10 md:grid-cols-2 md:gap-6 lg:grid-cols-3 xl:gap-10"
          >
            {charts.map(({ name, component: Component }) => (
              <ChartDisplay key={name} name={name}>
                <Component />
              </ChartDisplay>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
