import Link from 'next/link'
import { ChevronRight } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { ChartDisplay } from '@/components/chart-block/chart-display'
import * as Charts from '@/app/charts/charts'

export default function ChartsPage() {
  return (
    <div className="grid gap-12">
      {/* Featured Charts Section */}
      <div className="gap-6 md:flex md:flex-row-reverse md:items-start">
        <div className="grid flex-1 gap-12">
          <div className="space-y-4">
            <h2 className="text-2xl font-bold tracking-tight">
              Featured Charts
            </h2>
            <p className="text-muted-foreground">
              Popular and commonly used chart examples to get you started.
            </p>
          </div>

          <div
            id="featured"
            className="grid flex-1 scroll-mt-20 items-start gap-10 md:grid-cols-2 md:gap-6 lg:grid-cols-3 xl:gap-10"
          >
            <ChartDisplay name="chart-area-base">
              <Charts.ChartAreaBase />
            </ChartDisplay>
            <ChartDisplay name="chart-bar-multiple">
              <Charts.ChartBarMultiple />
            </ChartDisplay>
            <ChartDisplay
              name="chart-pie-donut-text"
              className="[&_[data-chart]]:xl:max-h-[243px]"
            >
              <Charts.ChartPieDonutText />
            </ChartDisplay>
          </div>
        </div>
      </div>

      <Separator />

      {/* Chart Categories */}
      <div className="space-y-8">
        <div className="space-y-4">
          <h2 className="text-2xl font-bold tracking-tight">
            Chart Categories
          </h2>
          <p className="text-muted-foreground">
            Browse charts by category to find the perfect visualization for your
            data.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {/* Area Charts */}
          <div className="group relative overflow-hidden rounded-lg border p-6 transition-shadow hover:shadow-md">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Area Charts</h3>
                <p className="text-muted-foreground text-sm">
                  Perfect for showing data trends over time with filled areas.
                </p>
              </div>
              <div className="space-y-2">
                <ChartDisplay name="chart-area-base" className="h-32">
                  <Charts.ChartAreaBase />
                </ChartDisplay>
              </div>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link href="/charts/area">
                  View All Area Charts
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Bar Charts */}
          <div className="group relative overflow-hidden rounded-lg border p-6 transition-shadow hover:shadow-md">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Bar Charts</h3>
                <p className="text-muted-foreground text-sm">
                  Great for comparing categories and showing data distribution.
                </p>
              </div>
              <div className="space-y-2">
                <ChartDisplay name="chart-bar-multiple" className="h-32">
                  <Charts.ChartBarMultiple />
                </ChartDisplay>
              </div>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link href="/charts/bar">
                  View All Bar Charts
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Line Charts */}
          <div className="group relative overflow-hidden rounded-lg border p-6 transition-shadow hover:shadow-md">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Line Charts</h3>
                <p className="text-muted-foreground text-sm">
                  Ideal for displaying continuous data and trend analysis.
                </p>
              </div>
              <div className="space-y-2">
                <ChartDisplay name="chart-line-base" className="h-32">
                  <Charts.ChartLineBase />
                </ChartDisplay>
              </div>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link href="/charts/line">
                  View All Line Charts
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Pie Charts */}
          <div className="group relative overflow-hidden rounded-lg border p-6 transition-shadow hover:shadow-md">
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Pie Charts</h3>
                <p className="text-muted-foreground text-sm">
                  Perfect for showing proportions and parts of a whole.
                </p>
              </div>
              <div className="space-y-2">
                <ChartDisplay name="chart-pie-donut" className="h-32">
                  <Charts.ChartPieDonut />
                </ChartDisplay>
              </div>
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link href="/charts/pie">
                  View All Pie Charts
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
