import { Metadata } from 'next'
import Link from 'next/link'

import { But<PERSON> } from '@/components/ui/button'
import { ChartsNav } from '@/components/chart-block/charts-nav'
import { ThemesSwitcher } from '@/components/chart-block/theme/theme-switcher'
import {
  PageActions,
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/components/page-header'
import { SmoothScrollButton } from '@/components/smooth-scroll-button'

const title = 'Beautiful Charts & Graphs'
const description =
  'A collection of ready-to-use chart components built with Recharts. From basic charts to rich data displays, copy and paste into your apps.'

export const metadata: Metadata = {
  title,
  description,
  openGraph: {
    images: [
      {
        url: `/og?title=${encodeURIComponent(
          title
        )}&description=${encodeURIComponent(description)}`,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    images: [
      {
        url: `/og?title=${encodeURIComponent(
          title
        )}&description=${encodeURIComponent(description)}`,
      },
    ],
  },
}

export default function ChartsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <main className="flex flex-1 flex-col">
      <PageHeader>
        <PageHeaderHeading>{title}</PageHeaderHeading>
        <PageHeaderDescription>{description}</PageHeaderDescription>
        <PageActions>
          <SmoothScrollButton
            targetId="charts"
            className="has-[>svg]:px-2.5 h-8 gap-1.5 rounded-md px-3"
            size="sm"
          >
            Browse Charts
          </SmoothScrollButton>
          <Button
            asChild
            variant="outline"
            size="sm"
            className="has-[>svg]:px-2.5 h-8 gap-1.5 rounded-md px-3"
          >
            <Link href="/docs/chart">Documentation</Link>
          </Button>
        </PageActions>
      </PageHeader>
      <div className="border-b">
        <div className="container flex max-w-[1400px] items-center justify-between py-4">
          <ChartsNav />
          <ThemesSwitcher />
        </div>
      </div>
      <div className="container max-w-[1400px] py-6" data-chart-block>
        <section id="charts" className="scroll-mt-20">
          {children}
        </section>
      </div>
    </main>
  )
}
