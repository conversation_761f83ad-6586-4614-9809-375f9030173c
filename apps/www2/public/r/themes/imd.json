{"name": "imd", "type": "registry:template", "files": [{"path": "../../packages/design-system/src/css/chart.css", "content": ":root {\n  --chart-positive-default: hsl(153, 54%, 56%);\n  --chart-positive-light: hsl(152, 71%, 85%);\n  --chart-negative-l1-default: hsl(4, 100%, 68%);\n  --chart-negative-l1-light: hsl(4, 80%, 88%);\n  --chart-negative-l2-default: hsl(26, 100%, 63%);\n  --chart-negative-l2-light: hsl(25, 91%, 83%);\n  --chart-negative-l3-default: hsl(46, 100%, 63%);\n  --chart-negative-l3-light: hsl(46, 100%, 83%);\n\n  --chart-order-primary-1: hsl(223, 100%, 93%);\n  --chart-order-primary-2: hsl(225, 94%, 87%);\n  --chart-order-primary-3: hsl(227, 100%, 74%);\n  --chart-order-primary-4: hsl(229, 100%, 65%);\n  --chart-order-primary-5: hsl(231, 100%, 57%);\n}\n\n@utility chart-theme-order-2 {\n  --chart-1: var(--chart-order-primary-2);\n  --chart-2: var(--chart-order-primary-4);\n}\n@utility chart-theme-order-3 {\n  --chart-1: var(--chart-order-primary-1);\n  --chart-2: var(--chart-order-primary-3);\n  --chart-3: var(--chart-order-primary-5);\n}\n@utility chart-theme-order-4 {\n  --chart-1: var(--chart-order-primary-1);\n  --chart-2: var(--chart-order-primary-2);\n  --chart-3: var(--chart-order-primary-4);\n  --chart-4: var(--chart-order-primary-5);\n}\n@utility chart-theme-order-5 {\n  --chart-1: var(--chart-order-primary-1);\n  --chart-2: var(--chart-order-primary-2);\n  --chart-3: var(--chart-order-primary-3);\n  --chart-4: var(--chart-order-primary-4);\n  --chart-5: var(--chart-order-primary-5);\n}\n\n@utility chart-theme-lt10 {\n  --chart-1: hsl(229, 100%, 66%);\n  --chart-2: hsl(175, 67%, 55%);\n  --chart-3: hsl(35, 98%, 65%);\n  --chart-4: hsl(280, 100%, 74%);\n  --chart-5: hsl(253, 100%, 67%);\n  --chart-6: hsl(75, 99%, 41%);\n  --chart-7: hsl(57, 100%, 32%);\n  --chart-8: hsl(323, 99%, 70%);\n  --chart-9: hsl(185, 79%, 36%);\n  --chart-10: hsl(175, 67%, 55%);\n}\n\n@utility chart-theme-lt20 {\n  --chart-1: hsl(229, 100%, 66%);\n  --chart-2: hsl(225, 95%, 87%);\n  --chart-3: hsl(175, 67%, 55%);\n  --chart-4: hsl(170, 65%, 80%);\n  --chart-5: hsl(35, 98%, 65%);\n  --chart-6: hsl(35, 92%, 80%);\n  --chart-7: hsl(280, 100%, 74%);\n  --chart-8: hsl(280, 92%, 86%);\n  --chart-9: hsl(253, 100%, 67%);\n  --chart-10: hsl(253, 92%, 90%);\n  --chart-11: hsl(75, 99%, 41%);\n  --chart-12: hsl(75, 65%, 75%);\n  --chart-13: hsl(57, 100%, 32%);\n  --chart-14: hsl(58, 50%, 80%);\n  --chart-15: hsl(323, 99%, 70%);\n  --chart-16: hsl(323, 100%, 92%);\n  --chart-17: hsl(185, 79%, 36%);\n  --chart-18: hsl(190, 75%, 80%);\n  --chart-19: hsl(111, 89%, 40%);\n  --chart-20: hsl(112, 75%, 85%);\n}\n@utility chart-theme-lt30 {\n  --chart-1: hsl(229, 100%, 66%);\n  --chart-2: hsl(225, 95%, 87%);\n  --chart-3: hsl(235, 90%, 40%);\n  --chart-4: hsl(175, 67%, 55%);\n  --chart-5: hsl(170, 65%, 80%);\n  --chart-6: hsl(181, 90%, 30%);\n  --chart-7: hsl(35, 98%, 65%);\n  --chart-8: hsl(35, 92%, 80%);\n  --chart-9: hsl(35, 90%, 40%);\n  --chart-10: hsl(280, 100%, 74%);\n  --chart-11: hsl(280, 92%, 86%);\n  --chart-12: hsl(285, 90%, 30%);\n  --chart-13: hsl(253, 100%, 67%);\n  --chart-14: hsl(253, 92%, 90%);\n  --chart-15: hsl(253, 90%, 40%);\n  --chart-16: hsl(75, 99%, 41%);\n  --chart-17: hsl(75, 65%, 75%);\n  --chart-18: hsl(70, 90%, 25%);\n  --chart-19: hsl(57, 100%, 32%);\n  --chart-20: hsl(58, 50%, 80%);\n  --chart-21: hsl(50, 80%, 25%);\n  --chart-22: hsl(323, 99%, 70%);\n  --chart-23: hsl(323, 100%, 92%);\n  --chart-24: hsl(323, 90%, 40%);\n  --chart-25: hsl(185, 79%, 36%);\n  --chart-26: hsl(190, 75%, 80%);\n  --chart-27: hsl(185, 80%, 25%);\n  --chart-28: hsl(111, 89%, 40%);\n  --chart-29: hsl(112, 75%, 85%);\n  --chart-30: hsl(111, 80%, 25%);\n}\n", "type": "registry:template"}, {"path": "../../packages/design-system/src/css/typography.css", "content": "/* 基础排版主题变量, 设计只定义了下面的工具类 typography-*，额外定义基础变量支撑它们 */\n@theme {\n  /** 基础字体大小 */\n  --text-2xs: 10px;\n  --text-xs: 12px;\n  --text-sm: 14px;\n  --text-base: 16px;\n  --text-lg: 18px;\n  --text-xl: 24px;\n  --text-2xl: 30px;\n  --text-3xl: 36px;\n\n  /** 行高 */\n  --leading-none: 1;\n  --leading-tight: 1.25;\n  --leading-snug: 1.375;\n  --leading-normal: 1.5;\n  --leading-relaxed: 1.625;\n  --leading-loose: 2;\n\n  /** 具体行高像素值 */\n  --text-2xs--line-height: 18px;\n  --text-xs--line-height: 20px;\n  --text-sm--line-height: 22px;\n  --text-base--line-height: 24px;\n  --text-lg--line-height: 26px;\n  --text-xl--line-height: 32px;\n  --text-2xl--line-height: 36px;\n  --text-3xl--line-height: 40px;\n\n  /** 字重 */\n  --font-weight-thin: 100;\n  --font-weight-light: 300;\n  --font-weight-normal: 400;\n  --font-weight-medium: 500;\n  --font-weight-semibold: 600;\n  --font-weight-bold: 700;\n  --font-weight-extrabold: 800;\n  --font-weight-black: 900;\n\n  /** 默认字重 */\n  --text-xs--font-weight: var(--font-normal);\n  --text-sm--font-weight: var(--font-normal);\n  --text-base--font-weight: var(--font-normal);\n  --text-lg--font-weight: var(--font-semibold);\n  --text-xl--font-weight: var(--font-semibold);\n}\n\n/* 组合工具类 */\n@utility typography-heading-large {\n  font-size: var(--text-xl);\n  font-weight: var(--font-semibold);\n  line-height: var(--text-xl--line-height);\n}\n\n@utility typography-heading-medium {\n  font-size: var(--text-lg);\n  font-weight: var(--font-semibold);\n  line-height: var(--text-lg--line-height);\n}\n\n@utility typography-heading-small {\n  font-size: var(--text-base);\n  font-weight: var(--font-semibold);\n  line-height: var(--text-base--line-height);\n}\n\n@utility typography-body-large {\n  font-size: var(--text-base);\n  font-weight: var(--font-normal);\n  line-height: var(--text-base--line-height);\n}\n\n@utility typography-body-large-plus {\n  font-size: var(--text-base);\n  font-weight: var(--font-semibold);\n  line-height: var(--text-base--line-height);\n}\n\n@utility typography-body-medium {\n  font-size: var(--text-sm);\n  font-weight: var(--font-normal);\n  line-height: var(--text-sm--line-height);\n}\n\n@utility typography-body-medium-plus {\n  font-size: var(--text-sm);\n  font-weight: var(--font-semibold);\n  line-height: var(--text-sm--line-height);\n}\n\n@utility typography-body-small {\n  font-size: var(--text-xs);\n  font-weight: var(--font-normal);\n  line-height: var(--text-xs--line-height);\n}\n\n@utility typography-body-small-plus {\n  font-size: var(--text-xs);\n  font-weight: var(--font-semibold);\n  line-height: var(--text-xs--line-height);\n}\n\n@utility typography-paragraph-medium {\n  font-size: var(--text-sm);\n  font-weight: var(--font-normal);\n  line-height: var(--text-xs--line-height);\n}\n\n@utility typography-paragraph-small {\n  font-size: var(--text-xs);\n  font-weight: var(--font-normal);\n  line-height: var(--text-2xs--line-height);\n}\n", "type": "registry:template"}, {"path": "../../packages/design-system/src/css/utilities.css", "content": "@utility imd-scrollbar-hide11 {\n  -ms-overflow-style: none;\n  scrollbar-width: none;\n  &::-webkit-scrollbar {\n    display: none;\n  }\n}\n", "type": "registry:template"}, {"path": "../../packages/design-system/src/themes/imd.css", "content": "@import 'tailwindcss';\n@import '../css/utilities.css';\n@import '../css/typography.css';\n@import '../css/chart.css';\n\n:root {\n  --radius: 2px;\n  --spacing: 4px;\n}\n\n@theme {\n  --spacing-imd-2xs: calc(var(--spacing) * 0.5);\n  --spacing-imd-xs: calc(var(--spacing) * 1);\n  --spacing-imd-sm: calc(var(--spacing) * 2);\n  --spacing-imd-md: calc(var(--spacing) * 3);\n  --spacing-imd-lg: calc(var(--spacing) * 4);\n  --spacing-imd-xl: calc(var(--spacing) * 6);\n  --spacing-imd-2xl: calc(var(--spacing) * 8);\n\n  --padding-imd-2xs: calc(var(--spacing) * 0.25);\n  --padding-imd-xs: calc(var(--spacing) * 0.5);\n  --padding-imd-sm: calc(var(--spacing) * 1);\n  --padding-imd-md: calc(var(--spacing) * 1.5);\n  --padding-imd-lg: calc(var(--spacing) * 2);\n  --padding-imd-xl: calc(var(--spacing) * 2.5);\n  --padding-imd-2xl: calc(var(--spacing) * 3);\n  --padding-imd-3xl: calc(var(--spacing) * 4);\n  --padding-imd-4xl: calc(var(--spacing) * 5);\n  --padding-imd-5xl: calc(var(--spacing) * 6);\n\n  --radius-xs: var(--radius, 2px);\n  --radius-sm: calc(var(--radius) * 2);\n  --radius-md: calc(var(--radius) * 3);\n  --radius-lg: calc(var(--radius) * 4);\n  --radius-xl: 10px;\n  --radius-2xl: 12px;\n  --radius-3xl: 14px;\n  --radius-full: 9999px;\n\n  --color-red-1: #ffeceb;\n  --color-red-2: #fcd9d4;\n  --color-red-3: #ffb8ad;\n  --color-red-4: #ff9185;\n  --color-red-5: #ff675c;\n  --color-red-6: #f23730;\n  --color-red-7: #cc1f1f;\n  --color-red-8: #a61116;\n  --color-red-9: #80060e;\n  --color-red-10: #59040c;\n\n  --color-gray-1: #f7f8fc;\n  --color-gray-2: #edeff5;\n  --color-gray-3: #e4e5ed;\n  --color-gray-4: #d6d8e1;\n  --color-gray-5: #bfc1cd;\n  --color-gray-6: #9fa2b1;\n  --color-gray-7: #878b9c;\n  --color-gray-8: #717486;\n  --color-gray-9: #575b6d;\n  --color-gray-10: #444757;\n  --color-gray-11: #343643;\n  --color-gray-12: #272933;\n  --color-gray-13: #101117;\n\n  --color-imileBlue-1: #f0f4ff;\n  --color-imileBlue-2: #d9e4ff;\n  --color-imileBlue-3: #abbfff;\n  --color-imileBlue-4: #7895ff;\n  --color-imileBlue-5: #4f6fff;\n  --color-imileBlue-6: #2546ff;\n  --color-imileBlue-7: #162cd9;\n  --color-imileBlue-8: #0917b3;\n  --color-imileBlue-9: #00078c;\n  --color-imileBlue-10: #000266;\n\n  --color-blue-1: #ebf4fc;\n  --color-blue-2: #cfe9ff;\n  --color-blue-3: #a1d0ff;\n  --color-blue-4: #78b7ff;\n  --color-blue-5: #559cfa;\n  --color-blue-6: #2474ed;\n  --color-blue-7: #1456c7;\n  --color-blue-8: #083ba1;\n  --color-blue-9: #00257a;\n  --color-blue-10: #001654;\n\n  --color-orange-1: #fff5eb;\n  --color-orange-2: #ffe8d1;\n  --color-orange-3: #ffcb9e;\n  --color-orange-4: #ffb778;\n  --color-orange-5: #ff9442;\n  --color-orange-6: #fa7319;\n  --color-orange-7: #d4540b;\n  --color-orange-8: #ad3a00;\n  --color-orange-9: #872900;\n  --color-orange-10: #611a00;\n\n  --color-green-1: #e3fcf0;\n  --color-green-2: #d8f2e4;\n  --color-green-3: #a8e6c5;\n  --color-green-4: #7cd9ac;\n  --color-green-5: #54cc96;\n  --color-green-6: #30bf84;\n  --color-green-7: #1f996a;\n  --color-green-8: #117351;\n  --color-green-9: #084d37;\n  --color-green-10: #03261c;\n\n  --color-yellow-1: #fcfbeb;\n  --color-yellow-2: #fcf8c5;\n  --color-yellow-3: #fcf39d;\n  --color-yellow-4: #fae97d;\n  --color-yellow-5: #fade50;\n  --color-yellow-6: #f2c81f;\n  --color-yellow-7: #cca010;\n  --color-yellow-8: #a67b05;\n  --color-yellow-9: #805900;\n  --color-yellow-10: #593b00;\n\n  --color-cyan-1: #eefcf9;\n  --color-cyan-2: #c1f7ee;\n  --color-cyan-3: #96f2e4;\n  --color-cyan-4: #6ae6d7;\n  --color-cyan-5: #41d9cc;\n  --color-cyan-6: #1dccc3;\n  --color-cyan-7: #0fa6a3;\n  --color-cyan-8: #057d80;\n  --color-cyan-9: #005559;\n  --color-cyan-10: #002f33;\n\n  --color-purple-1: #f9edff;\n  --color-purple-2: #eed1ff;\n  --color-purple-3: #deadff;\n  --color-purple-4: #cc8aff;\n  --color-purple-5: #ac5cf2;\n  --color-purple-6: #821ee5;\n  --color-purple-7: #610fbf;\n  --color-purple-8: #450599;\n  --color-purple-9: #2e0073;\n  --color-purple-10: #1c004d;\n\n  --color-bright-green-1: #f0fff1;\n  --color-bright-green-2: #cffcd4;\n  --color-bright-green-3: #a8e6c5;\n  --color-bright-green-4: #aafab5;\n  --color-bright-green-5: #5ce579;\n  --color-bright-green-6: #1cd94b;\n  --color-bright-green-7: #0eb33d;\n  --color-bright-green-8: #048c2f;\n  --color-bright-green-9: #006624;\n  --color-bright-green-10: #004018;\n\n  --color-bright-orange-1: #fff9e8;\n  --color-bright-orange-2: #ffecbf;\n  --color-bright-orange-3: #ffdc96;\n  --color-bright-orange-4: #ffca6e;\n  --color-bright-orange-5: #ffb545;\n  --color-bright-orange-6: #f5961b;\n  --color-bright-orange-7: #cf740c;\n  --color-bright-orange-8: #a85502;\n  --color-bright-orange-9: #823d00;\n  --color-bright-orange-10: #5c2800;\n\n  --shadow-1:\n    0px 1px 4px 0px rgba(16, 17, 24, 0.08),\n    0px 2px 16px 0px rgba(16, 17, 24, 0.04),\n    0px 4px 32px 0px rgba(16, 17, 23, 0.01);\n  --shadow-2:\n    0px 2px 10px 0px rgba(16, 17, 23, 0.1),\n    0px 5px 20px 0px rgba(16, 17, 23, 0.06),\n    0px 10px 30px 0px rgba(16, 17, 23, 0.03);\n  --shadow-3:\n    0px 4px 16px 0px rgba(16, 17, 23, 0.12),\n    0px 8px 28px 0px rgba(16, 17, 23, 0.08),\n    0px 12px 48px 0px rgba(16, 17, 23, 0.02);\n  --shadow-component-focus: 0px 0px 0px 2px rgba(37, 70, 255, 0.2);\n  --shadow-component-focus-error: 0px 0px 0px 2px rgba(242, 55, 48, 0.2);\n\n  --z-index-imd-drawer: 1000; /* Drawer/Dialog/Modal */\n  --z-index-imd-modal: 1000; /* Modal */\n  --z-index-imd-popover: 1050; /* Popover/Dropdown */\n  --z-index-imd-tooltip: 1060; /* Tooltip */\n  --z-index-imd-toast: 1070; /* Toast/Notification */\n  --z-index-imd-max: 9999;\n}\n@theme inline {\n  /** 业务 token，暂时内置在主题里 */\n  --primary: var(--color-imileBlue-6);\n  --primary-foreground: #ffffff;\n  --imd-muted: var(--color-gray-7);\n  --imd-disabled: var(--color-gray-4);\n  --imd-disabled-on-gray: var(--color-gray-5);\n  --imd-container: var(--color-gray-2);\n  --imd-component-fill: var(--color-gray-2);\n  --imd-component-hover: var(--color-gray-3);\n  --imd-component-disabled: var(--color-gray-4);\n  --imd-card-hover: var(--color-gray-1);\n\n  --imd-border-light: var(--color-gray-1);\n  --imd-border-dark: var(--color-gray-2);\n  --imd-border-component: var(--color-gray-2);\n\n  --border: var(--color-gray-2);\n  --ring: rgba(37, 70, 255, 0.2);\n  --background: #ffffff;\n  --foreground: var(--color-gray-13);\n  --popover-foreground: #ffffff;\n  --popover: #ffffff;\n\n  --primary-1: var(--color-imileBlue-1);\n  --primary-2: var(--color-imileBlue-2);\n  --primary-3: var(--color-imileBlue-3);\n  --primary-4: var(--color-imileBlue-4);\n  --primary-5: var(--color-imileBlue-5);\n  --primary-6: var(--color-imileBlue-6);\n  --primary-7: var(--color-imileBlue-7);\n  --primary-8: var(--color-imileBlue-8);\n  --primary-9: var(--color-imileBlue-9);\n  --primary-10: var(--color-imileBlue-10);\n\n  /** 暂时内置主题色为 imileBlue-6，如果后面有多主题色，移除后交给业务系统 */\n  --color-primary: var(--primary, var(--color-imileBlue-6));\n  --color-primary-primary: var(--primary, var(--color-imileBlue-6));\n  --color-primary-foreground: var(--primary-foreground, #ffffff);\n  /** primary 中的代表色 */\n  --color-primary-1: var(--primary-1, var(--color-imileBlue-1));\n  --color-primary-2: var(--primary-2, var(--color-imileBlue-2));\n  --color-primary-3: var(--primary-3, var(--color-imileBlue-3));\n  --color-primary-4: var(--primary-4, var(--color-imileBlue-4));\n  --color-primary-5: var(--primary-5, var(--color-imileBlue-5));\n  --color-primary-6: var(--primary-6, var(--color-imileBlue-6));\n  --color-primary-7: var(--primary-7, var(--color-imileBlue-7));\n  --color-primary-8: var(--primary-8, var(--color-imileBlue-8));\n  --color-primary-9: var(--primary-9, var(--color-imileBlue-9));\n  --color-primary-10: var(--primary-10, var(--color-imileBlue-10));\n\n  /** 基础颜色 */\n  --color-red: var(--color-red-6);\n  --color-red-primary: var(--color-red-6);\n  --color-red-hover: var(--color-red-7);\n  --color-red-selected: var(--color-red-8);\n  --color-red-disabled: var(--color-red-4);\n  --color-red-secondary: var(--color-red-1);\n  --color-red-secondary-hover: var(--color-red-2);\n  --color-red-secondary-selected: var(--color-red-3);\n\n  --color-imileBlue-secondary: var(--color-imileBlue-1);\n  --color-imileBlue-secondary-hover: var(--color-imileBlue-2);\n  --color-imileBlue-secondary-selected: var(--color-imileBlue-3);\n  --color-imileBlue-disabled: var(--color-imileBlue-4);\n  --color-imileBlue-primary: var(--color-imileBlue-6);\n  --color-imileBlue-hover: var(--color-imileBlue-7);\n  --color-imileBlue-selected: var(--color-imileBlue-8);\n\n  --color-blue-secondary: var(--color-blue-1);\n  --color-blue-secondary-hover: var(--color-blue-2);\n  --color-blue-secondary-selected: var(--color-blue-3);\n  --color-blue-disabled: var(--color-blue-4);\n  --color-blue-primary: var(--color-blue-6);\n  --color-blue-hover: var(--color-blue-7);\n  --color-blue-selected: var(--color-blue-8);\n\n  --color-orange-secondary: var(--color-orange-1);\n  --color-orange-secondary-hover: var(--color-orange-2);\n  --color-orange-secondary-selected: var(--color-orange-3);\n  --color-orange-disabled: var(--color-orange-4);\n  --color-orange-primary: var(--color-orange-6);\n  --color-orange-hover: var(--color-orange-7);\n  --color-orange-selected: var(--color-orange-8);\n\n  --color-green-secondary: var(--color-green-1);\n  --color-green-secondary-hover: var(--color-green-2);\n  --color-green-secondary-selected: var(--color-green-3);\n  --color-green-disabled: var(--color-green-4);\n  --color-green-primary: var(--color-green-6);\n  --color-green-hover: var(--color-green-7);\n  --color-green-selected: var(--color-green-8);\n\n  --color-yellow-secondary: var(--color-yellow-1);\n  --color-yellow-secondary-hover: var(--color-yellow-2);\n  --color-yellow-secondary-selected: var(--color-yellow-3);\n  --color-yellow-disabled: var(--color-yellow-4);\n  --color-yellow-primary: var(--color-yellow-6);\n  --color-yellow-hover: var(--color-yellow-7);\n  --color-yellow-selected: var(--color-yellow-8);\n\n  --color-cyan-secondary: var(--color-cyan-1);\n  --color-cyan-secondary-hover: var(--color-cyan-2);\n  --color-cyan-secondary-selected: var(--color-cyan-3);\n  --color-cyan-disabled: var(--color-cyan-4);\n  --color-cyan-primary: var(--color-cyan-6);\n  --color-cyan-hover: var(--color-cyan-7);\n  --color-cyan-selected: var(--color-cyan-8);\n\n  --color-purple-secondary: var(--color-purple-1);\n  --color-purple-secondary-hover: var(--color-purple-2);\n  --color-purple-secondary-selected: var(--color-purple-3);\n  --color-purple-disabled: var(--color-purple-4);\n  --color-purple-primary: var(--color-purple-6);\n  --color-purple-hover: var(--color-purple-7);\n  --color-purple-selected: var(--color-purple-8);\n\n  --color-bright-green-secondary: var(--color-bright-green-1);\n  --color-bright-green-secondary-hover: var(--color-bright-green-2);\n  --color-bright-green-secondary-selected: var(--color-bright-green-3);\n  --color-bright-green-disabled: var(--color-bright-green-4);\n  --color-bright-green-primary: var(--color-bright-green-6);\n  --color-bright-green-hover: var(--color-bright-green-7);\n  --color-bright-green-selected: var(--color-bright-green-8);\n\n  --color-bright-orange-secondary: var(--color-bright-orange-1);\n  --color-bright-orange-secondary-hover: var(--color-bright-orange-2);\n  --color-bright-orange-secondary-selected: var(--color-bright-orange-3);\n  --color-bright-orange-disabled: var(--color-bright-orange-4);\n  --color-bright-orange-primary: var(--color-bright-orange-6);\n  --color-bright-orange-hover: var(--color-bright-orange-7);\n  --color-bright-orange-selected: var(--color-bright-orange-8);\n\n  /** gray，文字/背景/组件/卡片/边框, iMile特有 */\n  /* --color-imd-primary: var(--primary, var(--color-gray-13)); */\n  /* --color-imd-secondary: var(--secondary, var(--color-gray-10)); */\n  --color-imd-muted: var(--imd-muted, var(--color-gray-7));\n  --color-imd-disabled: var(--imd-disabled, var(--color-gray-4));\n  --color-imd-disabled-on-gray: var(\n    --imd-disabled-on-gray,\n    var(--color-gray-5)\n  );\n  --color-imd-container: var(--imd-container, var(--color-gray-2));\n  --color-imd-component-fill: var(--imd-component-fill, var(--color-gray-2));\n  --color-imd-component-hover: var(--imd-component-hover, var(--color-gray-3));\n  --color-imd-component-disabled: var(\n    --imd-component-disabled,\n    var(--color-gray-4)\n  );\n  --color-imd-card-hover: var(--imd-card-hover, var(--color-gray-1));\n  --color-imd-border-light: var(--imd-border-light, var(--color-gray-1));\n  --color-imd-border-dark: var(--imd-border-dark, var(--color-gray-2));\n  --color-imd-border-component: var(\n    --imd-border-component,\n    var(--color-gray-2)\n  );\n  /** 颜色变量，需要业务项目定义 */\n  --color-border: var(--border, var(--color-gray-2));\n  --color-ring: var(--ring, rgba(37, 70, 255, 0.2));\n  --color-background: var(--background, #ffffff);\n  --color-popover-foreground: var(--popover-foreground, #ffffff);\n  --color-popover: var(--popover, #ffffff);\n  --color-foreground: var(--foreground, var(--color-gray-13));\n}\n", "type": "registry:template"}], "description": "imd 组件库的默认 tailwindcss 主题", "tags": ["default theme", "theme"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "a12efd002ea1fe918fdfc5ae96c49e564ae1c421"}, "generator": "imd-build-registry", "architectureLayer": "unknown", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}