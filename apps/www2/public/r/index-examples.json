[{"name": "base-affix", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-affix/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-affix/target-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-alert", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-alert/close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-alert/commonIcon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-alert/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-alert/open-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-button", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-button/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-button/disabled-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-checkbox", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-checkbox/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-checkbox/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-checkbox/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-checkbox/indeterminate-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-drawer", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-drawer/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-drawer/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-drawer/direction-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-drawer/nest-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-drawer/scroll-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-dropdown-menu", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-dropdown-menu/controllable-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-dropdown-menu/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-dropdown-menu/side-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-ellipsis", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-ellipsis/default-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-message", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-message/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-message/expand-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-message/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-message/types-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-popconfirm", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-popconfirm/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-popconfirm/default-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-popover", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-popover/action-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-popover/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-popover/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-popover/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-popover/position-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-radio-group", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-radio-group/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-radio-group/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-radio-group/icon.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-scroll-area", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-scroll-area/default-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-select", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-select/defaultClassName.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-select/select-content-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-select/select-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-select/select-form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-select/select-multi-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-select/select-scrollable-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-select/select-search-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-steps", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-steps/custom-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-steps/default-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-switch", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-switch/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-switch/disabled-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-tabs", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-tabs/base-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-tag", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-tag/asChild-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-tag/close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-tag/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-tag/icon-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-tooltip", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/base-tooltip/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-tooltip/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-tooltip/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/base-tooltip/provider-demo.tsx", "type": "registry:example"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "chart-area", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/chart-area/chart-area-base.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-brush.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-cross-x.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-cross.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-legend.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-linear.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-referenceLine.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-tooltip-direction.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-xaxis-angle.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-area/chart-area-yaxis.tsx", "type": "registry:example"}], "architectureLayer": "unknown", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "chart-bar", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/chart-bar/chart-bar-auto-width.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-base.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-interval.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-label.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-multiple.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-multiple2.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-stacked-bidirectional.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-stacked-rate.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-stacked.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical-interval.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical-label.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical-multiple-label.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical-multiple.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical-stack-rate.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical-stack.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical-stacked-bidirectional.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-vertical.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-bar/chart-bar-yaxis.tsx", "type": "registry:example"}], "architectureLayer": "unknown", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "chart-line", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/chart-line/chart-line-base.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-brush-color.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-brush.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-cross-x.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-cross.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-brush.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-empty.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-grid.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-legend.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-theme.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-tooltip.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-xaxis.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-demo-yaxis.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-legend-custom.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-legend.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-linear.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-referenceLine.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-tooltip-direction.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-xaxis-angle.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-yaxis-type-category.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-line/chart-line-yaxis.tsx", "type": "registry:example"}], "architectureLayer": "unknown", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "chart-pie", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/chart-pie/chart-pie-base.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-donut-active.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-donut-text.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-donut.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-interactive.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-label-custom.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-label-list.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-label.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-legend.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-separator-none.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/chart-pie/chart-pie-stacked.tsx", "type": "registry:example"}], "architectureLayer": "unknown", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-batch-input", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-batch-input/controlled-uncontrolled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-batch-input/custom-split-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-batch-input/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-batch-input/icon.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-button", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-button/color-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/group-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/loading-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/onSubmit-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/tips-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-batch-input", "pro-textField"]}, {"name": "pro-dialog", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-dialog/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dialog/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dialog/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dialog/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dialog/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-drawer", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-drawer/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-drawer/custom-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-drawer/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-drawer/dismissible-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-drawer/fullscreen-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-drawer/no-overlay-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-drawer/placement-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-drawer/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-page-filter"]}, {"name": "pro-dropdown-menu", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-dropdown-menu/content-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dropdown-menu/controllable-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dropdown-menu/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dropdown-menu/side-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dropdown-menu/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dropdown-menu/trigger-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-dropdown-menu/triggerSize-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-ellipsis", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-ellipsis/default-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-empty", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-empty/complex-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-empty/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-empty/simple-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-empty/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-empty/type-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-form", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-form/async-validation-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/compare-modes-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/dynamic-form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/form-methods-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/global-disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/onsubmit-test-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-form/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-fullscreen-dialog", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-fullscreen-dialog/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-fullscreen-dialog/custom-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-fullscreen-dialog/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-fullscreen-dialog/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-input-number", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-input-number/clear-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/hasError-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/icon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/prefix-suffix-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-page-filter", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-page-filter/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-page-filter/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-page-filter/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-page-filter/drawer-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-page-filter/icon/index.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-page-filter/icon/SelectExpand.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-page-filter/icon/SelectSearchIcon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-page-filter/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["crud", "crud2"]}, {"name": "pro-popconfirm", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-popconfirm/action-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popconfirm/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popconfirm/auto-close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popconfirm/customize-content-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popconfirm/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popconfirm/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popconfirm/status-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-popover", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-popover/action-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popover/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popover/auto-close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popover/customize-content-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popover/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popover/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-popover/status-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-tooltip"]}, {"name": "pro-poptextarea", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-poptextarea/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-poptextarea/custom-split-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-poptextarea/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-poptextarea/icon.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-batch-input"]}, {"name": "pro-select", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-select/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/ellipsis-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/icon/index.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/icon/SelectExpand.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/icon/SelectSearchIcon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/multi-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/popper-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/search-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/select-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/select-once-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/select-once-origin-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/select-origin-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/select-prefix-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/select-scroll-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/select-search-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-select/sizevariant-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-switch", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-switch/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-switch/description-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-switch/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-switch/loading-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-switch/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-tabs", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-tabs/base-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/count-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/edit-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/round-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tabs/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-tag", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-tag/close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tag/color-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tag/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tag/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tag/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tag/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-textarea", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-textarea/autoResize-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/hasError-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/maxCount-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-textField", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-textField/add-before-after-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/clear-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/hasError-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/icon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-variant-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/prefix-suffix-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/search-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-batch-input"]}, {"name": "pro-tooltip", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-tooltip/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tooltip/colorful-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tooltip/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tooltip/offset-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-tooltip/position-demo.tsx", "type": "registry:example"}], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-ellipsis"]}, {"name": "ui-alert", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-alert/close-button-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/closeIcon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/ellipsis-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/global-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/open-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/status-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-alert/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-button", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-button/asChild-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/color-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/group-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/headless-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/loading-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 10, "ce": 0, "instability": 0}, "dependents": ["pro-button", "pro-dialog", "pro-drawer", "pro-dropdown-menu", "pro-fullscreen-dialog", "pro-page-filter", "pro-popconfirm", "pro-poptextarea", "ui-dropdown-menu", "ui-popconfirm"]}, {"name": "ui-checkbox", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-checkbox/card-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/group-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/indeterminate-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/state-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-checkbox/with-label-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["ui-select"]}, {"name": "ui-dialog", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-dialog/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dialog/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dialog/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dialog/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dialog/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-dialog", "ui-fullscreen-dialog"]}, {"name": "ui-drawer", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-drawer/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-drawer/custom-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-drawer/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-drawer/dismissible-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-drawer/fullscreen-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-drawer/no-overlay-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-drawer/placement-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-drawer/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 4, "ce": 0, "instability": 0}, "dependents": ["crud", "crud2", "pro-drawer", "pro-page-filter"]}, {"name": "ui-dropdown-menu", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-dropdown-menu/content-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dropdown-menu/controllable-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dropdown-menu/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dropdown-menu/icon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dropdown-menu/side-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dropdown-menu/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dropdown-menu/trigger-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-dropdown-menu/triggerSize-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-dropdown-menu"]}, {"name": "ui-ellipsis", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-ellipsis/default-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-ellipsis", "ui-select"]}, {"name": "ui-empty", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-empty/complex-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-empty/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-empty/simple-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-empty/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-empty/type-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-empty"]}, {"name": "ui-form", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-form/async-validation-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/compare-modes-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/dynamic-form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/form-methods-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/global-disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form/validation-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-form"]}, {"name": "ui-form-field", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-form-field/default.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-form-field/icon.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-formField", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-formField/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-formField/icon.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-fullscreen-dialog", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-fullscreen-dialog/controlled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-fullscreen-dialog/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-fullscreen-dialog/fullscreen-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-fullscreen-dialog/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-fullscreen-dialog"]}, {"name": "ui-hello-world", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-hello-world/default-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-input-number", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-input-number/affix-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/clear-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/hasError-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/icon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/inline-affix-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-input-number"]}, {"name": "ui-loading", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-loading/custom-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-loading/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-loading/partial-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-loading/system-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-loading/tw-colors-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 3, "ce": 0, "instability": 0}, "dependents": ["pro-button", "ui-button", "ui-spin"]}, {"name": "ui-message", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-message/basic-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/custom-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/expand-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/filled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/promise-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-message/types-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-notification", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-notification/action-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-notification/basic-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-notification/expand-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-notification/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-notification/style-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-notification/types-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-popconfirm", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-popconfirm/action-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popconfirm/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popconfirm/auto-close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popconfirm/customize-content-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popconfirm/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popconfirm/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popconfirm/status-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-popconfirm"]}, {"name": "ui-popover", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-popover/action-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/anchor-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/auto-close-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/customize-content-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/status-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-popover", "pro-poptextarea"]}, {"name": "ui-radio-group", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-radio-group/card-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-radio-group/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-radio-group/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-radio-group/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-radio-group/radio-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-radio-group/radio-group-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-radio-group/tip-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-scroll-area", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-scroll-area/horizontal-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-scroll-area/list-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-select", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-select/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/ellipsis-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/multi-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/popper-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/position-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/search-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-contentui-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-customItem-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-dropWidth-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-tagList-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-triggerui-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/select-virtuoso-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-select/sizevariant-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-select"]}, {"name": "ui-skeleton", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-skeleton/base-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-skeleton/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-skeleton/group-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-spin", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-spin/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-spin/loading-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-steps", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-steps/clickable-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/direction-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/dot-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/status-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/type-arrows-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/type-no-connection-line-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-steps/type-tabs-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-switch", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-switch/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-switch/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-switch/description-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-switch/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-switch/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-switch/loading-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-switch/size-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-switch"]}, {"name": "ui-tabs", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-tabs/base-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/count-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/edit-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/example-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/scroll-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tabs/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-tabs"]}, {"name": "ui-tag", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-tag/asChild-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tag/closable-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tag/color-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tag/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tag/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tag/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tag/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-tag"]}, {"name": "ui-textarea", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-textarea/autoResize-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/form-validation-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/hasError-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/maxCount-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-poptextarea", "pro-textarea"]}, {"name": "ui-textField", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-textField/affix-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/button-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/clear-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/control-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/disabled-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/form-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/hasError-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/icon-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/icon.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/inline-affix-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/password-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/size-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/variant-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-form", "pro-textField"]}, {"name": "ui-tooltip", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-tooltip/arrow-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tooltip/colorful-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tooltip/default-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tooltip/offset-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-tooltip/position-demo.tsx", "type": "registry:example"}], "architectureLayer": "UI", "architectureMetrics": {"ca": 5, "ce": 0, "instability": 0}, "dependents": ["pro-button", "pro-tooltip", "ui-ellipsis", "ui-form", "ui-select"]}, {"name": "z-index", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/z-index/basic-layering-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/z-index/comprehensive-demo.tsx", "type": "registry:example"}, {"path": "registry/default/examples/ui/z-index/nested-layering-demo.tsx", "type": "registry:example"}], "architectureLayer": "unknown", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}]