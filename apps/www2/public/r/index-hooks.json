[{"name": "use-mobile", "type": "registry:hook", "files": [{"path": "registry/default/hooks/use-mobile.tsx", "type": "registry:hook"}], "architectureLayer": "hooks", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "use-translation", "type": "registry:hook", "files": [{"path": "registry/default/hooks/use-translation.ts", "type": "registry:hook"}], "architectureLayer": "hooks", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-poptextarea"]}]