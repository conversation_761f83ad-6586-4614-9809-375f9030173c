{"name": "use-translation", "type": "registry:hook", "files": [{"path": "registry/default/hooks/use-translation.ts", "content": "/**\n * IMD 组件库国际化 Hook - 基础模板\n *\n * 这是一个基础的 useTranslation hook 实现，用作用户项目的起点。\n * 根据你的项目需求，你需要对这个文件进行相应的改造和扩展。\n *\n * 🚀 快速开始:\n * 1. 确定你的国际化方案：i18next、react-intl、自定义方案等\n * 2. 根据选择的方案修改下面的实现\n * 3. 添加语言状态管理（Context、Redux、Zustand 等）\n * 4. 配置翻译资源的加载方式\n *\n * 📋 常见集成场景:\n *\n * 【场景1: i18next 集成】\n * import { useTranslation as useI18nextTranslation } from 'react-i18next'\n * 在 useTranslation() 中调用 useI18nextTranslation() 获取翻译函数\n *\n * 【场景2: react-intl 集成】\n * import { useIntl } from 'react-intl'\n * 使用 intl.formatMessage() 实现翻译逻辑\n *\n * 【场景3: 自定义简单方案】\n * 使用 React Context 管理语言状态和翻译资源\n * 实现基础的参数插值和回退机制\n *\n * 【场景4: 企业级方案】\n * 结合命名空间、懒加载、缓存等高级功能\n *\n * 💡 提示: 查看文档中的完整集成示例来了解具体的实现方式\n */\n\nimport { useMemo } from 'react'\n\nexport interface TranslationParams {\n  [key: string]: string | number | boolean | null | undefined\n}\n/**\n * 默认翻译对象\n *\n * 🔧 扩展指南:\n * 1. 添加更多语言：在此对象中添加新的语言键（如 'ja-JP', 'ko-KR' 等）\n * 2. 组织翻译文件：大型项目建议将翻译拆分到独立文件中\n *    - translations/zh-CN.json\n *    - translations/en-US.json\n *    然后动态导入或使用构建工具打包\n * 3. 嵌套结构：支持深层嵌套的翻译键\n *    例：{ user: { profile: { name: '姓名' } } }\n * 4. 与后端集成：可以从 API 动态加载翻译资源\n *\n * 💡 最佳实践:\n * - 使用语义化的键名，避免使用翻译文本作为键\n * - 保持翻译键的一致性，所有语言应有相同的键结构\n * - 考虑使用 TypeScript 约束翻译键的类型安全\n */\nconst translations = {\n  'zh-CN': {\n    Cancel: '取消',\n    Confirm: '确认',\n    'No Data': '暂无数据',\n    PleaseSelect: '请选择',\n    More: '查看更多',\n    Less: '收起',\n  },\n  'en-US': {\n    Cancel: 'Cancel',\n    Confirm: 'Confirm',\n    'No Data': 'No Data',\n    PleaseSelect: 'Please Select',\n    More: 'More',\n    Less: 'Less',\n  },\n} as const satisfies Record<string, Record<string, string>>\n\n/**\n * 获取指定语言的默认翻译\n *\n * @param locale 语言代码，默认为 'zh-CN'\n * @returns 对应语言的翻译对象\n *\n * 🔧 扩展建议:\n * - 添加回退逻辑：当指定语言不存在时，回退到默认语言\n * - 支持部分匹配：'zh' 自动匹配 'zh-CN'\n * - 集成外部翻译服务：Google Translate API、百度翻译等\n */\nexport function getDefaultTranslations(locale = 'zh-CN') {\n  return translations[locale]\n}\n\n/**\n * 默认的 useTranslation hook 实现\n *\n * ⚠️  重要提醒 - 依赖项问题:\n * 当前实现中的空依赖数组 `[]` 在语言切换时可能导致组件不重新渲染。\n * 根据你选择的国际化方案，需要添加适当的依赖项：\n *\n * 🔧 解决方案示例:\n *\n * 【方案1: 使用 React Context】\n * const { language, translations } = useI18nContext()\n * 依赖项: [language, translations]\n *\n * 【方案2: 使用 i18next】\n * const { i18n } = useI18nextTranslation()\n * 依赖项: [i18n.language, i18n.store.data]\n *\n * 【方案3: 使用状态管理库】\n * const language = useSelector(state => state.i18n.language)\n * 依赖项: [language]\n *\n * 【方案4: 简单状态管理】\n * const [language, setLanguage] = useState('zh-CN')\n * 依赖项: [language]\n *\n * 🚀 改进建议:\n * 1. 添加语言状态管理：使用 Context、Redux、Zustand 等\n * 2. 实现参数插值：支持 {{param}} 语法进行动态文本替换\n * 3. 添加错误处理：翻译缺失时的回退机制\n * 4. 性能优化：大型应用中考虑使用 React.memo 和 useCallback\n * 5. 类型安全：使用 TypeScript 约束翻译键的类型\n *\n * 📋 集成模式参考:\n *\n * 【简单自定义方案】\n * 使用 Context 管理语言状态，实现基础的翻译和参数插值\n *\n * 【i18next 集成】\n * const { t: i18nextT } = useI18nextTranslation()\n * 优先使用 i18next 翻译，回退到组件库默认翻译\n *\n * 【react-intl 集成】\n * const intl = useIntl()\n * 使用 intl.formatMessage() 实现翻译逻辑\n */\nexport function useTranslation() {\n  return useMemo(() => {\n    /**\n     * 翻译函数\n     *\n     * @param key 翻译键\n     * @param params 参数对象，用于插值替换\n     * @returns 翻译后的文本\n     *\n     * 🔧 当前实现说明:\n     * - 默认只返回 key 本身，用作占位符\n     * - 实际项目中应该根据当前语言返回对应的翻译\n     * - 可以在这里添加参数插值逻辑\n     *\n     * 💡 参数插值示例:\n     * t('Welcome {{name}}', { name: 'John' }) -> 'Welcome John'\n     *\n     * 🚀 扩展建议:\n     * - 添加翻译缺失时的警告日志\n     * - 实现嵌套键的访问：t('user.profile.name')\n     * - 支持复数形式：t('items.count', { count: 5 })\n     * - 添加格式化功能：日期、数字、货币等\n     */\n    const t = (key: string, _params?: TranslationParams): string => {\n      // 默认实现只返回 key 本身\n      // 实际项目中，这里应该根据当前语言返回对应的翻译\n      return key\n    }\n\n    return {\n      t,\n      /**\n       * 当前语言\n       *\n       * 🔧 扩展建议:\n       * - 从 Context、状态管理库或 localStorage 获取\n       * - 支持语言变化的监听和响应\n       * - 与浏览器语言设置联动\n       */\n      language: 'zh-CN',\n      /**\n       * 语言切换函数\n       *\n       * @param lang 要切换到的语言代码\n       *\n       * 🔧 扩展建议:\n       * - 更新全局语言状态\n       * - 持久化语言设置到 localStorage/cookie\n       * - 动态加载对应语言的翻译资源\n       * - 触发组件重新渲染\n       * - 通知其他依赖语言的组件更新\n       */\n      changeLanguage: () => {},\n    }\n    /**\n     * ⚠️  依赖项数组说明:\n     *\n     * 当前使用空数组 `[]`，意味着这个 hook 只会在组件挂载时执行一次。\n     * 在实际项目中，你需要根据选择的国际化方案添加适当的依赖项，\n     * 以确保语言切换时组件能够正确重新渲染。\n     *\n     * 常见的依赖项包括：\n     * - 当前语言: [currentLanguage]\n     * - 翻译资源: [translations]\n     * - i18next 实例: [i18n.language]\n     * - 状态管理的语言状态: [languageState]\n     *\n     * 📋 SSR 注意事项:\n     * - 确保服务端和客户端的初始语言一致\n     * - 避免在 SSR 期间访问 localStorage 或 document\n     * - 考虑使用 cookie 来传递语言偏好\n     */\n  }, [])\n}\n", "type": "registry:hook"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "76d5b267e03a922cff087cd9a51107475588acef"}, "generator": "imd-build-registry", "architectureLayer": "hooks", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-poptextarea"]}