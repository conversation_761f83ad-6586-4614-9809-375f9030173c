{"name": "ui-select", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-select/defined.tsx", "content": "import { tv } from 'tailwind-variants'\n\n// ------ border\n// background\n// border\n// color\n// ------ size\n// padding\n// fontSize\nexport const selectVariants = tv({\n  slots: {\n    trigger: [\n      'group/selecttrigger flex items-center justify-between',\n      'var(--roundeds) placeholder:text-imd-muted rounded-sm',\n      'w-full',\n      'cursor-pointer disabled:cursor-not-allowed',\n    ],\n    value: [\n      'group-disabled/selecttrigger:text-gray-8 max-w-full flex-1 truncate pl-1.5',\n    ],\n    content: [\n      'border-none p-1',\n      'bg-popover data-[state=open]:animate-in data-[state=closed]:animate-out',\n      'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',\n      'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      'shadow-2 z-imd-popover relative max-h-96 min-w-[8rem] overflow-hidden rounded-md border',\n    ],\n    icon: [\n      'hover:text-primary-primary text-imd-secondary mr-1 flex items-center justify-center',\n    ],\n    group: ['border-muted border-b pb-1.5 last:border-b-0', 'mb-1 last:mb-0'],\n    groupLabel: ['text-imd-muted mb-0.5'],\n    selectItem: [\n      // default\n      'relative flex w-full cursor-default select-none items-center rounded-sm outline-none',\n      'text-imd-primary',\n      'cursor-pointer',\n      // actived\n      'data-[actived=true]:bg-accent data-[actived=true]:bg-gray-1',\n      // disabled\n      // 'data-[disabled]:pointer-events-none\n      'data-[disabled]:text-imd-disabled',\n      'data-[disabled]:cursor-not-allowed',\n      'mb-0.5',\n    ],\n    multiValueTag: [\n      'flex',\n      'box-border border bg-white',\n      'flex items-center rounded-sm', // px-2 py-1',\n      // 'data-[disabled]:pointer-events-none',\n      'data-[disabled]:text-imd-disabled-on-gray',\n      'mr-1',\n      'text-nowrap',\n    ],\n  },\n  variants: {\n    type: {\n      single: {\n        selectItem: [\n          'data-[state=checked]:bg-primary-secondary data-[state=checked]:text-primary-primary data-[actived=true]:data-[state=checked]:bg-gray-1',\n        ],\n      },\n      multiple: {\n        selectItem: [],\n      },\n    },\n    size: {\n      small: {\n        trigger: ['typography-body-small h-6 px-0.5'],\n        value: ['padtypography-body-small'],\n        icon: ['h-[14px] w-[14px]'],\n        group: [],\n        groupLabel: ['typography-body-small px-2 py-1'],\n        selectItem: ['typography-body-small px-2 py-1'],\n        multiValueTag: ['typography-body-small h-5 px-1.5'],\n      },\n      medium: {\n        trigger: ['typography-body-small h-8 px-1'],\n        value: ['typography-body-small'],\n        icon: ['h-[14px] w-[14px]'],\n        group: [],\n        groupLabel: ['typography-body-small px-2 py-1'],\n        selectItem: ['typography-body-small px-2 py-1'],\n        multiValueTag: ['typography-body-small h-6 px-1.5'],\n      },\n      large: {\n        trigger: ['typography-body-medium h-[38px] px-1.5'],\n        value: ['typography-body-medium'],\n        icon: ['h-4 w-4'],\n        group: [],\n        groupLabel: ['typography-body-medium px-2 py-1.5'],\n        selectItem: ['typography-body-medium px-2 py-1.5'],\n        multiValueTag: ['typography-body-small h-7 px-1.5'],\n      },\n    },\n    variant: {\n      filled: {\n        trigger: [\n          // default\n          'bg-imd-container', // var(--roundeds) border group/selecttrigger',\n          'ring-offset-background border border-transparent',\n          // hover\n          'hover:border-primary-primary hover:bg-white',\n          // focus\n          'focus:border-primary-primary focus:bg-white',\n          'focus:ring-primary-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-0',\n          // open\n          'data-[open]:border-primary-primary data-[open]:bg-white',\n          'data-[open]:ring-primary-primary/20 data-[open]:outline-none data-[open]:ring-2 data-[open]:ring-offset-0',\n          // disabled\n          'disabled:bg-imd-component-disabled disabled:hover:bg-imd-component-disabled disabled:cursor-not-allowed disabled:hover:border-transparent', // disabled:opacity-50', // disabled:pointer-events-none',\n        ],\n        value: [\n          'text-left',\n          'data-[placeholder=true]:text-imd-muted',\n          'group-disabled/selecttrigger:data-[placeholder=true]:text-gray-8',\n        ],\n        group: [],\n        groupLabel: [],\n        selectItem: [],\n        multiValueTag: [\n          // default\n          'border-gray-3 rounded-xs bg-white',\n          // group hover / focus\n          'group-hover/selecttrigger:bg-imd-container group-focus/selecttrigger:bg-imd-container group-hover/selecttrigger:border-imd-border-component group-focus/selecttrigger:border-imd-border-component',\n          // group open\n          'group-data-[open]/selecttrigger:bg-imd-container group-data-[open]/selecttrigger:border-imd-border-component',\n          'hover:bg-imd-container hover:border-imd-border-component',\n          // disabled\n          // 'group-disabled/selecttrigger:hover:bg-imd-container '\n          'group-disabled/selecttrigger:text-imd-disabled-on-gray group-disabled/selecttrigger:pointer-events-none group-disabled/selecttrigger:cursor-not-allowed', // disabled:opacity-50', // disabled:pointer-events-none',\n          'group-disabled/selecttrigger:group-hover/selecttrigger:bg-white',\n        ],\n      },\n      outlined: {\n        trigger: [\n          // default\n          'border-gray-3 border bg-white', // var(--roundeds) border group/selecttrigger',\n          // hover\n          'hover:border-primary-primary',\n          // focus\n          'focus:border-primary-primary focus:bg-white',\n          'focus:ring-primary-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-0',\n          // open\n          'data-[open]:border-primary-primary data-[open]:bg-white',\n          'data-[open]:ring-primary-primary/20 data-[open]:outline-none data-[open]:ring-2 data-[open]:ring-offset-0',\n          // disabled\n          'disabled:bg-gray-1 disabled:hover:border-gray-3 disabled:cursor-not-allowed', // disabled:opacity-50', // disabled:pointer-events-none',\n          // form error\n          'data-[error=true]:bg-red-secondary data-[error=true]:hover:bg-red-secondary-hover data-[error=true]:focus:bg-red-secondary',\n        ],\n        value: [\n          'text-left',\n          'data-[placeholder=true]:text-imd-muted',\n          'group-disabled/selecttrigger:data-[placeholder=true]:text-imd-disabled',\n        ],\n        group: [],\n        groupLabel: [],\n        selectItem: [],\n        multiValueTag: [\n          // default\n          'border-imd-border-component rounded-xs bg-imd-container',\n          // group hover / focus\n          'group-hover/selecttrigger:bg-imd-container group-focus/selecttrigger:bg-imd-container group-hover/selecttrigger:border-imd-border-component group-focus/selecttrigger:border-imd-border-component',\n          // group open\n          'group-data-[open]/selecttrigger:bg-imd-container group-data-[open]/selecttrigger:border-imd-border-component',\n          'hover:bg-imd-container hover:border-imd-border-component',\n          // disabled\n          // 'group-disabled/selecttrigger:hover:bg-imd-container '\n          'group-disabled/selecttrigger:text-imd-disabled-on-gray group-disabled/selecttrigger:pointer-events-none group-disabled/selecttrigger:cursor-not-allowed', // disabled:opacity-50', // disabled:pointer-events-none',\n          'group-disabled/selecttrigger:group-hover/selecttrigger:bg-imd-container\t',\n        ],\n      },\n      ghost: {\n        trigger: [\n          // default\n          'bg-white', // var(--roundeds) border group/selecttrigger',\n          // hover\n          'hover:bg-imd-container',\n          // focus\n          'focus:bg-imileBlue-1',\n          // open\n          'data-[open]:bg-imileBlue-1',\n          // disabled\n          'disabled:cursor-not-allowed disabled:hover:border-transparent disabled:hover:bg-white', // disabled:opacity-50', // disabled:pointer-events-none',\n          // form error\n          'data-[error=true]:bg-red-secondary data-[error=true]:hover:bg-red-secondary-hover data-[error=true]:focus:bg-red-secondary',\n        ],\n        value: [\n          'text-center',\n          'data-[placeholder=true]:text-imd-muted',\n          \"data-[placeholder=true]:after:content-['-']\",\n          \"data-[placeholder=true]:before:content-['-']\",\n          'group-disabled/selecttrigger:data-[placeholder=true]:text-imd-disabled',\n        ],\n\n        group: [],\n        groupLabel: [],\n        selectItem: [],\n        multiValueTag: [\n          'border-gray-3 border bg-white',\n          'group-disabled/selecttrigger:border-gray-3 group-disabled/selecttrigger:text-imd-disabled-on-gray',\n        ],\n      },\n    },\n    hasError: {\n      true: {},\n      false: {},\n    },\n    position: {\n      popper: {\n        content:\n          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\n      }, // 当 position='popper' 时应用的基础样式\n      // 可以定义其他 position 值\n      'item-aligned': '',\n    },\n  },\n  compoundVariants: [\n    // hasError true, variant filled\n    {\n      hasError: true,\n      variant: 'filled',\n      class: {\n        trigger: [\n          'bg-red-secondary hover:border-red-primary hover:bg-white',\n          'focus:border-red-primary focus:ring-red-primary/20',\n          'data-[open]:border-red-primary data-[open]:ring-red-primary/20',\n        ], // Override filled styles for error\n      },\n    },\n    // hasError true, variant outlined\n    {\n      hasError: true,\n      variant: 'outlined',\n      class: {\n        trigger: [\n          'border-red-secondary-selected hover:bg-red-primary bg-white',\n          'focus:border-red-primary focus:ring-red-primary/20',\n          'data-[open]:border-red-primary data-[open]:ring-red-primary/20',\n        ],\n      },\n    },\n    // hasError true, variant ghost\n    {\n      hasError: true,\n      variant: 'ghost',\n      class: {\n        trigger: [\n          'bg-red-secondary hover:bg-red-secondary',\n          'data-[open]:bg-red-secondary',\n        ],\n      },\n    },\n  ],\n  defaultVariants: {\n    size: 'medium',\n    hasError: false,\n    variant: 'filled',\n    type: 'single',\n    position: 'popper',\n  },\n})\n\nexport const AdornmentTV = tv({\n  slots: {\n    adornment: ['flex items-center justify-center'],\n  },\n  variants: {\n    variant: {\n      contained: {\n        adornment: [\n          'bg-imd-container hover:bg-imd-component-disabled\tfocus:bg-imd-component-disabled',\n        ],\n      },\n      ghost: {\n        adornment: [],\n      },\n    },\n  },\n  defaultVariants: {\n    variant: 'contained',\n  },\n})\n\nexport const tvDefault = tv({\n  base: [],\n  variants: {\n    size: {\n      small: 'typography-body-small px-2 py-1',\n      medium: 'typography-body-small px-2 py-1',\n      large: 'typography-body-medium px-2 py-1.5',\n    },\n  },\n  defaultVariants: {\n    size: 'medium',\n    borderless: false,\n  },\n})\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/ClearIcon.tsx", "content": "/* eslint-disable */\nexport default () => (\n  <svg\n    width=\"16\"\n    height=\"16\"\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M8.00002 6.88893L4.1111 3L3 4.11111L6.88892 8.00003L3 11.8889L4.1111 13L8.00002 9.11114L11.8889 13L13 11.8889L9.11111 8.00003L13 4.11111L11.8889 3L8.00002 6.88893Z\"\n      fill=\"currentColor\"\n    />\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/CollapseIcon.tsx", "content": "/* eslint-disable */\nexport default () => (\n  <svg\n    width=\"14\"\n    height=\"14\"\n    viewBox=\"0 0 14 14\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M8.75 2.33338H7.58333V6.41673H11.6667V5.25005H9.57495L12.0791 2.74586L11.2542 1.9209L8.75 4.42509V2.33338ZM2.33338 8.75H4.42508L1.9209 11.2542L2.74585 12.0791L5.25005 9.57495V11.6667H6.41672V7.58333H2.33338V8.75Z\"\n      fill=\"#444757\"\n    />\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/DownIcon.tsx", "content": "/* eslint-disable */\nexport default () => (\n  <svg\n    width=\"16\"\n    height=\"16\"\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M4.10379 6.86193L7.74328 10.883C7.88408 11.039 8.11607 11.039 8.25686 10.883L11.8968 6.86193C12.1195 6.61554 11.9584 6.19995 11.6396 6.19995H4.36058C4.04179 6.19995 3.8802 6.61554 4.10379 6.86193Z\"\n      fill=\"currentColor\"\n    />\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/ExpandIcon.tsx", "content": "/* eslint-disable */\nexport default () => (\n  <svg\n    width=\"14\"\n    height=\"14\"\n    viewBox=\"0 0 14 14\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M10.2584 2.91667H8.16667V1.75H12.25V5.83333H11.0833V3.74162L8.57914 6.24581L7.75419 5.42085L10.2584 2.91667ZM1.75 8.16667H2.91667V10.2584L5.42085 7.75419L6.24581 8.57914L3.74162 11.0833H5.83333V12.25H1.75V8.16667Z\"\n      fill=\"#444757\"\n    />\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/index.tsx", "content": "export { default as ClearIcon } from './ClearIcon'\nexport { default as LoadingIcon } from './LoadingIcon'\nexport { default as DownIcon } from './DownIcon'\nexport { default as UpIcon } from './UpIcon'\nexport { default as ListIcon } from './ListIcon'\nexport { default as ExpandIcon } from './ExpandIcon'\nexport { default as CollapseIcon } from './CollapseIcon'\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/ListIcon.tsx", "content": "/* eslint-disable */\nexport default () => (\n  <svg\n    width=\"14\"\n    height=\"14\"\n    viewBox=\"0 0 14 14\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M5.83333 10.5H8.16667V9.33333H5.83333V10.5ZM1.75 3.5V4.66667H12.25V3.5H1.75ZM3.5 7.58333H10.5V6.41667H3.5V7.58333Z\"\n      fill=\"currentColor\"\n    />\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/LoadingIcon.tsx", "content": "/* eslint-disable */\nexport default () => (\n  <svg\n    width=\"14\"\n    height=\"14\"\n    viewBox=\"0 0 14 14\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M12 7A5 5 0 1 1 2 7\"\n      stroke=\"url(#loading-gradient)\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n    >\n      <animateTransform\n        attributeName=\"transform\"\n        type=\"rotate\"\n        from=\"0 7 7\"\n        to=\"360 7 7\"\n        dur=\"0.8s\"\n        repeatCount=\"indefinite\"\n      />\n    </path>\n    <defs>\n      <linearGradient\n        id=\"loading-gradient\"\n        x1=\"2\"\n        y1=\"7\"\n        x2=\"12\"\n        y2=\"7\"\n        gradientUnits=\"userSpaceOnUse\"\n      >\n        <stop offset=\"0\" stopColor=\"#2546FF\" />\n        <stop offset=\"1\" stopColor=\"#2546FF\" stopOpacity=\"0\" />\n      </linearGradient>\n    </defs>\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/UpIcon.tsx", "content": "/* eslint-disable */\nexport default () => (\n  <svg\n    width=\"16\"\n    height=\"16\"\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M4.10379 9.47138L7.74328 5.4503C7.88408 5.2943 8.11607 5.2943 8.25687 5.4503L11.8968 9.47138C12.1196 9.71777 11.9584 10.1334 11.6396 10.1334H4.36058C4.04179 10.1334 3.8802 9.71777 4.10379 9.47138Z\"\n      fill=\"currentColor\"\n    />\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/index.tsx", "content": "'use client'\n\nimport React from 'react'\nimport * as ScrollAreaPrimitive from '@imd/base-scroll-area'\nimport * as SelectPrimitive from '@imd/base-select'\nimport { usePageFilterContext } from '@imd/context'\nimport { ChevronDown, ChevronUp } from 'lucide-react'\n\nimport { cn } from '@/lib/utils'\nimport { useTranslation } from '@/registry/default/hooks/use-translation'\nimport { Checkbox } from '@/registry/default/ui/ui-checkbox'\nimport {\n  Ellipsis,\n  EllipsisContent,\n  EllipsisTooltip,\n} from '@/registry/default/ui/ui-ellipsis'\nimport {\n  Tooltip,\n  TooltipArrow,\n  TooltipContent,\n  TooltipPortal,\n  TooltipTrigger,\n} from '@/registry/default/ui/ui-tooltip'\n\nimport { AdornmentTV, selectVariants, tvDefault } from './defined'\nimport { ClearIcon, LoadingIcon } from './icon'\n\n// 用于样式传递各父子组件\nexport const SelectTvContext = React.createContext({})\n\nexport const useMergePropsWithContext = (\n  context: React.Context<any>,\n  props: any\n) => {\n  const c = React.useContext(context)\n\n  return { ...c, ...props }\n}\n/**\n * 选择器尺寸大小\n * - small: 小尺寸\n * - medium: 中尺寸\n * - large: 大尺寸\n */\nexport type SelectSize = 'small' | 'medium' | 'large'\n/**\n * 选择器边框样式变体\n * - outlined: 带边框样式\n * - filled: 填充样式\n * - ghost: 无边框透明样式\n */\nexport type BorderVariant = 'outlined' | 'filled' | 'ghost'\nconst Select = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Select>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Select> & {\n    /**\n     * 选择器尺寸\n     * @default 'medium'\n     */\n    size?: SelectSize\n    /**\n     * 选择器边框样式变体\n     * @default 'outlined'\n     */\n    variant?: BorderVariant\n    /**\n     * 是否存在错误状态，将显示错误样式\n     * @default false\n     */\n    hasError?: boolean\n  }\n>(({ size = 'medium', variant, hasError, ...props }, _ref) => {\n  const pageFilterContext = usePageFilterContext('ui-select')\n  const mergedVariant = pageFilterContext.variant || variant\n  return (\n    <SelectTvContext.Provider\n      value={{ size, variant: mergedVariant, hasError }}\n    >\n      <SelectPrimitive.Root\n        // className={cn(tvDefault({ size, borderVariant }))}\n        {...props}\n      />\n    </SelectTvContext.Provider>\n  )\n})\nSelect.displayName = SelectPrimitive.Root.displayName\n\nconst SelectGroup = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectGroup>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectGroup>\n>(({ className, ...props }, ref) => {\n  const _sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { group } = React.useMemo(() => {\n    return selectVariants({})\n  }, [])\n  return (\n    <SelectPrimitive.SelectGroup\n      ref={ref}\n      data-ui-slot=\"select-group\"\n      className={group({ class: className })}\n      {...props}\n    />\n  )\n})\nSelectGroup.displayName = SelectPrimitive.SelectGroup.displayName\n\nconst SelectValue = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Value>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Value>\n>(({ className, placeholder, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { value: valueTv } = React.useMemo(() => {\n    return selectVariants({\n      size: sProps.size,\n      variant: sProps.variant,\n    })\n  }, [sProps.size, sProps.variant])\n  const { t } = useTranslation()\n\n  return (\n    <SelectPrimitive.Value\n      ref={ref}\n      data-ui-slot=\"select-value\"\n      className={valueTv({ class: className })}\n      placeholder={placeholder ?? t('PleaseSelect')}\n      {...props}\n    />\n  )\n})\nSelectValue.displayName = SelectPrimitive.Value.displayName\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { trigger } = React.useMemo(() => {\n    return selectVariants({\n      size: sProps.size,\n      variant: sProps.variant,\n      hasError: sProps.hasError,\n    })\n  }, [sProps.size, sProps.variant, sProps.hasError])\n  return (\n    <SelectPrimitive.Trigger\n      ref={ref}\n      data-ui-slot=\"select-trigger\"\n      className={trigger({ class: className })}\n      {...props}\n    >\n      {children}\n    </SelectPrimitive.Trigger>\n  )\n})\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    data-ui-slot=\"select-scroll-up-button\"\n    className={cn(\n      'py-0.25 flex cursor-default items-center justify-center',\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    data-ui-slot=\"select-scroll-down-button\"\n    className={cn(\n      'py-0.25 flex cursor-default items-center justify-center',\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = 'popper', ...props }, ref) => {\n  const { content } = React.useMemo(() => {\n    return selectVariants({ position })\n  }, [position])\n\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        ref={ref}\n        data-ui-slot=\"select-content\"\n        className={content({ className })}\n        position={position}\n        {...props}\n      >\n        {children}\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n})\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { groupLabel } = React.useMemo(() => {\n    return selectVariants({\n      size: sProps.size,\n    })\n  }, [sProps.size])\n  return (\n    <SelectPrimitive.Label\n      ref={ref}\n      data-ui-slot=\"select-label\"\n      className={groupLabel({ class: className })}\n      {...props}\n    />\n  )\n})\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> & {\n    /**\n     * 选项前缀内容渲染函数\n     */\n    prefix?: React.ReactNode\n    /**\n     * 是否需要自定义渲染内容\n     * 设置为true时，children中必须包含ItemText\n     * @default false\n     */\n    needCustomRender?: boolean\n    /**\n     * 是否禁用省略\n     * @default false\n     */\n    disabledItemEllipsis?: boolean\n  }\n>(\n  (\n    {\n      className,\n      children,\n      prefix,\n      needCustomRender,\n      disabledItemEllipsis,\n      ...props\n    },\n    ref\n  ) => {\n    const sProps = useMergePropsWithContext(SelectTvContext, props)\n    const { selectItem: selectItemTv } = React.useMemo(() => {\n      return selectVariants({\n        size: sProps.size,\n      })\n    }, [sProps.size])\n    return (\n      <SelectPrimitive.Item\n        ref={ref}\n        data-ui-slot=\"select-item\"\n        className={selectItemTv({ className })}\n        {...props}\n      >\n        {prefix}\n        {needCustomRender ? (\n          children\n        ) : (\n          <ContentEllipsisNode>\n            <SelectPrimitive.ItemText\n              className={disabledItemEllipsis ? '' : 'truncate'}\n              highLightClassName={cn('text-primary-primary')}\n            >\n              {children}\n            </SelectPrimitive.ItemText>\n          </ContentEllipsisNode>\n        )}\n      </SelectPrimitive.Item>\n    )\n  }\n)\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectItemText = SelectPrimitive.ItemText\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    data-ui-slot=\"select-separator\"\n    className={cn('bg-imd-muted mx-1 my-0.5 h-px', className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nconst SelectMultiItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> & {\n    prefix?: React.ReactNode\n    /*\n     * needCustomRender: 默认false，使用ItemText包裹value\n     * 设置为true 表示需要自定义内容，，且children中必须包含ItemText\n     */\n    needCustomRender?: boolean\n    /**\n     * 是否禁用省略\n     * @default false\n     */\n    disabledItemEllipsis?: boolean\n  }\n>(\n  (\n    {\n      className,\n      children,\n      prefix,\n      needCustomRender,\n      disabledItemEllipsis,\n      ...props\n    },\n    ref\n  ) => {\n    const sProps = useMergePropsWithContext(SelectTvContext, props)\n    const { selectItem: selectItemTv } = React.useMemo(() => {\n      return selectVariants({\n        size: sProps.size,\n        type: 'multiple',\n      })\n    }, [sProps.size])\n    return (\n      <SelectPrimitive.SelectMultiItem\n        ref={ref}\n        data-ui-slot=\"select-multi-item\"\n        className={selectItemTv({ class: className })}\n        {...props}\n      >\n        <SelectPrimitive.SelectMultiItemIndicator>\n          <Checkbox className=\"mr-2\" />\n        </SelectPrimitive.SelectMultiItemIndicator>\n        {prefix}\n        {needCustomRender ? (\n          children\n        ) : (\n          <ContentEllipsisNode>\n            <SelectPrimitive.ItemText\n              className={disabledItemEllipsis ? '' : 'truncate'}\n              highLightClassName={cn('text-primary-primary')}\n            >\n              {children}\n            </SelectPrimitive.ItemText>\n          </ContentEllipsisNode>\n        )}\n      </SelectPrimitive.SelectMultiItem>\n    )\n  }\n)\nSelectMultiItem.displayName = SelectPrimitive.SelectMultiItem.displayName\n\nconst ItemText = SelectPrimitive.ItemText\n\nconst SelectSearch = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectSearch>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectSearch> & {\n    needSearch?: boolean\n  }\n>(({ className, children, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n\n  return (\n    <SelectPrimitive.SelectSearch\n      ref={ref}\n      data-ui-slot=\"select-search\"\n      className={cn(\n        tvDefault({ size: sProps.size }),\n        'text-imd-primary\t',\n        'flex items-center pt-0.5 [&>input]:ml-1',\n        'p [&>input]:w-full',\n        '[&>input]:border-none [&>input]:outline-none [&>input]:focus:outline-none [&>input]:focus:ring-0',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </SelectPrimitive.SelectSearch>\n  )\n})\nSelectSearch.displayName = SelectPrimitive.SelectSearch.displayName\n\nconst SelectMultiValue = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectMultiValue>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectMultiValue> & {\n    tagEllipsis?: boolean\n    children?: React.ReactNode\n    hasPrefix?: boolean\n  }\n>(\n  (\n    {\n      className,\n      tagEllipsis = true,\n      hideCountTooltips,\n      children,\n      hasPrefix,\n      ...props\n    },\n    ref\n  ) => {\n    const sProps = useMergePropsWithContext(SelectTvContext, props)\n    const { multiValueTag, value: valueTv } = React.useMemo(() => {\n      return selectVariants({\n        size: sProps.size,\n        variant: sProps.variant,\n      })\n    }, [sProps.size, sProps.variant])\n    return (\n      <SelectPrimitive.SelectMultiValue\n        ref={ref}\n        data-ui-slot=\"select-multi-value\"\n        className={valueTv({ className })}\n        {...props}\n      >\n        {children ? (\n          children\n        ) : (\n          <TagList\n            size={sProps.size}\n            variant={sProps.variant}\n            tagEllipsis={tagEllipsis}\n            hideCountTooltips={hideCountTooltips}\n            className={hasPrefix ? 'ml-1.5' : ''}\n          />\n        )}\n      </SelectPrimitive.SelectMultiValue>\n    )\n  }\n)\nSelectMultiValue.displayName = SelectPrimitive.SelectMultiValue.displayName\n\nconst SelectMultiAllItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectMultiAllItem>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectMultiAllItem>\n>(({ className, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { selectItem } = React.useMemo(() => {\n    return selectVariants({\n      size: sProps.size,\n      type: 'multiple',\n    })\n  }, [sProps.size])\n  return (\n    <SelectPrimitive.SelectMultiAllItem\n      ref={ref}\n      data-ui-slot=\"select-multi-all-item\"\n      className={selectItem({ class: className })}\n      indicatorRender={(checked) => {\n        return <Checkbox className=\"mr-2\" checked={checked} />\n      }}\n      {...props}\n    ></SelectPrimitive.SelectMultiAllItem>\n  )\n})\nSelectMultiAllItem.displayName = SelectPrimitive.SelectMultiAllItem.displayName\n\nconst SelectNoData = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectNoData>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectNoData>\n>(({ className, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { t } = useTranslation()\n\n  return (\n    <SelectPrimitive.SelectNoData\n      ref={ref}\n      data-ui-slot=\"select-no-data\"\n      className={cn(\n        tvDefault({ size: sProps.size }),\n        'text-imd-muted flex flex-col items-center justify-center',\n        className\n      )}\n      {...props}\n    >\n      <SelectPrimitive.SelectNoDataIcon />\n      <div>{t('No Data')}</div>\n    </SelectPrimitive.SelectNoData>\n  )\n})\n\nSelectNoData.displayName = SelectPrimitive.SelectNoData.displayName\n\nconst SelectLoading = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectLoading>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectLoading>\n>(({ className, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { t } = useTranslation()\n\n  return (\n    <SelectPrimitive.SelectLoading\n      ref={ref}\n      data-ui-slot=\"select-loading\"\n      className={cn(\n        tvDefault({ size: sProps.size }),\n        'text-imd-primary\t flex items-center justify-center',\n        className\n      )}\n      {...props}\n    >\n      <LoadingIcon />\n      <div className=\"ml-2\">{t('Loading')}</div>\n    </SelectPrimitive.SelectLoading>\n  )\n})\n\nSelectLoading.displayName = SelectPrimitive.SelectLoading.displayName\n\nconst SelectSearchNoData = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectNoData>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectNoData>\n>(({ className, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { t } = useTranslation()\n\n  return (\n    <SelectPrimitive.SelectNoData\n      ref={ref}\n      data-ui-slot=\"select-search-no-data\"\n      className={cn(\n        tvDefault({ size: sProps.size }),\n        'text-imd-muted\tflex flex-col items-center justify-center',\n        className\n      )}\n      {...props}\n    >\n      <SelectPrimitive.SelectSearchNoDataIcon />\n      <div>{t('No Data')}</div>\n    </SelectPrimitive.SelectNoData>\n  )\n})\nSelectSearchNoData.displayName = SelectPrimitive.SelectNoData.displayName\n\nconst SelectIcon = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectIcon>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectIcon>\n>(({ className, ...props }, ref) => {\n  const sProps = useMergePropsWithContext(SelectTvContext, props)\n  const { icon } = React.useMemo(() => {\n    return selectVariants({\n      variant: sProps.variant,\n      size: sProps.size,\n    })\n  }, [sProps.variant, sProps.size])\n\n  // 根据类型定义，这个组件不接受 loadingIcon 和 clearIcon 属性，需要从 props 中提取出来\n  const { ...restProps } = props as any\n\n  return (\n    <SelectPrimitive.SelectIcon\n      ref={ref}\n      data-ui-slot=\"select-icon\"\n      className={icon({ class: className })}\n      {...restProps}\n    >\n      {props.children}\n    </SelectPrimitive.SelectIcon>\n  )\n})\nSelectIcon.displayName = SelectPrimitive.SelectIcon.displayName\n\nconst SelectAdornment = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectAdornment>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectAdornment> & {\n    variant?: 'contained' | 'ghost'\n  }\n>(({ className, variant = 'contained', ...props }, ref) => {\n  const { adornment } = React.useMemo(() => {\n    return AdornmentTV({\n      variant: variant,\n    })\n  }, [variant])\n\n  const { ...restProps } = props as any\n\n  return (\n    <SelectPrimitive.SelectAdornment\n      ref={ref}\n      data-ui-slot=\"select-prefix\"\n      className={adornment({ class: className })}\n      {...restProps}\n    />\n  )\n})\nSelectAdornment.displayName = SelectPrimitive.SelectAdornment.displayName\n\n// --------------scroll area------ start ---------\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = 'vertical', ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    data-ui-slot=\"scroll-bar\"\n    orientation={orientation}\n    className={cn(\n      'flex touch-none select-none transition-colors',\n      orientation === 'vertical' &&\n        'h-full w-2.5 border-l border-l-transparent p-[1px]',\n      orientation === 'horizontal' &&\n        'h-2.5 flex-col border-t border-t-transparent p-[1px]',\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb\n      data-ui-slot=\"scroll-area-thumb\"\n      className=\"bg-border relative flex-1 rounded-full\"\n    />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nconst SelectScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    data-ui-slot=\"select-scroll-area\"\n    className={cn('relative w-full overflow-hidden', className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport\n      data-ui-slot=\"scroll-area-viewport\"\n      className=\"h-full max-h-80 w-full rounded-[inherit]\"\n    >\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner data-ui-slot=\"scroll-area-corner\" />\n  </ScrollAreaPrimitive.Root>\n))\nSelectScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\n// --------------scroll area------- end -------------\n\nconst SelectViewport = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Viewport>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Viewport>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Viewport\n    ref={ref}\n    data-ui-slot=\"select-viewport\"\n    className={cn(\n      'p-0',\n      'h-[var(--radix-select-trigger-height)]', // min-w-[var(--radix-select-trigger-width)]'\n      'overflow-y-hidden',\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </SelectPrimitive.Viewport>\n))\nSelectViewport.displayName = SelectPrimitive.Viewport.displayName\n\nconst CustomScroller = ({ ...props }) => {\n  return (\n    <div\n      className={cn(\n        '[&::-webkit-scrollbar]:w-2',\n        '[&::-webkit-scrollbar-track]:bg-transparent',\n        '[&::-webkit-scrollbar-thumb]:bg-gray-4',\n        '[&::-webkit-scrollbar-thumb]:w-1',\n        '[&::-webkit-scrollbar-thumb]:rounded-full'\n      )}\n      {...props}\n    />\n  )\n}\n\nCustomScroller.displayName = 'CustomScroller'\n\nconst SelectVirtuoso = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.SelectVirtuoso>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.SelectVirtuoso>\n>(({ style, children, ...props }, ref) => (\n  <SelectPrimitive.SelectVirtuoso\n    ref={ref}\n    data-ui-slot=\"select-Virtuoso\"\n    style={{ height: '300px', ...style }}\n    components={{\n      Scroller: CustomScroller,\n    }}\n    {...props}\n  />\n))\nSelectVirtuoso.displayName = SelectPrimitive.SelectVirtuoso.displayName\n\nconst SelectViewportSticky = SelectPrimitive.SelectViewportSticky\n\nconst SelectTagItem = ({\n  className,\n  item,\n  index,\n  onItemClose,\n  tagEllipsis,\n}: {\n  className?: string\n  item?: any\n  index?: number\n  onItemClose?: (item?: any, index?: number) => void\n  tagEllipsis?: boolean\n}) => {\n  if (!item) {\n    return\n  }\n  const content = item?.label || item?.value\n  return (\n    <div\n      className={className}\n      // 'px-1 rounded-sm flex items-center bg-white border'}\n      data-disabled={item?.disabled}\n      key={index}\n    >\n      {tagEllipsis ? (\n        <Ellipsis content={content}>\n          <EllipsisTooltip>\n            <EllipsisContent className=\"max-w-[80px]\" />\n          </EllipsisTooltip>\n        </Ellipsis>\n      ) : (\n        <span className=\"text-nowrap\">{content}</span>\n      )}\n      <span\n        onClick={(event) => {\n          if (item?.disabled) {\n            return\n          }\n          onItemClose && onItemClose(item, index)\n          event?.preventDefault()\n          event.stopPropagation()\n        }}\n        className={cn(\n          'hover:text-imileBlue-primary ml-1.5 flex w-[10px] items-center justify-center',\n          item?.disabled ? 'pointer-events-none' : ''\n        )}\n      >\n        <ClearIcon />\n      </span>\n    </div>\n  )\n}\n\nconst SelectTagCount = ({\n  className,\n  length,\n  hiddenItems,\n}: {\n  className?: string\n  length?: number\n  hiddenItems?: any[]\n}) => (\n  <Tooltip>\n    <TooltipTrigger asChild>\n      <span className={className}>\n        {/* \"px-1 rounded-sm bg-white border\"> */}+ {length}\n      </span>\n    </TooltipTrigger>\n    <TooltipPortal>\n      <TooltipContent>\n        {hiddenItems?.map((e) => e?.label || e?.value)?.join(',')}\n        <TooltipArrow />\n      </TooltipContent>\n    </TooltipPortal>\n  </Tooltip>\n)\n\nconst TagList = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.TagListRoot>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.TagListRoot> & {\n    size?: SelectSize\n    variant?: BorderVariant\n    tagEllipsis?: boolean\n    children?: React.ReactNode\n  }\n>(({ size, variant, children, tagEllipsis = true, ...props }, _ref) => {\n  const { multiValueTag } = React.useMemo(() => {\n    return selectVariants({\n      size,\n      variant,\n    })\n  }, [size, variant])\n\n  const [itemTemplate, countTemplate] = React.useMemo(() => {\n    const childrenArray = React.Children.toArray(children)\n    const item = childrenArray.find(\n      (child: any) => child.type.displayName === 'TagListItem'\n    )\n    const count = childrenArray.find(\n      (child: any) => child.type.displayName === 'TagListCount'\n    )\n    return [item, count]\n  }, [children])\n\n  return (\n    <SelectPrimitive.TagListRoot {...props}>\n      {itemTemplate ? (\n        itemTemplate\n      ) : (\n        <SelectPrimitive.TagListItem>\n          <SelectTagItem\n            className={multiValueTag({})}\n            tagEllipsis={tagEllipsis}\n          />\n        </SelectPrimitive.TagListItem>\n      )}\n      {countTemplate ? (\n        countTemplate\n      ) : (\n        <SelectPrimitive.TagListCount>\n          <SelectTagCount className={multiValueTag({})} />\n        </SelectPrimitive.TagListCount>\n      )}\n    </SelectPrimitive.TagListRoot>\n  )\n})\nTagList.displayName = 'TagList'\n\nconst ContentEllipsisTooltip = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ContentEllipsisTooltip>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ContentEllipsisTooltip>\n>(({ className, ...props }, ref) => {\n  return (\n    <SelectPrimitive.ContentEllipsisTooltip\n      ref={ref}\n      className={cn(\n        'text-popover-foreground bg-gray-10 z-imd-tooltip typography-body-small max-w-[400px] overflow-hidden break-words rounded-md px-1.5 py-1 shadow-md',\n        className\n      )}\n      side=\"top\"\n      sideOffset={1}\n      {...props}\n    />\n  )\n})\nContentEllipsisTooltip.displayName =\n  SelectPrimitive.ContentEllipsisTooltip.displayName\n\nconst ContentEllipsis = SelectPrimitive.ContentEllipsis\nconst ContentEllipsisNode = SelectPrimitive.ContentEllipsisNode\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSearch,\n  SelectMultiItem,\n  SelectMultiValue,\n  ItemText,\n  SelectMultiAllItem,\n  SelectViewport,\n  SelectIcon,\n  SelectNoData,\n  SelectLoading,\n  SelectSearchNoData,\n  SelectViewportSticky,\n  SelectVirtuoso,\n  TagList,\n  SelectScrollArea,\n  ContentEllipsis,\n  ContentEllipsisTooltip,\n  ContentEllipsisNode,\n  // Aliases\n  Select as Root,\n  SelectGroup as Group,\n  SelectValue as Value,\n  SelectTrigger as Trigger,\n  SelectContent as Content,\n  SelectLabel as Label,\n  SelectItem as Item,\n  SelectSeparator as Separator,\n  SelectScrollUpButton as ScrollUpButton,\n  SelectScrollDownButton as ScrollDownButton,\n  SelectSearch as Search,\n  SelectMultiItem as MultiItem,\n  SelectMultiValue as MultiValue,\n  SelectMultiAllItem as MultiAllItem,\n  SelectViewport as Viewport,\n  SelectIcon as Icon,\n  SelectAdornment as Adornment,\n  SelectNoData as NoData,\n  SelectLoading as Loading,\n  SelectSearchNoData as SearchNoData,\n  SelectViewportSticky as ViewportSticky,\n  SelectVirtuoso as Virtuoso,\n  SelectScrollArea as ScrollArea,\n}\n", "type": "registry:ui"}], "dependencies": ["@imd/base-scroll-area", "@imd/base-select", "@imd/context", "lucide-react"], "registryDependencies": ["ui-checkbox", "ui-ellipsis", "ui-tooltip"], "tags": ["dropdown", "picker", "select", "下拉选择", "选择器"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "5eeb50684b0360a51daaca195169700fd22fc0ea"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 3, "instability": 0.75}, "dependents": ["pro-select"]}