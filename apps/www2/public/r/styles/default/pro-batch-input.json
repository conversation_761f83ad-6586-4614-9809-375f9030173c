{"name": "pro-batch-input", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-batch-input/index.tsx", "content": "'use client'\n\n/**\n * 批量输入组件，结合 TextField 和 Poptextarea\n */\nimport React from 'react'\n\nimport {\n  Poptextarea,\n  splitBatchValue,\n  type PoptextareaProps,\n} from '@/registry/default/ui/pro-poptextarea'\nimport {\n  TextField,\n  type TextFieldProps,\n} from '@/registry/default/ui/pro-textField'\n\ninterface ProBatchInputProps extends Omit<TextFieldProps, 'suffix'> {\n  /** 批量输入确认回调 */\n  onConfirm?: (items: string[], rawValue: string) => void\n  /** 批量输入取消回调 */\n  onCancel?: () => void\n  /** 批量输入清除回调 */\n  onBatchClear?: () => void\n  /** 自定义分割函数 */\n  customSplitBatchValue?: (input: string) => string[]\n  /** 弹窗文本域属性 */\n  poptextareaProps?: Pick<\n    PoptextareaProps,\n    | 'trigger'\n    | 'placeholder'\n    | 'popoverContentProps'\n    | 'clearText'\n    | 'cancelText'\n    | 'confirmText'\n  >\n}\n\nconst ProBatchInput = React.forwardRef<HTMLInputElement, ProBatchInputProps>(\n  (props, ref) => {\n    const {\n      onConfirm,\n      onCancel,\n      onBatchClear,\n      customSplitBatchValue,\n      value,\n      defaultValue,\n      onChange,\n      poptextareaProps,\n      ...textFieldProps\n    } = props\n\n    const isControlled = value !== undefined\n    const [internalBatchValue, setInternalBatchValue] = React.useState(\n      defaultValue?.toString() || ''\n    )\n    const [popoverValue, setPopoverValue] = React.useState('')\n\n    const finalTextFieldValue = isControlled\n      ? value?.toString() || ''\n      : internalBatchValue\n\n    // 当弹窗打开时，将 TextField 的值回显到弹窗 textarea\n    const handlePopoverOpen = (open: boolean) => {\n      if (open) {\n        setPopoverValue(finalTextFieldValue)\n      }\n    }\n\n    // 处理批量输入确认\n    const handleBatchConfirm = (items: string[], rawValue: string) => {\n      onConfirm?.(items, rawValue)\n\n      if (isControlled) {\n        const syntheticEvent = {\n          target: { value: rawValue },\n          currentTarget: { value: rawValue },\n        } as React.ChangeEvent<HTMLInputElement>\n        onChange?.(syntheticEvent)\n      } else {\n        setInternalBatchValue(rawValue)\n      }\n    }\n\n    // 处理批量输入取消\n    const handleBatchCancel = () => {\n      onCancel?.()\n    }\n\n    // 处理批量输入清除\n    const handleBatchClear = () => {\n      onBatchClear?.()\n      // 清空弹窗内容\n      setPopoverValue('')\n    }\n\n    // 处理批量输入值变化\n    const handleBatchChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n      // 只更新弹窗的状态，不影响 TextField\n      setPopoverValue(e.target.value)\n    }\n\n    // 处理 TextField 值变化\n    const handleTextFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n      onChange?.(e)\n\n      // 非受控模式下同步更新内部状态\n      if (!isControlled) {\n        setInternalBatchValue(e.target.value)\n      }\n    }\n\n    return (\n      <TextField\n        {...textFieldProps}\n        value={finalTextFieldValue}\n        onChange={handleTextFieldChange}\n        ref={ref}\n        suffix={\n          <Poptextarea\n            value={popoverValue}\n            onChange={handleBatchChange}\n            onConfirm={handleBatchConfirm}\n            onCancel={handleBatchCancel}\n            onClear={handleBatchClear}\n            customSplitBatchValue={customSplitBatchValue}\n            onOpenChange={handlePopoverOpen}\n            {...poptextareaProps}\n          />\n        }\n      />\n    )\n  }\n)\n\nProBatchInput.displayName = 'ProBatchInput'\n\nexport { ProBatchInput, splitBatchValue }\nexport type { ProBatchInputProps }\n", "type": "registry:ui"}], "registryDependencies": ["pro-button", "pro-poptextarea", "pro-textField"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "e985dbe3995ecab41158e5abc6b261a04a7d678d"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 3, "instability": 1}}