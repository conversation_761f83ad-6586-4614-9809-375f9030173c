{"name": "ui-input-number", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-input-number/icon.tsx", "content": "export const ClearIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      viewBox=\"0 0 16 16\"\n      fill=\"currentColor\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path d=\"M8.00002 6.88893L4.1111 3L3 4.11111L6.88892 8.00003L3 11.8889L4.1111 13L8.00002 9.11114L11.8889 13L13 11.8889L9.11111 8.00003L13 4.11111L11.8889 3L8.00002 6.88893Z\" />\n    </svg>\n  )\n}\n\nexport const ArrowUpIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      width=\"8px\"\n      height=\"6px\"\n      viewBox=\"0 0 8 6\"\n      fill=\"currentColor\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path d=\"M3.99963 2.31653L6.88696 5.20391L7.71191 4.37896L3.99963 0.666626L0.2873 4.37896L1.11225 5.20391L3.99963 2.31653Z\" />\n    </svg>\n  )\n}\n\nexport const ArrowDownIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      width=\"8px\"\n      height=\"6px\"\n      viewBox=\"0 0 8 6\"\n      fill=\"currentColor\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path d=\"M3.99963 3.68336L1.11225 0.796021L0.2873 1.62098L3.99963 5.33332L7.71191 1.62098L6.88696 0.796021L3.99963 3.68336Z\" />\n    </svg>\n  )\n}\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-input-number/index.tsx", "content": "'use client'\n\nimport React, {\n  useCallback,\n  useContext,\n  useMemo,\n  useRef,\n  useState,\n} from 'react'\nimport { usePageFilterContext } from '@imd/context'\nimport IMask from 'imask'\nimport { tv } from 'tailwind-variants'\n\nimport { ArrowDownIcon, ArrowUpIcon, ClearIcon } from './icon'\n\n/** 输入框大小 */\ntype Size = 'small' | 'medium' | 'large'\n\n/** 输入框变体 */\ntype Variant = 'outlined' | 'filled' | 'ghost'\n\nconst inputNumberVariants = tv({\n  slots: {\n    root: [\n      'group/inputNumber-root',\n      // base\n      'relative inline-flex  overflow-hidden rounded-sm border',\n      // 间隔线\n      '[&>:not([hidden]):not(:first-child)]:border-gray-3 [&>:not([hidden]):not(:first-child)]:border-l',\n      // hover\n      'hover:[&:not(&:has(input:disabled))]:border-primary-6 hover:[&:not(&:has(input:disabled))]:bg-white',\n      // focus\n      'has-[input:focus]:shadow-component-focus has-[input:focus]:border-primary-6 has-[input:focus]:bg-white',\n      // disabled\n      'has-[input:disabled]:shadow-none has-[input:disabled]:cursor-not-allowed',\n      'has-[input:disabled]:bg-gray-4 has-[input:disabled]:border-gray-4',\n      'has-[input:disabled]:[&>:not([hidden]):not(:first-child)]:border-gray-5',\n    ],\n    affix: ['flex items-center justify-items-center', 'text-gray-13'],\n    inlineAffix: ['text-gray-13 whitespace-nowrap'],\n    wrapper: [\n      'group/inputNumber-wrapper',\n      'flex flex-1 items-center',\n      'has-[input:disabled]:pointer-events-none',\n    ],\n    // 清除按钮\n    clear: [\n      'hidden cursor-pointer transition-colors',\n      'hover:text-primary-6',\n      'group-has-[input:focus]/inputNumber-root:block group-hover/inputNumber-root:block',\n    ],\n    icon: ['text-gray-10 flex-shrink-0'],\n    input: [\n      // base\n      'text-gray-13 placeholder:text-gray-7 w-full bg-transparent bg-none outline-none',\n    ],\n    control: [\n      'hidden w-[24px] flex-col',\n      'divide-gray-3 divide-y',\n      'group-has-[input:focus]/inputNumber-root:flex group-hover/inputNumber-root:flex',\n      'group-has-[input:disabled]/inputNumber-root:hidden',\n    ],\n    decrementTrigger: [\n      'flex flex-1 cursor-pointer items-center justify-center',\n      'hover:bg-gray-3',\n    ],\n    incrementTrigger: [\n      'flex flex-1 cursor-pointer items-center justify-center',\n      'hover:bg-gray-3',\n    ],\n  },\n  variants: {\n    size: {\n      small: {\n        root: 'h-6 w-60',\n        affix: 'typography-body-small px-2',\n        inlineAffix: 'typography-body-small',\n        wrapper: 'gap-x-1 px-2',\n        icon: 'size-3.5',\n        input: 'typography-body-small',\n      },\n      medium: {\n        root: 'w-70 h-8',\n        affix: 'typography-body-small px-3',\n        inlineAffix: 'typography-body-small',\n        wrapper: 'gap-x-2 px-3',\n        icon: 'size-4',\n        input: 'typography-body-small',\n      },\n      large: {\n        // TODO: 暂时写死38px, 不按照space来计算\n        root: 'h-[38px] w-80',\n        affix: 'typography-body-medium px-3',\n        inlineAffix: 'typography-body-medium',\n        wrapper: 'gap-x-2 px-3',\n        icon: 'size-3.5',\n        input: 'typography-body-medium',\n      },\n    },\n    variant: {\n      outlined: {\n        root: ['border-gray-3 has-[input:disabled]:bg-gray-1 bg-white'],\n        affix: [\n          // disabled\n          'group-has-[input:disabled]/inputNumber-root:text-gray-4',\n        ],\n        inlineAffix: [\n          // disabled\n          'group-has-[input:disabled]/inputNumber-root:text-gray-4',\n        ],\n        icon: [\n          // disabled\n          'group-has-[input:disabled]/inputNumber-root:text-gray-4',\n        ],\n        input: [\n          // disabled\n          'disabled:text-gray-5 disabled:placeholder:text-gray-5',\n        ],\n      },\n      filled: {\n        root: ['bg-gray-2 border-gray-2'],\n        affix: [\n          // disabled\n          'group-has-[input:disabled]/inputNumber-root:text-gray-8',\n        ],\n        inlineAffix: [\n          // disabled\n          'group-has-[input:disabled]/inputNumber-root:text-gray-8',\n        ],\n        icon: [\n          // disabled\n          'group-has-[input:disabled]/inputNumber-root:text-gray-8',\n        ],\n        input: [\n          // disabled\n          'disabled:text-gray-8 disabled:placeholder:text-gray-8 ',\n        ],\n      },\n      ghost: {\n        root: ['border-transparent bg-transparent'],\n      },\n    },\n    hasError: {\n      true: {\n        root: [\n          '[&:not(&:has(input:disabled))]:bg-red-1 [&:not(&:has(input:disabled))]:border-red-1',\n          'hover:[&:not(&:has(input:disabled))]:border-red-6',\n          'has-[input:focus]:shadow-component-focus-error has-[input:focus]:border-red-6',\n        ],\n      },\n      false: {},\n    },\n  },\n  defaultVariants: {\n    size: 'medium',\n    hasError: false,\n    variant: 'filled',\n  },\n})\n\ninterface ImaskOptions {\n  /** 最小值 */\n  min?: number\n  /** 最大值 */\n  max?: number\n  /** 小数位数 */\n  scale?: number\n  /** 千分位分隔符 */\n  thousandsSeparator?: string\n  /** 小数点的分隔符 */\n  radix?: string\n}\n\n/**\n * IMask pipes 自定义 hook，优化性能避免重复创建实例\n */\nconst useMaskPipes = (imaskOptions: ImaskOptions = {}) => {\n  return useMemo(() => {\n    const config = {\n      mask: Number,\n      min: imaskOptions.min ?? Number.MIN_SAFE_INTEGER,\n      max: imaskOptions.max ?? Number.MAX_SAFE_INTEGER,\n      scale: imaskOptions.scale ?? 0,\n      thousandsSeparator: imaskOptions.thousandsSeparator ?? '',\n      radix: imaskOptions.radix ?? '.',\n    }\n\n    return {\n      maskPipe: IMask.createPipe(config),\n      unmaskPipe: IMask.createPipe(\n        config,\n        IMask.PIPE_TYPE.MASKED,\n        IMask.PIPE_TYPE.TYPED\n      ),\n    }\n  }, [\n    // 使用具体值作为依赖，避免对象引用问题\n    imaskOptions.min,\n    imaskOptions.max,\n    imaskOptions.scale,\n    imaskOptions.thousandsSeparator,\n    imaskOptions.radix,\n  ])\n}\n\nconst InputNumberContext = React.createContext<{\n  size?: Size\n  allowClear?: boolean\n  incrementRef?: React.MutableRefObject<(() => void) | undefined>\n  decrementRef?: React.MutableRefObject<(() => void) | undefined>\n  step?: number\n  imaskOptions?: ImaskOptions\n  value?: number | string\n  formItemId?: string\n  onChange?: (value: number | null) => void\n  variant?: Variant\n}>({})\n\ninterface RootProps\n  extends ImaskOptions,\n    Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {\n  /** 输入框大小 */\n  size?: Size\n  /** 是否显示清空图标 */\n  allowClear?: boolean\n  /** 是否显示错误状态 */\n  hasError?: boolean\n  /** 点击增减按钮时的步长 */\n  step?: number\n  /** 输入框的值 */\n  value?: number | string\n  /** 值变化时的回调函数 */\n  onChange?: (value: number | null) => void\n  /** 表单项ID */\n  'data-form-item-id'?: string\n  /** 输入框的变体 */\n  variant?: Variant\n}\n\nconst InputNumber = React.forwardRef<HTMLDivElement, RootProps>(\n  (\n    {\n      className,\n      size = 'medium',\n      allowClear = false,\n      hasError = false,\n      min,\n      max,\n      scale = 0,\n      thousandsSeparator = '',\n      radix = '.',\n      step = 1,\n      children,\n      value,\n      onChange,\n      'data-form-item-id': formItemId,\n      variant,\n      ...rest\n    },\n    ref\n  ) => {\n    const Component = 'div'\n    const incrementRef = useRef<() => void>()\n    const decrementRef = useRef<() => void>()\n    const pageFilterContext = usePageFilterContext('ui-input-number')\n    const mergedVariant = pageFilterContext.variant || variant\n    const { root } = useMemo(() => {\n      return inputNumberVariants({\n        size,\n        hasError,\n        variant: mergedVariant,\n      })\n    }, [size, hasError, mergedVariant])\n\n    const contextValue = useMemo(\n      () => ({\n        size,\n        allowClear,\n        incrementRef,\n        decrementRef,\n        step,\n        imaskOptions: {\n          min,\n          max,\n          scale,\n          thousandsSeparator,\n          radix,\n        },\n        value,\n        onChange,\n        formItemId,\n        variant: mergedVariant,\n      }),\n      [\n        size,\n        allowClear,\n        step,\n        min,\n        max,\n        scale,\n        thousandsSeparator,\n        radix,\n        value,\n        onChange,\n        formItemId,\n        mergedVariant,\n      ]\n    )\n\n    return (\n      <InputNumberContext.Provider value={contextValue}>\n        <Component\n          ref={ref}\n          className={root({ class: className })}\n          data-ui-slot=\"input-number\"\n          {...rest}\n        >\n          {children}\n        </Component>\n      </InputNumberContext.Provider>\n    )\n  }\n)\n\nInputNumber.displayName = 'InputNumberRoot'\n\ninterface WrapperProps extends React.HTMLAttributes<HTMLLabelElement> {\n  /** 应用于包装容器的自定义 CSS 类 */\n  className?: string\n  /** 包装容器的内容 */\n  children?: React.ReactNode\n}\n\nconst InputNumberWrapper = React.forwardRef<HTMLLabelElement, WrapperProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'label'\n    const { size } = useContext(InputNumberContext)\n    const { wrapper } = useMemo(() => {\n      return inputNumberVariants({\n        size,\n      })\n    }, [size])\n\n    return (\n      <Component\n        ref={ref}\n        className={wrapper({ class: className })}\n        data-ui-slot=\"input-number-wrapper\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nInputNumberWrapper.displayName = 'InputNumberWrapper'\n\ninterface InputProps\n  extends Omit<\n    React.InputHTMLAttributes<HTMLInputElement>,\n    'type' | 'onChange' | 'value' | 'defaultValue'\n  > {\n  /** 值变化时的回调函数 */\n  onChange?: (value: number | null) => void\n  /** 输入框的值 */\n  value?: number | string | undefined\n  /** 输入框的默认值 */\n  defaultValue?: number | string\n}\n\nconst InputNumberInput = React.forwardRef<HTMLInputElement, InputProps>(\n  (\n    { className, id, value: inputValue, defaultValue, onChange, ...rest },\n    forwardedRef\n  ) => {\n    const Component = 'input'\n    const {\n      size,\n      allowClear,\n      incrementRef,\n      decrementRef,\n      step,\n      imaskOptions = {},\n      value: rootValue,\n      onChange: rootOnChange,\n      formItemId,\n      variant,\n    } = useContext(InputNumberContext)\n\n    const finalId = formItemId || id\n    const value = rootValue === undefined ? inputValue : rootValue\n    const isControlled = value !== undefined\n\n    const [internalValue, setInternalValue] = useState(defaultValue ?? '')\n    const inputRef = useRef<HTMLInputElement | null>(null)\n\n    // 使用受控值或内部状态值\n    const finalValue = isControlled ? value : internalValue\n\n    // 使用优化后的 IMask pipes hook\n    const { maskPipe, unmaskPipe } = useMaskPipes(imaskOptions)\n\n    // 处理输入变化\n    const handleInputChange = useCallback(\n      (e: React.ChangeEvent<HTMLInputElement>) => {\n        const inputValue = e.target.value\n        const isEmpty = inputValue === ''\n\n        // 解析数值，空值时返回空字符串用于显示\n        const parsedValue = unmaskPipe(inputValue)\n\n        // 更新内部状态\n        if (!isControlled) {\n          setInternalValue(isEmpty ? '' : parsedValue)\n        }\n\n        // 传递给 onChange 的值：空输入为 null，否则为解析后的数字\n        const numericValue = isEmpty ? null : parsedValue\n        onChange?.(numericValue)\n        rootOnChange?.(numericValue)\n      },\n      [unmaskPipe, isControlled, onChange, rootOnChange]\n    )\n\n    const { input, clear } = useMemo(() => {\n      return inputNumberVariants({\n        size,\n        variant,\n      })\n    }, [size, variant])\n\n    // 加\n    const increment = useCallback(() => {\n      if (inputRef.current) {\n        inputRef.current.focus()\n      }\n      const currentValue = unmaskPipe(String(finalValue))\n      let nextValue = currentValue + (step || 1)\n      const { max } = imaskOptions\n\n      // 检查是否超出最大值\n      if (max !== undefined && nextValue > max) {\n        nextValue = max\n      }\n\n      if (!isControlled) {\n        setInternalValue(String(nextValue))\n      }\n      onChange?.(nextValue)\n      rootOnChange?.(nextValue)\n    }, [\n      finalValue,\n      unmaskPipe,\n      step,\n      imaskOptions,\n      isControlled,\n      onChange,\n      rootOnChange,\n    ])\n\n    // 减\n    const decrement = useCallback(() => {\n      if (inputRef.current) {\n        inputRef.current.focus()\n      }\n      const currentValue = unmaskPipe(String(finalValue))\n      let nextValue = currentValue - (step || 1)\n      const { min } = imaskOptions\n      // 检查是否超出最小值\n      if (min !== undefined && nextValue < min) {\n        nextValue = min\n      }\n\n      if (!isControlled) {\n        setInternalValue(String(nextValue))\n      }\n      onChange?.(nextValue)\n      rootOnChange?.(nextValue)\n    }, [\n      finalValue,\n      unmaskPipe,\n      step,\n      imaskOptions,\n      isControlled,\n      onChange,\n      rootOnChange,\n    ])\n\n    if (incrementRef) {\n      incrementRef.current = increment\n    }\n\n    if (decrementRef) {\n      decrementRef.current = decrement\n    }\n\n    // 清空输入值\n    const handleClear = useCallback(\n      (e: React.MouseEvent) => {\n        const { min } = imaskOptions\n        e.stopPropagation()\n        const clearValue = min === undefined ? '' : String(min)\n\n        if (!isControlled) {\n          setInternalValue(clearValue)\n        }\n        onChange?.(min ?? null)\n        rootOnChange?.(min ?? null)\n      },\n      [imaskOptions, isControlled, onChange, rootOnChange]\n    )\n\n    // 处理键盘事件\n    const handleKeyDown = useCallback(\n      (e: React.KeyboardEvent<HTMLInputElement>) => {\n        switch (e.key) {\n          case 'ArrowUp':\n            e.preventDefault()\n            increment()\n            break\n          case 'ArrowDown':\n            e.preventDefault()\n            decrement()\n            break\n        }\n      },\n      [increment, decrement]\n    )\n    return (\n      <>\n        <Component\n          id={finalId}\n          className={input({ class: className })}\n          data-ui-slot=\"input-number-input\"\n          ref={(el) => {\n            if (typeof forwardedRef === 'function') {\n              forwardedRef(el)\n            } else if (forwardedRef) {\n              forwardedRef.current = el\n            }\n            inputRef.current = el\n          }}\n          value={maskPipe(String(finalValue))}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          {...rest}\n        />\n        {allowClear && String(finalValue) && (\n          <InputNumberIcon onClick={handleClear} className={clear()}>\n            <ClearIcon />\n          </InputNumberIcon>\n        )}\n      </>\n    )\n  }\n)\n\nInputNumberInput.displayName = 'InputNumberInput'\n\ninterface AffixProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** 应用于附加物容器的自定义 CSS 类 */\n  className?: string\n  /** 附加物的内容 */\n  children?: React.ReactNode\n}\n\nconst InputNumberAffix = React.forwardRef<HTMLDivElement, AffixProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'div'\n    const { size, variant } = useContext(InputNumberContext)\n    const { affix } = useMemo(() => {\n      return inputNumberVariants({\n        size,\n        variant,\n      })\n    }, [size, variant])\n\n    return (\n      <Component\n        ref={ref}\n        className={affix({ class: className })}\n        data-ui-slot=\"input-number-affix\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nInputNumberAffix.displayName = 'InputNumberAffix'\n\ninterface InlineAffixProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** 应用于内联附加物容器的自定义 CSS 类 */\n  className?: string\n  /** 内联附加物的内容 */\n  children?: React.ReactNode\n}\n\nconst InputNumberInlineAffix = React.forwardRef<\n  HTMLDivElement,\n  InlineAffixProps\n>(({ className, children, ...rest }, ref) => {\n  const Component = 'div'\n  const { size, variant } = useContext(InputNumberContext)\n  const { inlineAffix } = useMemo(() => {\n    return inputNumberVariants({\n      size,\n      variant,\n    })\n  }, [size, variant])\n\n  return (\n    <Component\n      ref={ref}\n      className={inlineAffix({ class: className })}\n      data-ui-slot=\"input-number-inline-affix\"\n      {...rest}\n    >\n      {children}\n    </Component>\n  )\n})\n\nInputNumberInlineAffix.displayName = 'InputNumberInlineAffix'\n\ninterface IconProps extends React.HTMLAttributes<HTMLSpanElement> {\n  /** 应用于图标容器的自定义 CSS 类 */\n  className?: string\n  /** 要渲染的图标元素 */\n  children?: React.ReactNode\n}\n\nconst InputNumberIcon = React.forwardRef<HTMLSpanElement, IconProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'span'\n    const { size, variant } = useContext(InputNumberContext)\n    const { icon } = useMemo(() => {\n      return inputNumberVariants({\n        size,\n        variant,\n      })\n    }, [size, variant])\n\n    return (\n      <Component\n        ref={ref}\n        className={icon({ class: className })}\n        data-ui-slot=\"input-number-icon\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nInputNumberIcon.displayName = 'InputNumberIcon'\n\ninterface ControlProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** 应用于控制按钮容器的自定义 CSS 类 */\n  className?: string\n  /** 控制按钮的内容 */\n  children?: React.ReactNode\n}\n\nconst InputNumberControl = React.forwardRef<HTMLDivElement, ControlProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'div'\n    const { control } = inputNumberVariants({})\n\n    return (\n      <Component\n        ref={ref}\n        className={control({ class: className })}\n        data-ui-slot=\"input-number-control\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nInputNumberControl.displayName = 'InputNumberControl'\n\ninterface TriggerProps extends React.HTMLAttributes<HTMLDivElement> {\n  /** 应用于按钮的自定义 CSS 类 */\n  className?: string\n}\n\nconst InputNumberIncrementTrigger = React.forwardRef<\n  HTMLDivElement,\n  TriggerProps\n>(({ className, ...rest }, ref) => {\n  const Component = 'div'\n  const { incrementTrigger } = inputNumberVariants({})\n  const { incrementRef } = useContext(InputNumberContext)\n\n  return (\n    <Component\n      ref={ref}\n      className={incrementTrigger({ class: className })}\n      data-ui-slot=\"input-number-increment-trigger\"\n      onClick={() => incrementRef?.current?.()}\n      {...rest}\n    >\n      <ArrowUpIcon />\n    </Component>\n  )\n})\n\nInputNumberIncrementTrigger.displayName = 'InputNumberIncrementTrigger'\n\nconst InputNumberDecrementTrigger = React.forwardRef<\n  HTMLDivElement,\n  TriggerProps\n>(({ className, ...rest }, ref) => {\n  const Component = 'div'\n  const { decrementTrigger } = inputNumberVariants({})\n  const { decrementRef } = useContext(InputNumberContext)\n\n  return (\n    <Component\n      ref={ref}\n      className={decrementTrigger({ class: className })}\n      data-ui-slot=\"input-number-decrement-trigger\"\n      onClick={() => decrementRef?.current?.()}\n      {...rest}\n    >\n      <ArrowDownIcon />\n    </Component>\n  )\n})\n\nInputNumberDecrementTrigger.displayName = 'InputNumberDecrementTrigger'\n\nexport {\n  InputNumber,\n  InputNumberWrapper,\n  InputNumberInput,\n  InputNumberAffix,\n  InputNumberInlineAffix,\n  InputNumberIcon,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n}\n\nexport type {\n  RootProps as InputNumberProps,\n  WrapperProps as InputNumberWrapperProps,\n  InputProps as InputNumberInputProps,\n  AffixProps as InputNumberAffixProps,\n  InlineAffixProps as InputNumberInlineAffixProps,\n  IconProps as InputNumberIconProps,\n  ControlProps as InputNumberControlProps,\n  TriggerProps as InputNumberTriggerProps,\n}\n", "type": "registry:ui"}], "dependencies": ["imask"], "tags": ["input-number", "number-input"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "fea4e1d82bc25f380ede74f21aca061cb5fd3db5"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-input-number"]}