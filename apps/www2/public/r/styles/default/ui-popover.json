{"name": "ui-popover", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-popover/icon.tsx", "content": "import * as React from 'react'\n\nexport const PopoverCloseIcon = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    width=\"14\"\n    height=\"14\"\n    viewBox=\"0 0 14 14\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    {...props}\n  >\n    <path d=\"M7.00002 5.83337L2.91665 1.75L1.75 2.91667L5.83336 7.00003L1.75 11.0833L2.91665 12.25L7.00002 8.16669L11.0833 12.25L12.25 11.0833L8.16667 7.00003L12.25 2.91667L11.0833 1.75L7.00002 5.83337Z\" />\n  </svg>\n)\n\nexport const PopoverErrorIcon = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"22\"\n    height=\"23\"\n    viewBox=\"0 0 22 23\"\n    fill=\"none\"\n    {...props}\n  >\n    <path\n      d=\"M11 21.1533C5.93743 21.1533 1.83337 17.0492 1.83337 11.9866C1.83337 6.92403 5.93743 2.81998 11 2.81998C16.0626 2.81998 20.1667 6.92403 20.1667 11.9866C20.1667 17.0492 16.0626 21.1533 11 21.1533ZM11 10.6903L8.40731 8.09756L7.11095 9.39392L9.70369 11.9866L7.11095 14.5793L8.40731 15.8757L11 13.283L13.5927 15.8757L14.8891 14.5793L12.2964 11.9866L14.8891 9.39392L13.5927 8.09756L11 10.6903Z\"\n      fill=\"var(--color-red-6)\"\n    />\n  </svg>\n)\n\nexport const PopoverInfoIcon = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"22\"\n    height=\"22\"\n    viewBox=\"0 0 22 22\"\n    fill=\"none\"\n    {...props}\n  >\n    <path\n      d=\"M11 20.1622C5.93743 20.1622 1.83337 16.0581 1.83337 10.9956C1.83337 5.93294 5.93743 1.82889 11 1.82889C16.0626 1.82889 20.1667 5.93294 20.1667 10.9956C20.1667 16.0581 16.0626 20.1622 11 20.1622ZM10.0834 10.0789V15.5789H11.9167V10.0789H10.0834ZM10.0834 6.41222V8.24555H11.9167V6.41222H10.0834Z\"\n      fill=\"var(--color-imileBlue-6)\"\n    />\n  </svg>\n)\n\nexport const PopoverWarningIcon = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"22\"\n    height=\"22\"\n    viewBox=\"0 0 22 22\"\n    fill=\"none\"\n    {...props}\n  >\n    <path\n      d=\"M10.2063 2.74123L1.47382 17.8663C1.22063 18.3047 1.37087 18.8653 1.80932 19.1184C1.94865 19.1989 2.10677 19.2413 2.26765 19.2413L19.7325 19.2413C20.2388 19.2413 20.6492 18.8309 20.6492 18.3246C20.6492 18.1636 20.6068 18.0056 20.5264 17.8663L11.7939 2.74123C11.5408 2.30279 10.9802 2.15257 10.5418 2.4057C10.4024 2.48616 10.2866 2.60188 10.2063 2.74123ZM11.9168 14.6579L11.9168 16.4913L10.0834 16.4913L10.0834 14.6579L11.9168 14.6579ZM11.9168 8.24123L11.9168 12.8246L10.0834 12.8246L10.0834 8.24123L11.9168 8.24123Z\"\n      fill=\"var(--color-orange-6)\"\n    />\n  </svg>\n)\n\nexport const PopoverSuccessIcon = (props: React.SVGProps<SVGSVGElement>) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"22\"\n    height=\"23\"\n    viewBox=\"0 0 22 23\"\n    fill=\"none\"\n    {...props}\n  >\n    <path\n      d=\"M11 21.1489C5.93743 21.1489 1.83337 17.0448 1.83337 11.9822C1.83337 6.91958 5.93743 2.81552 11 2.81552C16.0626 2.81552 20.1667 6.91958 20.1667 11.9822C20.1667 17.0448 16.0626 21.1489 11 21.1489ZM10.0858 15.6489L16.5676 9.16704L15.2712 7.87068L10.0858 13.0562L7.49307 10.4634L6.1967 11.7598L10.0858 15.6489Z\"\n      fill=\"var(--color-green-6)\"\n    />\n  </svg>\n)\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-popover/index.tsx", "content": "'use client'\n\nimport * as React from 'react'\nimport * as PopoverPrimitive from '@imd/base-popover'\nimport type {\n  PopoverAnchorProps,\n  PopoverArrowProps,\n  PopoverCloseProps,\n  PopoverContentProps,\n  PopoverPortalProps,\n  PopoverProps,\n  PopoverTriggerProps,\n} from '@imd/base-popover'\n\nimport { cn } from '@/lib/utils'\nimport { Button, ButtonIcon } from '@/registry/default/ui/ui-button'\n\nimport { PopoverCloseIcon } from './icon'\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverClose\n * -----------------------------------------------------------------------------------------------*/\nconst PopoverClose = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Close>,\n  PopoverCloseProps\n>(({ children, asChild = true, style, ...props }, ref) => {\n  return (\n    <PopoverPrimitive.Close\n      ref={ref}\n      asChild={asChild}\n      data-ui-slot=\"popover-close\"\n      {...props}\n      style={{ gridArea: 'close', ...style }}\n    >\n      {children || (\n        <Button className=\"h-5.5 w-5.5\" variant=\"text\" tabIndex={-1}>\n          <ButtonIcon className=\"h-3.5 w-3.5\">\n            <PopoverCloseIcon />\n          </ButtonIcon>\n        </Button>\n      )}\n    </PopoverPrimitive.Close>\n  )\n})\n\nPopoverClose.displayName = PopoverPrimitive.Close.displayName\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverIcon\n * -----------------------------------------------------------------------------------------------*/\nconst POPOVER_ICON_NAME = 'PopoverIcon'\n\ntype PopoverDivElement = HTMLDivElement\ntype PrimitiveDivProps = React.HTMLAttributes<HTMLDivElement>\n\ninterface PopoverIconProps extends PrimitiveDivProps {}\nconst PopoverIcon = React.forwardRef<PopoverDivElement, PopoverIconProps>(\n  ({ className, children, style, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        data-ui-slot=\"popover-icon\"\n        {...props}\n        style={{ gridArea: 'icon', ...style }}\n        className={cn('mr-2 min-w-[22px]', className)}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nPopoverIcon.displayName = POPOVER_ICON_NAME\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverContent\n * -----------------------------------------------------------------------------------------------*/\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  PopoverContentProps\n>(({ className, children, style, ...props }, ref) => {\n  return (\n    <PopoverPrimitive.Content\n      ref={ref}\n      data-ui-slot=\"popover-content\"\n      {...props}\n      className={cn(\n        'data-[state=open]:animate-in data-[state=closed]:animate-out',\n        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\n        'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',\n        'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        'z-imd-popover shadow-2 grid min-w-[160px] max-w-[400px] grid-cols-3 grid-rows-2 items-start rounded-md bg-white p-3',\n        className\n      )}\n      style={{\n        gridTemplateAreas: `\"icon title close\" \". description .\"`,\n        gridTemplateColumns: 'auto 1fr auto',\n        gridTemplateRows: 'auto auto',\n        ...style,\n      }}\n    >\n      {children}\n    </PopoverPrimitive.Content>\n  )\n})\n\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverTitle\n * -----------------------------------------------------------------------------------------------*/\nconst POPOVER_TITLE_NAME = 'PopoverTitle'\n\ninterface PopoverTitleProps extends PrimitiveDivProps {}\n\nconst PopoverTitle = React.forwardRef<PopoverDivElement, PopoverTitleProps>(\n  ({ className, children, style, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        data-ui-slot=\"popover-title\"\n        {...props}\n        style={{ gridArea: 'title', ...style }}\n        className={cn(\n          'text-gray-13 typography-body-medium-plus mb-2',\n          className\n        )}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nPopoverTitle.displayName = POPOVER_TITLE_NAME\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverDescription\n * -----------------------------------------------------------------------------------------------*/\nconst POPOVER_DESCRIPTION_NAME = 'PopoverDescription'\n\ninterface PopoverDescriptionProps extends PrimitiveDivProps {}\n\nconst PopoverDescription = React.forwardRef<\n  PopoverDivElement,\n  PopoverDescriptionProps\n>(({ className, children, style, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-ui-slot=\"popover-description\"\n      {...props}\n      className={cn(\n        'text-gray-10 typography-body-small break-words',\n        className\n      )}\n      style={{ gridArea: 'description', ...style }}\n    >\n      {children}\n    </div>\n  )\n})\n\nPopoverDescription.displayName = POPOVER_DESCRIPTION_NAME\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst PopoverArrow = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Arrow>,\n  PopoverArrowProps\n>(({ className, children, ...props }, ref) => {\n  return (\n    <PopoverPrimitive.Arrow\n      ref={ref}\n      data-ui-slot=\"popover-arrow\"\n      width={12}\n      height={6}\n      {...props}\n      className={cn('fill-white', className)}\n    >\n      {children}\n    </PopoverPrimitive.Arrow>\n  )\n})\n\nPopoverArrow.displayName = PopoverPrimitive.Arrow.displayName\n\n/* -------------------------------------------------------------------------------------------------\n * Popover, Anchor, Trigger, Portal with data-ui-slot attributes\n * -----------------------------------------------------------------------------------------------*/\n\nconst Popover = ({ ...props }: PopoverProps) => (\n  <PopoverPrimitive.Root data-ui-slot=\"popover\" {...props} />\n)\nPopover.displayName = 'Popover'\n\nconst PopoverAnchor = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Anchor>,\n  PopoverAnchorProps\n>((props, ref) => (\n  <PopoverPrimitive.Anchor ref={ref} data-ui-slot=\"popover-anchor\" {...props} />\n))\nPopoverAnchor.displayName = 'PopoverAnchor'\n\nconst PopoverTrigger = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Trigger>,\n  PopoverTriggerProps\n>((props, ref) => (\n  <PopoverPrimitive.Trigger\n    ref={ref}\n    data-ui-slot=\"popover-trigger\"\n    {...props}\n  />\n))\nPopoverTrigger.displayName = 'PopoverTrigger'\n\nconst PopoverPortal = ({ ...props }: PopoverPortalProps) => (\n  <PopoverPrimitive.Portal {...props} />\n)\nPopoverPortal.displayName = 'PopoverPortal'\n\n/* -------------------------------------------------------------------------------------------------\n * export\n * -----------------------------------------------------------------------------------------------*/\nexport {\n  Popover,\n  PopoverAnchor,\n  PopoverTrigger,\n  PopoverPortal,\n  PopoverContent,\n  PopoverClose,\n  PopoverArrow,\n  PopoverTitle,\n  PopoverDescription,\n  PopoverIcon,\n}\n\nexport { type TriggerAction } from '@imd/base-popover'\n\nexport type {\n  PopoverArrowProps,\n  PopoverDescriptionProps,\n  PopoverContentProps,\n  PopoverIconProps,\n  PopoverCloseProps,\n  PopoverProps,\n  PopoverPortalProps,\n  PopoverTriggerProps,\n  PopoverAnchorProps,\n}\n", "type": "registry:ui"}], "dependencies": ["@imd/base-popover"], "tags": ["flyout", "popover", "popper", "popup", "气泡卡片"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "22de7857f39da80e4cfdf3bf66ec2ef18bccb838"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-popover", "pro-poptextarea"]}