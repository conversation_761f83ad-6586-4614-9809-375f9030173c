{"name": "ui-textField", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-textField/icon.tsx", "content": "export const ClearIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      viewBox=\"0 0 16 16\"\n      fill=\"currentColor\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path d=\"M8.00002 6.88893L4.1111 3L3 4.11111L6.88892 8.00003L3 11.8889L4.1111 13L8.00002 9.11114L11.8889 13L13 11.8889L9.11111 8.00003L13 4.11111L11.8889 3L8.00002 6.88893Z\" />\n    </svg>\n  )\n}\n\nexport const EyeIcon = (props: React.SVGProps<SVGSVGElement>) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      {...props}\n    >\n      <path\n        d=\"M0.787762 8C1.41477 4.58651 4.40542 2 8.0002 2C11.5949 2 14.5856 4.58651 15.2126 8C14.5856 11.4135 11.5949 14 8.0002 14C4.40542 14 1.41477 11.4135 0.787762 8ZM8.0002 11.3333C9.84114 11.3333 11.3335 9.84093 11.3335 8C11.3335 6.15905 9.84114 4.66667 8.0002 4.66667C6.15923 4.66667 4.66684 6.15905 4.66684 8C4.66684 9.84093 6.15923 11.3333 8.0002 11.3333ZM8.0002 10C6.8956 10 6.00018 9.1046 6.00018 8C6.00018 6.8954 6.8956 6 8.0002 6C9.10474 6 10.0002 6.8954 10.0002 8C10.0002 9.1046 9.10474 10 8.0002 10Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n\nexport const EyeOffIcon = (props: React.SVGProps<SVGSVGElement>) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      {...props}\n    >\n      <g clipPath=\"url(#clip0_21661_11313)\">\n        <path\n          d=\"M3.01364 3.9563L0.929097 1.87177L1.87191 0.928955L15.0713 14.1283L14.1285 15.0711L11.9218 12.8645C10.7876 13.5836 9.44252 14.0001 8.00019 14.0001C4.4054 14.0001 1.41476 11.4135 0.78775 8.00006C1.07887 6.41514 1.87954 5.00852 3.01364 3.9563ZM9.83846 10.7811L8.86246 9.80512C8.60132 9.93006 8.30892 10.0001 8.00019 10.0001C6.89559 10.0001 6.00016 9.10459 6.00016 8.00006C6.00016 7.69126 6.07014 7.39886 6.19508 7.13772L5.2191 6.16177C4.87008 6.68872 4.66683 7.32066 4.66683 8.00006C4.66683 9.84099 6.15922 11.3334 8.00019 11.3334C8.67952 11.3334 9.31146 11.1301 9.83846 10.7811ZM5.3163 2.50669C6.1475 2.17965 7.05286 2.00003 8.00019 2.00003C11.5949 2.00003 14.5856 4.58654 15.2126 8.00006C15.0045 9.13312 14.5359 10.1751 13.8712 11.0616L11.298 8.48839C11.3214 8.32899 11.3335 8.16592 11.3335 8.00006C11.3335 6.15908 9.84112 4.6667 8.00019 4.6667C7.83426 4.6667 7.67119 4.67881 7.51179 4.70221L5.3163 2.50669Z\"\n          fill=\"currentColor\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_21661_11313\">\n          <rect width=\"16\" height=\"16\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  )\n}\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textField/index.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { usePageFilterContext } from '@imd/context'\nimport { tv } from 'tailwind-variants'\n\nimport { ClearIcon } from './icon'\n\ntype Size = 'small' | 'medium' | 'large'\n\ntype Variant = 'outlined' | 'filled' | 'ghost'\n\nconst textFieldVariants = tv({\n  slots: {\n    root: [\n      'group/textField-root',\n      // base\n      'relative flex w-full overflow-hidden rounded-sm border',\n      // 间隔线\n      'divide-gray-3 divide-x',\n      // hover\n      'hover:[&:not(&:has(input:disabled))]:border-primary-6 hover:[&:not(&:has(input:disabled))]:bg-white',\n      // focus\n      'has-[input:focus]:shadow-component-focus has-[input:focus]:border-primary-6 has-[input:focus]:bg-white',\n      // disabled\n      'has-[input:disabled]:shadow-none has-[input:disabled]:cursor-not-allowed',\n      'has-[input:disabled]:bg-gray-4 has-[input:disabled]:border-gray-4 has-[input:disabled]:divide-gray-5',\n    ],\n    affix: ['flex items-center justify-items-center', 'text-gray-13'],\n    inlineAffix: [\n      'flex items-center justify-items-center',\n      'text-gray-13 whitespace-nowrap',\n    ],\n    wrapper: [\n      'group/textField-wrapper',\n      'flex flex-1 items-center',\n      'has-[input:disabled]:pointer-events-none',\n    ],\n    // 清除按钮\n    clear: [\n      'hidden cursor-pointer transition-colors',\n      'hover:text-primary-6',\n      'group-has-[input:focus]/textField-root:block group-hover/textField-root:block',\n    ],\n    icon: ['text-gray-10 flex-shrink-0'],\n    input: [\n      // base\n      'text-gray-13 placeholder:text-gray-7 w-full bg-transparent bg-none outline-none',\n    ],\n  },\n  variants: {\n    size: {\n      small: {\n        root: 'h-6',\n        affix: 'typography-body-small px-2',\n        inlineAffix: 'typography-body-small',\n        wrapper: 'gap-x-1 px-2',\n        icon: 'size-3.5',\n        input: 'typography-body-small',\n      },\n      medium: {\n        root: 'h-8',\n        affix: 'typography-body-small px-3',\n        inlineAffix: 'typography-body-small',\n        wrapper: 'gap-x-2 px-3',\n        icon: 'size-3.5',\n        input: 'typography-body-small',\n      },\n      large: {\n        // TODO: 暂时写死38px, 不按照space来计算\n        root: 'h-[38px]',\n        affix: 'typography-body-medium px-3',\n        inlineAffix: 'typography-body-medium',\n        wrapper: 'gap-x-2 px-3',\n        icon: 'size-4',\n        input: 'typography-body-medium',\n      },\n    },\n    variant: {\n      outlined: {\n        root: ['border-gray-3 has-[input:disabled]:bg-gray-1 bg-white'],\n        affix: [\n          // disabled\n          'group-has-[input:disabled]/textField-root:text-gray-4',\n        ],\n        inlineAffix: [\n          // disabled\n          'group-has-[input:disabled]/textField-root:text-gray-4',\n        ],\n        icon: [\n          // disabled\n          'group-has-[input:disabled]/textField-root:text-gray-4',\n        ],\n        input: [\n          // disabled\n          'disabled:text-gray-5 disabled:placeholder:text-gray-5',\n        ],\n      },\n      filled: {\n        root: ['bg-gray-2 border-gray-2'],\n        affix: [\n          // disabled\n          'group-has-[input:disabled]/textField-root:text-gray-8',\n        ],\n        inlineAffix: [\n          // disabled\n          'group-has-[input:disabled]/textField-root:text-gray-8',\n        ],\n        icon: [\n          // disabled\n          'group-has-[input:disabled]/textField-root:text-gray-8',\n        ],\n        input: [\n          // disabled\n          'disabled:text-gray-8 disabled:placeholder:text-gray-8 ',\n        ],\n      },\n      ghost: {\n        root: [\n          'hover:[&:not(&:has(input:disabled))]:bg-gray-1 border-transparent bg-transparent',\n        ],\n      },\n    },\n    hasError: {\n      true: {\n        root: [\n          'bg-red-1 border-red-1',\n          'hover:[&:not(&:has(input:disabled))]:border-red-6',\n          'has-[input:focus]:shadow-component-focus-error has-[input:focus]:border-red-6',\n        ],\n      },\n      false: {},\n    },\n  },\n  defaultVariants: {\n    size: 'medium',\n    hasError: false,\n    variant: 'filled',\n  },\n})\n\nconst TextFieldContext = React.createContext<{\n  size?: Size\n  allowClear?: boolean\n  formItemId?: string\n  value?: string | number\n  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void\n  disabled?: boolean\n  hasError?: boolean\n  variant?: Variant\n}>({})\n\ntype RootProps = React.HTMLAttributes<HTMLDivElement> & {\n  /** 输入框大小 */\n  size?: Size\n  /** 是否显示清空图标 */\n  allowClear?: boolean\n  /** 是否显示错误状态 */\n  hasError?: boolean\n  /** 输入框的值 */\n  value?: string | number\n  /** 值变化时的回调函数，会触发 formItem 的 onChange */\n  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void\n  /** 表单项ID */\n  'data-form-item-id'?: string\n  /** 输入框的变体 */\n  variant?: Variant\n  /** 是否禁用 */\n  disabled?: boolean\n}\n\nconst TextField = React.forwardRef<HTMLDivElement, RootProps>(\n  (\n    {\n      className,\n      size = 'medium',\n      allowClear = false,\n      hasError = false,\n      'data-form-item-id': formItemId,\n      children,\n      value,\n      onChange,\n      variant,\n      disabled,\n      ...rest\n    },\n    ref\n  ) => {\n    const Component = 'div'\n    const pageFilterContext = usePageFilterContext('ui-textField')\n    const mergedVariant = pageFilterContext.variant || variant\n    const { root } = React.useMemo(() => {\n      return textFieldVariants({\n        size,\n        hasError,\n        variant: mergedVariant,\n      })\n    }, [size, hasError, mergedVariant])\n\n    const contextValue = React.useMemo(\n      () => ({\n        size,\n        allowClear,\n        formItemId,\n        value,\n        onChange,\n        disabled,\n        hasError,\n        variant: mergedVariant,\n      }),\n      [\n        size,\n        allowClear,\n        formItemId,\n        value,\n        onChange,\n        disabled,\n        hasError,\n        mergedVariant,\n      ]\n    )\n\n    return (\n      <TextFieldContext.Provider value={contextValue}>\n        <Component\n          ref={ref}\n          className={root({ class: className })}\n          data-ui-slot=\"text-field\"\n          {...rest}\n        >\n          {children}\n        </Component>\n      </TextFieldContext.Provider>\n    )\n  }\n)\n\nTextField.displayName = 'TextField'\n\ntype WrapperProps = React.InputHTMLAttributes<HTMLLabelElement>\n\nconst TextFieldWrapper = React.forwardRef<HTMLLabelElement, WrapperProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'label'\n    const { size } = React.useContext(TextFieldContext)\n    const { wrapper } = React.useMemo(() => {\n      return textFieldVariants({\n        size,\n      })\n    }, [size])\n\n    return (\n      <Component\n        ref={ref}\n        className={wrapper({ class: className })}\n        data-ui-slot=\"text-field-wrapper\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\nTextFieldWrapper.displayName = 'TextFieldWrapper'\n\ntype InputProps = React.InputHTMLAttributes<HTMLInputElement>\n\nconst TextFieldInput = React.forwardRef<HTMLInputElement, InputProps>(\n  (\n    {\n      id,\n      className,\n      defaultValue,\n      value: inputValue,\n      disabled,\n      onChange,\n      ...rest\n    },\n    ref\n  ) => {\n    const Component = 'input'\n    const {\n      size,\n      allowClear,\n      formItemId,\n      value: rootValue,\n      onChange: rootOnChange,\n      disabled: rootDisabled,\n      variant,\n    } = React.useContext(TextFieldContext)\n\n    const { input, clear } = React.useMemo(() => {\n      return textFieldVariants({\n        size,\n        variant,\n      })\n    }, [size, variant])\n    const value = rootValue === undefined ? inputValue : rootValue // 优先使用 rootValue\n    // 检测是否为受控组件\n    const isControlled = value !== undefined\n    const [internalValue, setInternalValue] = React.useState(defaultValue ?? '')\n\n    // 使用受控值或内部状态值\n    const finalValue = isControlled ? value : internalValue\n\n    const finalId = formItemId || id\n\n    const finalDisabled = rootDisabled ?? disabled\n    // 处理受控和非受控的值变化\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n      // 如果是非受控组件，更新内部状态\n      if (!isControlled) {\n        setInternalValue(e.target.value)\n      }\n      onChange?.(e)\n      // 如果有 rootOnChange 回调，调用它 formItem\n      rootOnChange?.(e)\n    }\n\n    // 清空输入值\n    const handleClear = (e: React.MouseEvent | React.KeyboardEvent) => {\n      e.stopPropagation()\n\n      // 如果是非受控组件，清空内部状态\n      if (!isControlled) {\n        setInternalValue('')\n      }\n\n      // 创建一个合成事件来模拟输入改变\n      const fakeEvent = {\n        target: { value: '' },\n        currentTarget: { value: '' },\n        preventDefault: () => {},\n        stopPropagation: () => {},\n      } as React.ChangeEvent<HTMLInputElement>\n      onChange?.(fakeEvent)\n      rootOnChange?.(fakeEvent)\n\n      // 焦点返回到输入框\n      if (ref && 'current' in ref && ref.current) {\n        ref.current.focus()\n      }\n    }\n\n    return (\n      <>\n        <Component\n          id={finalId}\n          className={input({ class: className })}\n          value={finalValue}\n          ref={ref}\n          data-ui-slot=\"text-field-input\"\n          disabled={finalDisabled}\n          {...rest}\n          onChange={handleChange}\n        />\n        {allowClear && !finalDisabled && finalValue && (\n          <TextFieldIcon onClick={handleClear} className={clear()}>\n            <ClearIcon />\n          </TextFieldIcon>\n        )}\n      </>\n    )\n  }\n)\n\nTextFieldInput.displayName = 'TextFieldInput'\n\ntype AffixProps = React.HTMLAttributes<HTMLDivElement>\n\nconst TextFieldAffix = React.forwardRef<HTMLDivElement, AffixProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'div'\n    const { size, variant } = React.useContext(TextFieldContext)\n    const { affix } = React.useMemo(() => {\n      return textFieldVariants({\n        size,\n        variant,\n      })\n    }, [size, variant])\n\n    return (\n      <Component\n        ref={ref}\n        className={affix({ class: className })}\n        data-ui-slot=\"text-field-affix\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nTextFieldAffix.displayName = 'TextFieldAffix'\n\ntype InlineAffixProps = React.HTMLAttributes<HTMLDivElement>\n\nconst TextFieldInlineAffix = React.forwardRef<HTMLDivElement, InlineAffixProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'div'\n    const { size, variant } = React.useContext(TextFieldContext)\n    const { inlineAffix } = React.useMemo(() => {\n      return textFieldVariants({\n        size,\n        variant,\n      })\n    }, [size, variant])\n\n    return (\n      <Component\n        ref={ref}\n        className={inlineAffix({ class: className })}\n        data-ui-slot=\"text-field-inline-affix\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nTextFieldInlineAffix.displayName = 'TextFieldInlineAffix'\n\ntype IconProps = React.HTMLAttributes<HTMLSpanElement>\n\nconst TextFieldIcon = React.forwardRef<HTMLSpanElement, IconProps>(\n  ({ className, children, ...rest }, ref) => {\n    const Component = 'span'\n    const { size, variant } = React.useContext(TextFieldContext)\n    const { icon } = React.useMemo(() => {\n      return textFieldVariants({\n        size,\n        variant,\n      })\n    }, [size, variant])\n\n    return (\n      <Component\n        ref={ref}\n        className={icon({ class: className })}\n        data-ui-slot=\"text-field-icon\"\n        {...rest}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nTextFieldIcon.displayName = 'TextFieldIcon'\n\nexport {\n  TextField,\n  TextFieldWrapper,\n  TextFieldInput,\n  TextFieldAffix,\n  TextFieldInlineAffix,\n  TextFieldIcon,\n}\n\nexport type {\n  RootProps as TextFieldProps,\n  WrapperProps as TextFieldWrapperProps,\n  InputProps as TextFieldInputProps,\n  AffixProps as TextFieldAffixProps,\n  InlineAffixProps as TextFieldInlineAffixProps,\n  IconProps as TextFieldIconProps,\n}\n", "type": "registry:ui"}], "dependencies": ["@imd/context"], "tags": ["form-control", "input", "textField", "输入框"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "fea4e1d82bc25f380ede74f21aca061cb5fd3db5"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-form", "pro-textField"]}