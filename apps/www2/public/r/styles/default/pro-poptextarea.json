{"name": "pro-poptextarea", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-poptextarea/index.tsx", "content": "'use client'\n\n/**\n * 用于批量输入的 popover + textarea 的小部件\n */\nimport React from 'react'\n\nimport { cn } from '@/lib/utils'\nimport { useTranslation } from '@/registry/default/hooks/use-translation'\nimport { Button, ButtonIcon } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverClose,\n  PopoverContent,\n  PopoverContentProps,\n  PopoverPortal,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\nimport { Textarea } from '@/registry/default/ui/ui-textarea'\n\nconst ExpandIcon = React.memo(() => {\n  return (\n    <svg\n      fill=\"currentColor\"\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path d=\"M10.2584 2.91667H8.16667V1.75H12.25V5.83333H11.0833V3.74162L8.57914 6.24581L7.75419 5.42085L10.2584 2.91667ZM1.75 8.16667H2.91667V10.2584L5.42085 7.75419L6.24581 8.57914L3.74162 11.0833H5.83333V12.25H1.75V8.16667Z\" />\n    </svg>\n  )\n})\n\nExpandIcon.displayName = 'ExpandIcon'\n\ninterface PoptextareaProps {\n  /** 受控 value */\n  value?: string\n  /** 非受控 defaultValue */\n  defaultValue?: string\n  /** placeholder */\n  placeholder?: string\n  /** 触发器 */\n  trigger?: React.ReactNode\n  /** 确认回调，将多行字符串转为去除空行和首尾空格的数组 和 当前输入框的原始值 现有格式化不满足的情况下，可以自行处理 */\n  onConfirm?: (items: string[], value: string) => void\n  /** 取消回调 */\n  onCancel?: () => void\n  /** 清除回调 */\n  onClear?: () => void\n  /** onChange */\n  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void\n  /** PopoverContentProps */\n  popoverContentProps?: PopoverContentProps\n  /**\n   * 自定义分割函数，将输入字符串转为数组\n   * @param value 输入字符串\n   * @returns 分割后的字符串数组\n   */\n  customSplitBatchValue?: (value: string) => string[]\n  /** 打开状态变化回调 */\n  onOpenChange?: (open: boolean) => void\n  /** 清除按钮文本 */\n  clearText?: string\n  /** 取消按钮文本 */\n  cancelText?: string\n  /** 确认按钮文本 */\n  confirmText?: string\n}\n\n/**\n * 按照分隔符将字符串转为去除空行和首尾空格的数组\n */\nconst splitBatchValue = (input: string): string[] => {\n  return input\n    .split(/,+|;+|；+|，+|\\n+|\\s+/)\n    .map((item) => item.trim())\n    .filter((item) => item.length > 0)\n}\n\nconst Poptextarea = React.memo<PoptextareaProps>((props) => {\n  const { t } = useTranslation()\n  const {\n    trigger,\n    onConfirm,\n    onCancel,\n    onClear,\n    value,\n    defaultValue,\n    onChange,\n    placeholder,\n    popoverContentProps = {},\n    customSplitBatchValue,\n    onOpenChange,\n    clearText,\n    cancelText,\n    confirmText,\n  } = props\n  const isControlled = value !== undefined\n  const [innerValue, setInnerValue] = React.useState(defaultValue ?? '')\n  const [open, setOpen] = React.useState(false)\n\n  const finalValue = isControlled ? value : innerValue\n\n  // 优先使用自定义 customSplitBatchValue，否则用默认\n  const splitFn = customSplitBatchValue ?? splitBatchValue\n\n  const total = React.useMemo(() => {\n    try {\n      return splitFn(finalValue).length\n    } catch (error) {\n      console.warn('Parse function error:', error)\n      return 0\n    }\n  }, [finalValue, splitFn])\n\n  // 处理 confirm - 使用 useCallback 优化\n  const handleConfirm = () => {\n    try {\n      const items = splitFn(finalValue)\n      onConfirm?.(items, finalValue)\n      setOpen(false)\n    } catch (error) {\n      console.warn('Parse function error in confirm:', error)\n      onConfirm?.([], finalValue)\n      setOpen(false)\n    }\n  }\n\n  // 处理 clear - 使用 useCallback 优化\n  const handleClear = () => {\n    if (isControlled) {\n      // 触发 onChange 事件清空值\n      onChange?.({\n        target: { value: '' },\n      } as React.ChangeEvent<HTMLTextAreaElement>)\n    } else {\n      setInnerValue('')\n    }\n    onClear?.()\n  }\n\n  // 处理 cancel\n  const handleCancel = () => {\n    onCancel?.()\n  }\n\n  // 受控/非受控 onChange\n  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    if (isControlled) {\n      onChange?.(e)\n    } else {\n      setInnerValue(e.target.value)\n    }\n  }\n\n  return (\n    <Popover\n      action={['click']}\n      open={open}\n      onOpenChange={(v) => {\n        setOpen(v)\n        onOpenChange?.(v)\n      }}\n    >\n      <PopoverTrigger asChild>\n        {trigger ? (\n          trigger\n        ) : (\n          <Button size=\"small\" className=\"mr-[-8px]\" color=\"secondary\">\n            <ButtonIcon>\n              <ExpandIcon />\n            </ButtonIcon>\n          </Button>\n        )}\n      </PopoverTrigger>\n      <PopoverPortal>\n        <PopoverContent\n          role=\"dialog\"\n          aria-label={t('Batch input popover')}\n          side=\"bottom\"\n          align=\"end\"\n          sideOffset={12}\n          alignOffset={-4}\n          onCloseAutoFocus={(e) => {\n            // 阻止自动聚焦到触发器按钮\n            e.preventDefault()\n          }}\n          {...popoverContentProps}\n          className={cn(\n            'block w-[432px] max-w-none',\n            popoverContentProps?.className\n          )}\n        >\n          <Textarea\n            aria-label={t('Batch input Textarea')}\n            aria-describedby=\"batch-search-help\"\n            className=\"h-[200px] resize-none\"\n            value={finalValue}\n            onChange={handleChange}\n            placeholder={placeholder}\n            onKeyDown={(e) => {\n              if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {\n                e.preventDefault()\n                handleConfirm()\n              }\n            }}\n          />\n          <div id=\"batch-search-help\" className=\"sr-only\">\n            {t('Enter multiple search terms, one per line')}\n          </div>\n          <div className=\"mt-3 flex justify-between\">\n            <div className=\"flex items-center\">\n              <span className=\"typography-body-small text-gray-10 mr-1\">\n                {t('Total')} :{' '}\n              </span>\n              <span className=\"typography-body-small-plus text-gray-13\">\n                {total}\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button variant=\"text\" onClick={handleClear}>\n                {clearText ?? t('Clear')}\n              </Button>\n              <PopoverClose asChild>\n                <Button color=\"secondary\" onClick={handleCancel}>\n                  {cancelText ?? t('Cancel')}\n                </Button>\n              </PopoverClose>\n              <PopoverClose asChild>\n                <Button onClick={handleConfirm}>\n                  {confirmText ?? t('Confirm')}\n                </Button>\n              </PopoverClose>\n            </div>\n          </div>\n        </PopoverContent>\n      </PopoverPortal>\n    </Popover>\n  )\n})\n\nPoptextarea.displayName = 'ProPoptextarea'\n\nexport { Poptextarea, splitBatchValue }\nexport type { PoptextareaProps }\n", "type": "registry:ui"}], "registryDependencies": ["ui-button", "ui-popover", "ui-textarea", "use-translation"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "e985dbe3995ecab41158e5abc6b261a04a7d678d"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 4, "instability": 0.8}, "dependents": ["pro-batch-input"]}