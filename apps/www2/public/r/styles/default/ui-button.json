{"name": "ui-button", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-button/index.tsx", "content": "'use client'\n\nimport * as React from 'react'\nimport * as BaseButton from '@imd/base-button'\nimport { tv } from 'tailwind-variants'\n\nimport { cn } from '@/lib/utils'\n\nconst CHINESE_CHAR_REGEX = /[\\u4e00-\\u9fff]/\n\nconst isChinese = (char: string) => CHINESE_CHAR_REGEX.test(char)\n\n// 检查文本是否为两个中文字符\nconst isChineseTwoChars = (text: string) => {\n  const trimmed = text.trim()\n  return trimmed.length === 2 && isChinese(trimmed[0]) && isChinese(trimmed[1])\n}\n\n// 按钮的样式\nconst buttonVariants = tv({\n  base: [\n    // 布局相关\n    'inline-flex items-center justify-center',\n    // 文本相关\n    'whitespace-nowrap font-semibold',\n    // 交互相关\n    'cursor-pointer transition-colors',\n    // 形状相关\n    'rounded-sm',\n    // 焦点相关\n    'focus-visible:ring-primary focus-visible:outline-none focus-visible:ring-2',\n    // 禁用状态\n    'pointer-events-auto disabled:cursor-not-allowed',\n  ],\n  variants: {\n    variant: {\n      contained: 'bg-primary text-primary-foreground hover:bg-primary/90',\n      outlined: 'border border-solid',\n      text: 'hover:bg-accent hover:text-accent-foreground',\n    },\n    color: {\n      primary: '',\n      secondary: '',\n      default: '',\n      error: '',\n      'light-error': '',\n      'linear-gradient': '',\n      search: '',\n      reset: '',\n    },\n    size: {\n      small: 'h-6 px-2 py-0.5 text-xs leading-5',\n      medium: 'h-8 px-4 py-1.5 text-xs leading-5',\n      large: 'h-9.5 px-6 py-2 text-sm leading-[22px]',\n    },\n    onlyIcon: {\n      true: '',\n      false: '',\n    },\n  },\n  compoundVariants: [\n    {\n      color: 'primary',\n      variant: 'contained',\n      class: [\n        'bg-primary text-white',\n        'hover:bg-primary-7',\n        'focus:bg-primary-8',\n        'disabled:bg-primary-4',\n      ],\n    },\n    {\n      color: 'secondary',\n      variant: 'contained',\n      class: [\n        'bg-gray-2 text-gray-13 [&_.ButtonIcon]:text-gray-10',\n        'hover:bg-gray-3 hover:[&_.ButtonIcon]:text-gray-10',\n        'focus:bg-gray-4 focus:[&_.ButtonIcon]:text-gray-10',\n        'disabled:bg-gray-2 disabled:text-gray-5 [&:disabled_.ButtonIcon]:text-gray-5',\n      ],\n    },\n    {\n      color: 'primary',\n      variant: 'outlined',\n      class: [\n        'border-primary text-primary',\n        'hover:border-primary-7 hover:text-primary-7 hover:bg-white',\n        'focus:border-primary-8 focus:text-primary-8',\n        'disabled:border-primary-4 disabled:text-primary-4',\n      ],\n    },\n    {\n      color: 'primary',\n      variant: 'text',\n      class: [\n        'text-primary',\n        'hover:text-primary-7 hover:bg-gray-2',\n        'focus:text-primary focus:bg-primary-1',\n        'disabled:text-primary-4',\n      ],\n    },\n    {\n      color: 'error',\n      variant: 'contained',\n      class: [\n        'bg-red-6 text-white',\n        'hover:bg-red-7',\n        'focus:bg-red-8',\n        'disabled:bg-red-4',\n      ],\n    },\n    {\n      color: 'light-error',\n      variant: 'contained',\n      class: [\n        'bg-red-1 text-red-6',\n        'hover:bg-red-2',\n        'focus:bg-red-3',\n        'disabled:bg-red-1 disabled:text-red-4',\n      ],\n    },\n    {\n      color: 'error',\n      variant: 'outlined',\n      class: [\n        'border-red-6 text-red-6',\n        'hover:border-red-7 hover:text-red-7 hover:bg-white',\n        'focus:border-red-8 focus:text-red-8',\n        'disabled:border-red-4 disabled:text-red-4',\n      ],\n    },\n    {\n      color: 'error',\n      variant: 'text',\n      class: [\n        'text-red-6',\n        'hover:text-red-6 hover:bg-gray-2',\n        'focus:text-red-6 focus:bg-red-1',\n        'disabled:text-red-4',\n      ],\n    },\n    {\n      color: 'default',\n      variant: 'text',\n      class: [\n        'text-gray-13 [&_.ButtonIcon]:text-gray-10 group',\n        'hover:text-gray-13 hover:bg-gray-2',\n        'focus:text-primary focus:bg-primary-1',\n        'disabled:text-gray-5 [&:disabled_.ButtonIcon]:text-gray-5',\n      ],\n    },\n    {\n      color: 'linear-gradient',\n      variant: 'contained',\n      class: [\n        'text-white [background:linear-gradient(104deg,#459cff_1.04%,#2546ff_98.96%)]',\n        'hover:[background:linear-gradient(102deg,#2546ff_2.66%,#459cff_98.01%)]',\n        'focus:[background:linear-gradient(104deg,#377dcc_1.04%,#1e38cc_98.96%)]',\n        'disabled:[background:linear-gradient(104deg,#adcff6_1.04%,#c5cdf7_98.96%)]',\n      ],\n    },\n    {\n      color: 'default',\n      size: 'medium',\n      variant: 'text',\n      class: ['text-gray-10', 'hover:text-gray-10'],\n    },\n    {\n      color: 'search',\n      variant: 'contained',\n      class: [\n        'bg-gray-13 text-white',\n        'hover:bg-gray-12',\n        'focus:bg-gray-11',\n        'disabled:bg-gray-13/50',\n      ],\n    },\n    {\n      color: 'reset',\n      variant: 'outlined',\n      class: [\n        'border-gray-3 text-gray-13',\n        'hover:border-gray-4',\n        'focus:border-gray-5',\n        'disabled:border-gray-3/50 disabled:text-gray-5',\n      ],\n    },\n    {\n      onlyIcon: true,\n      size: 'small',\n      class: 'h-6 w-6 p-[5px]',\n    },\n    {\n      onlyIcon: true,\n      size: 'medium',\n      class: 'h-8 w-8 p-2',\n    },\n    {\n      onlyIcon: true,\n      size: 'large',\n      class: 'h-9.5 w-9.5 p-2.5',\n    },\n  ],\n  defaultVariants: {\n    variant: 'contained',\n    color: 'primary',\n    size: 'medium',\n    onlyIcon: false,\n  },\n})\n\nconst iconVariants = tv({\n  base: 'ButtonIcon group-focus:text-primary group-disabled:text-gray-5  inline-flex',\n  variants: {\n    size: {\n      medium: '[&>svg]:size-[16px] h-4 w-4',\n      small: '[&>svg]:size-[16px] h-4 w-4',\n      large: 'h-4.5 w-4.5 [&>svg]:size-[18px]',\n    },\n    position: {\n      left: 'mr-2',\n      middle: '',\n      right: 'ml-2',\n    },\n  },\n  compoundVariants: [\n    {\n      position: 'left',\n      size: 'small',\n      class: 'mr-1',\n    },\n    {\n      position: 'right',\n      size: 'small',\n      class: 'ml-1',\n    },\n  ],\n  defaultVariants: {\n    size: 'medium',\n    position: 'middle',\n  },\n})\n\ninterface ButtonIconProps extends React.HTMLAttributes<HTMLDivElement> {\n  position?: 'left' | 'middle' | 'right'\n  children: React.ReactNode\n}\n\ninterface ButtonContextValue {\n  size?: ButtonSize\n  variant?: ButtonVariant\n  color?: ButtonColor\n  disabled?: boolean\n}\n\nexport const ButtonContext = React.createContext<ButtonContextValue>({})\n\nconst ButtonIcon = React.forwardRef<\n  React.ElementRef<typeof BaseButton.Icon>,\n  ButtonIconProps\n>(({ position = 'middle', className, children, ...props }, ref) => {\n  const ctx = React.useContext(ButtonContext)\n\n  return (\n    <BaseButton.Icon\n      ref={ref}\n      data-ui-slot=\"button-icon\"\n      className={cn(\n        iconVariants({\n          position,\n          size: ctx.size,\n        }),\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </BaseButton.Icon>\n  )\n})\n\nButtonIcon.displayName = 'ButtonIcon'\n\ntype ButtonColor =\n  | 'primary'\n  | 'secondary'\n  | 'default'\n  | 'error'\n  | 'light-error'\n  | 'linear-gradient'\n  | 'search'\n  | 'reset'\ntype ButtonVariant = 'contained' | 'outlined' | 'text'\ntype ButtonSize = 'small' | 'medium' | 'large'\n\ninterface ButtonProps\n  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'color'> {\n  /**\n   * 启用插槽模式，允许将按钮行为应用到自定义元素上\n   * @default false\n   */\n  asChild?: boolean\n  /**\n   * 按钮变体风格\n   * @default 'contained'\n   */\n  variant?: ButtonVariant\n  /**\n   * 按钮颜色主题\n   * - primary: 主要颜色，用于重要操作\n   * - secondary: 次要颜色，用于辅助操作\n   * - default: 默认颜色，用于一般操作\n   * - error: 错误颜色，用于危险操作\n   * - light-error: 浅错误颜色，用于错误提示\n   * - linear-gradient: 渐变色，用于特殊场景\n   * - search: 搜索颜色，用于搜索相关操作\n   * - reset: 重置颜色，用于重置操作\n   * @default 'primary'\n   */\n  color?: ButtonColor\n  /**\n   * 按钮尺寸\n   * @default 'medium'\n   */\n  size?: ButtonSize\n}\n\ntype ButtonType = React.ForwardRefExoticComponent<\n  ButtonProps & React.RefAttributes<HTMLButtonElement>\n> & {\n  Icon: typeof ButtonIcon\n}\n\nconst Button: ButtonType = React.forwardRef(\n  (\n    {\n      children,\n      className,\n      variant = 'contained',\n      color,\n      size,\n      disabled,\n      ...props\n    },\n    ref\n  ) => {\n    const contextValue = React.useMemo(\n      () => ({ size, variant, color, disabled }),\n      [size, variant, color, disabled]\n    )\n\n    const onlyIcon = React.useMemo(() => {\n      const childrenArray = React.Children.toArray(children).filter(Boolean)\n      return (\n        childrenArray.length === 1 &&\n        React.isValidElement(childrenArray[0]) &&\n        childrenArray[0].type === ButtonIcon\n      )\n    }, [children])\n\n    const chineseSpacing = React.useMemo(() => {\n      return typeof children === 'string' && isChineseTwoChars(children)\n    }, [children])\n\n    return (\n      <ButtonContext.Provider value={contextValue}>\n        <BaseButton.Root\n          ref={ref}\n          data-ui-slot=\"button\"\n          data-variant={variant}\n          disabled={disabled}\n          className={cn(\n            buttonVariants({ variant, color, size, onlyIcon }),\n            className\n          )}\n          {...props}\n        >\n          {chineseSpacing && typeof children === 'string'\n            ? children.split('').join(' ')\n            : children}\n        </BaseButton.Root>\n      </ButtonContext.Provider>\n    )\n  }\n) as ButtonType\n\nButton.Icon = ButtonIcon\nButton.displayName = 'Button'\n\nexport {\n  Button,\n  ButtonIcon,\n  type ButtonContextValue,\n  type ButtonIconProps,\n  type ButtonProps,\n}\n", "type": "registry:ui"}], "dependencies": ["@imd/base-button"], "registryDependencies": ["ui-loading"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "f7236738d753689e61a83d1b1e6f4a9fa231649d"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 10, "ce": 1, "instability": 0.09090909090909091}, "dependents": ["pro-button", "pro-dialog", "pro-drawer", "pro-dropdown-menu", "pro-fullscreen-dialog", "pro-page-filter", "pro-popconfirm", "pro-poptextarea", "ui-dropdown-menu", "ui-popconfirm"]}