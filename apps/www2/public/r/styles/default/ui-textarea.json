{"name": "ui-textarea", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-textarea/autoresize-textarea.ts", "content": "const styleCache = new WeakMap<Element, CSSStyleDeclaration>()\n\nconst getComputedStyle = (el: Element) => {\n  if (!styleCache.has(el)) {\n    styleCache.set(el, getWindow(el).getComputedStyle(el))\n  }\n  return styleCache.get(el)!\n}\n\nconst ELEMENT_NODE: typeof Node.ELEMENT_NODE = 1\nconst DOCUMENT_NODE: typeof Node.DOCUMENT_NODE = 9\nconst DOCUMENT_FRAGMENT_NODE: typeof Node.DOCUMENT_FRAGMENT_NODE = 11\n\nconst isObject = (v: unknown): v is Record<string, unknown> =>\n  typeof v === 'object' && v !== null\n\nconst isDocument = (el: any): el is Document =>\n  isObject(el) && el.nodeType === DOCUMENT_NODE\nconst isWindow = (el: any): el is Window => isObject(el) && el === el.window\n\nconst getDocument = (\n  el: Element | Window | Node | Document | null | undefined\n) => {\n  if (isDocument(el)) return el\n  if (isWindow(el)) return el.document\n  return el?.ownerDocument ?? document\n}\nconst isNode = (el: any): el is Node =>\n  isObject(el) && el.nodeType !== undefined\n\nconst isShadowRoot = (el: any): el is ShadowRoot =>\n  isNode(el) && el.nodeType === DOCUMENT_FRAGMENT_NODE && 'host' in el\n\nconst isHTMLElement = (el: any): el is HTMLElement =>\n  isObject(el) &&\n  el.nodeType === ELEMENT_NODE &&\n  typeof el.nodeName === 'string'\n\nexport function getWindow(el: Node | ShadowRoot | Document | null | undefined) {\n  if (isShadowRoot(el)) return getWindow(el.host)\n  if (isDocument(el)) return el.defaultView ?? window\n  if (isHTMLElement(el)) return el.ownerDocument?.defaultView ?? window\n  return window\n}\n\nexport const autoresizeTextarea = (el: HTMLTextAreaElement | null) => {\n  if (!el) return\n\n  const style = getComputedStyle(el)\n\n  const win = getWindow(el)\n  const doc = getDocument(el)\n\n  const resize = () => {\n    requestAnimationFrame(() => {\n      el.style.height = 'auto'\n      let newHeight: number\n\n      if (style.boxSizing === 'content-box') {\n        newHeight =\n          el.scrollHeight -\n          (parseFloat(style.paddingTop) + parseFloat(style.paddingBottom))\n      } else {\n        newHeight =\n          el.scrollHeight +\n          parseFloat(style.borderTopWidth) +\n          parseFloat(style.borderBottomWidth)\n      }\n\n      if (\n        style.maxHeight !== 'none' &&\n        newHeight > parseFloat(style.maxHeight)\n      ) {\n        if (style.overflowY === 'hidden') {\n          el.style.overflowY = 'scroll'\n        }\n        newHeight = parseFloat(style.maxHeight)\n      } else if (style.overflowY !== 'hidden') {\n        el.style.overflowY = 'hidden'\n      }\n\n      el.style.height = `${newHeight}px`\n    })\n  }\n\n  el.addEventListener('input', resize)\n  el.form?.addEventListener('reset', resize)\n\n  const elementPrototype = Object.getPrototypeOf(el)\n  const descriptor = Object.getOwnPropertyDescriptor(elementPrototype, 'value')\n  Object.defineProperty(el, 'value', {\n    ...descriptor,\n    set() {\n      // @ts-ignore\n      descriptor?.set?.apply(this, arguments as unknown as [unknown])\n      resize()\n    },\n  })\n\n  const resizeObserver = new win.ResizeObserver(() => {\n    requestAnimationFrame(() => resize())\n  })\n  resizeObserver.observe(el)\n\n  const attrObserver = new win.MutationObserver(() => resize())\n  attrObserver.observe(el, {\n    attributes: true,\n    attributeFilter: ['rows', 'placeholder'],\n  })\n\n  doc.fonts?.addEventListener('loadingdone', resize)\n\n  return () => {\n    el.removeEventListener('input', resize)\n    el.form?.removeEventListener('reset', resize)\n    doc.fonts?.removeEventListener('loadingdone', resize)\n    resizeObserver.disconnect()\n    attrObserver.disconnect()\n  }\n}\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textarea/icon.tsx", "content": "export const ResizeIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      viewBox=\"0 0 10 10\"\n      fill=\"currentColor\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M8.56694 2.31694C8.81102 2.07286 8.81102 1.67714 8.56694 1.43306C8.32286 1.18898 7.92714 1.18898 7.68306 1.43306L1.43306 7.68306C1.18898 7.92714 1.18898 8.32286 1.43306 8.56694C1.67714 8.81102 2.07286 8.81102 2.31694 8.56694L8.56694 2.31694ZM8.56694 5.44194C8.81102 5.19786 8.81102 4.80214 8.56694 4.55806C8.32286 4.31398 7.92714 4.31398 7.68306 4.55806L4.55806 7.68306C4.31398 7.92714 4.31398 8.32286 4.55806 8.56694C4.80214 8.81102 5.19786 8.81102 5.44194 8.56694L8.56694 5.44194Z\"\n      />\n    </svg>\n  )\n}\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textarea/index.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { usePageFilterContext } from '@imd/context'\nimport { tv } from 'tailwind-variants'\n\nimport { autoresizeTextarea } from './autoresize-textarea'\n\ntype Variant = 'outlined' | 'filled' | 'ghost'\n\nconst textAreaVariants = tv({\n  slots: {\n    root: [\n      'relative overflow-hidden rounded-sm border',\n      'hover:[&:not(&:has(textarea:disabled))]:border-primary hover:[&:not(&:has(textarea:disabled))]:bg-white',\n      'has-[textarea:focus]:shadow-component-focus has-[textarea:focus]:border-primary has-[textarea:focus]:bg-white',\n      'has-[textarea:disabled]:cursor-not-allowed has-[textarea:disabled]:bg-gray-4 has-[textarea:disabled]:border-gray-4',\n    ],\n    textarea: [\n      'block px-3',\n      'placeholder:text-gray-7 text-gray-13 w-full resize-none bg-transparent outline-none',\n      'disabled:placeholder:text-gray-8 disabled:text-gray-8 disabled:pointer-events-none',\n    ],\n    charCounter: ['typography-body-small text-gray-7'],\n  },\n  variants: {\n    isHasCharCounter: {\n      true: {\n        textarea: 'pb-8',\n      },\n    },\n    variant: {\n      outlined: {\n        root: ['border-gray-3 has-[textarea:disabled]:bg-gray-1 bg-white'],\n        textarea: [\n          // disabled\n          'disabled:text-gray-5 disabled:placeholder:text-gray-5',\n        ],\n      },\n      filled: {\n        root: ['bg-gray-2 border-gray-2'],\n        textarea: [\n          // disabled\n          'disabled:text-gray-8 disabled:placeholder:text-gray-8 ',\n        ],\n      },\n      ghost: {\n        root: ['border-transparent bg-transparent'],\n      },\n    },\n    hasError: {\n      true: {\n        root: [\n          'bg-red-1 border-red-1',\n          'hover:[&:not(&:has(textarea:disabled))]:border-red-6',\n          'has-[textarea:focus]:shadow-component-focus-error has-[textarea:focus]:border-red-6',\n        ],\n      },\n      false: {},\n    },\n    disabled: {\n      true: {},\n      false: {\n        textarea: 'resize-y',\n      },\n    },\n    size: {\n      small: {\n        textarea: 'typography-paragraph-small py-1.5',\n      },\n      large: {\n        textarea: 'typography-paragraph-medium py-2',\n      },\n    },\n  },\n  defaultVariants: {\n    isHasCharCounter: false,\n    hasError: false,\n    disabled: false,\n    autoResize: false,\n    size: 'small',\n    variant: 'filled',\n  },\n})\n\ntype CharCounterProps = React.TextareaHTMLAttributes<HTMLSpanElement> & {\n  maxCount: number\n  current: number\n}\n\nconst CharCounter = React.forwardRef<HTMLSpanElement, CharCounterProps>(\n  ({ maxCount, current }, ref) => {\n    const { charCounter } = textAreaVariants({})\n\n    return (\n      <span\n        ref={ref}\n        className={charCounter()}\n        data-ui-slot=\"textarea-char-counter\"\n      >\n        {current}/{maxCount}\n      </span>\n    )\n  }\n)\n\nCharCounter.displayName = 'TextareaCharCounter'\n\ntype TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement> & {\n  /** 是否显示错误状态 */\n  hasError?: boolean\n  /** 输入框大小 */\n  size?: 'small' | 'medium' | 'large'\n  /** 是否自动调整大小 */\n  autoResize?: boolean\n  /** 表单项ID */\n  'data-form-item-id'?: string\n  /** 输入框的变体 */\n  variant?: Variant\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  (\n    {\n      className,\n      id,\n      'data-form-item-id': formItemId,\n      disabled,\n      children,\n      hasError,\n      autoResize,\n      size,\n      variant,\n      ...rest\n    },\n    ref\n  ) => {\n    const pageFilterContext = usePageFilterContext('ui-textarea')\n    const mergedVariant = pageFilterContext.variant || variant\n\n    // 将medium映射为small，因为Textarea只支持small和large\n    const mappedSize = size === 'medium' ? 'small' : size\n    const { root, textarea } = React.useMemo(() => {\n      return textAreaVariants({\n        hasError,\n        disabled,\n        size: mappedSize,\n        variant: mergedVariant,\n        isHasCharCounter: !!children,\n      })\n    }, [hasError, disabled, mappedSize, children, mergedVariant])\n    const finalId = formItemId || id\n    const textareaRef = React.useRef<HTMLTextAreaElement | null>(null)\n    React.useEffect(() => {\n      if (textareaRef.current && autoResize) {\n        autoresizeTextarea(textareaRef.current)\n      }\n    }, [autoResize])\n\n    return (\n      <div className={root()} data-ui-slot=\"textarea\">\n        <textarea\n          id={finalId}\n          ref={(el) => {\n            if (typeof ref === 'function') {\n              ref(el)\n            } else if (ref) {\n              ref.current = el\n            }\n            textareaRef.current = el\n          }}\n          className={textarea({ class: className })}\n          data-ui-slot=\"textarea-input\"\n          disabled={!!disabled}\n          {...rest}\n        />\n        {children && (\n          <div\n            className=\"pointer-events-none absolute bottom-1 right-3 flex w-full items-center justify-end\"\n            data-ui-slot=\"textarea-counter-container\"\n          >\n            {children}\n          </div>\n        )}\n      </div>\n    )\n  }\n)\n\nTextarea.displayName = 'TextareaRoot'\n\nexport { Textarea, CharCounter as TextareaCharCounter }\nexport type { TextareaProps, CharCounterProps as TextareaCharCounterProps }\n", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textarea/ui-textarea.test-coverage.md", "content": "# ui-textarea 测试覆盖度文档\n\n## 📊 覆盖度统计\n\n- **总测试数量**: 57 个\n- **通过测试**: 55 个 ✅\n- **跳过测试**: 2 个 ⏭️\n- **失败测试**: 0 个 ❌\n- **覆盖率**: 96.5% (55/57)\n\n## 🧪 测试类别分布\n\n### ✅ 已完全覆盖的功能\n\n#### 基础渲染测试 (4/4)\n\n- **渲染正确性**: 验证组件能正确渲染\n- **可访问性**: axe 无障碍检查通过\n- **Ref转发**: 正确转发ref到textarea元素\n- **数据属性**: 正确设置data-ui-slot属性\n\n#### 属性变体测试\n\n- **variant属性** (6/6): outlined, filled, ghost 各种状态\n- **size属性** (4/4): small, medium, large 各种尺寸\n- **布尔属性** (6/6): hasError, autoResize, disabled\n\n#### 属性组合测试 (10/10)\n\n- 关键属性组合的兼容性\n- 组合状态的可访问性验证\n\n#### 事件处理测试 (3/3)\n\n- **onChange**: 文本输入时正确触发\n- **onFocus**: 焦点获得时正确触发\n- **onBlur**: 焦点失去时正确触发\n\n#### CharCounter子组件测试 (4/4)\n\n- **渲染逻辑**: 根据children存在性决定显示\n- **数据属性**: 正确设置data-ui-slot\n- **容器管理**: 正确控制counter容器显示隐藏\n\n#### 表单集成测试 (3/3)\n\n- **表单ID**: data-form-item-id优先级处理\n- **表单属性**: name, id等属性正确传递\n- **表单上下文**: 在form内正确工作\n\n#### 样式和类名测试 (2/2)\n\n- **自定义类名**: className正确传递到textarea元素\n- **HTML属性**: 原生属性正确保留\n\n#### Context依赖测试 (2/2)\n\n- **Context集成**: usePageFilterContext正确调用\n- **优先级**: prop variant vs context variant\n\n#### 边界值与异常测试 (9/9)\n\n- **children边界**: undefined, null, 空字符串\n- **极值测试**: 长内容, 大数值\n- **无效props**: 运行时错误处理\n\n#### autoResize功能测试 (1/3)\n\n- ✅ **条件调用**: autoResize=false时不调用\n- ⏭️ **功能调用**: 跳过 - 源码useEffect依赖数组有误\n- ⏭️ **minHeight设置**: 跳过 - 源码功能未实现\n\n## ⏭️ 跳过的测试 (需要源码修复)\n\n### 🔴 高优先级源码问题\n\n#### autoResize功能调用缺陷\n\n```typescript\nit.skip('should call autoresizeTextarea when autoResize is true', () => {\n  // TODO (Source Code): ui-textarea 的 useEffect 依赖数组有误，导致autoresizeTextarea不会被调用\n  // 原因: useEffect依赖数组包含textareaRef，但ref对象永远不变，无法在ref被设置后重新运行\n  // 源码位置: apps/www2/registry/default/ui/ui-textarea/index.tsx:147-151\n  // 优先级: 🔴 高优先级 - 核心功能失效\n  // 修复建议:\n  // 1. 移除textareaRef从依赖数组\n  // 2. 或使用useEffect配合ref callback模式\n  // 3. 或使用useLayoutEffect确保DOM更新后执行\n})\n```\n\n**问题描述**:\n\n- useEffect的依赖数组包含 `textareaRef`，但ref对象本身永远不会改变\n- useEffect在首次渲染时运行，但这时 `textareaRef.current` 可能还是 `null`\n- ref回调在DOM节点创建后设置 `textareaRef.current`，但useEffect不会重新运行\n- 导致autoResize功能完全失效\n\n**修复建议**:\n\n1. **选项1**: 移除 `textareaRef` 从依赖数组：`[autoResize]`\n2. **选项2**: 使用ref callback模式直接在ref设置时调用autoresize\n3. **选项3**: 使用 `useLayoutEffect` 确保在DOM更新后执行\n\n### 🟡 中优先级源码问题\n\n#### minHeight设置功能\n\n```typescript\nit.skip('should set minimum height based on computed styles', () => {\n  // TODO (Source Code): ui-textarea 的 minHeight 设置功能未完整实现\n  // 原因: autoresizeTextarea 和主组件都没有设置 minHeight 属性，但测试期望设置36px\n  // 源码位置: apps/www2/registry/default/ui/ui-textarea/index.tsx:147-151\n  //          apps/www2/registry/default/ui/ui-textarea/autoresize-textarea.ts:46-120\n  // 优先级: 🟡 中优先级 - 功能增强需求\n  // 预期行为: 根据 lineHeight + paddingTop + paddingBottom 计算并设置 minHeight\n})\n```\n\n**问题描述**:\n\n- 源码中的 `autoresizeTextarea` 功能没有设置 textarea 的 `minHeight` 样式属性\n- 主组件也没有根据计算样式设置最小高度\n- 测试期望基于 `lineHeight(20px) + paddingTop(8px) + paddingBottom(8px) = 36px` 设置minHeight\n\n**修复建议**:\n\n1. 在 `autoresizeTextarea.ts` 中添加minHeight计算逻辑\n2. 或在主组件的useEffect中设置minHeight\n3. 计算公式: `lineHeight + paddingTop + paddingBottom`\n\n## 📈 测试质量分析\n\n### 🎯 测试覆盖度评估\n\n**优势**:\n\n- 全面的属性变体测试\n- 完整的事件处理覆盖\n- 系统性的边界值测试\n- 良好的可访问性验证\n\n**改进建议**:\n\n- 完善autoResize功能的深度测试\n- 添加更多复杂交互场景\n\n### 🔧 测试技术评估\n\n**Mock策略**:\n\n- ✅ 外部依赖正确Mock (@imd/context, tailwind-variants)\n- ✅ DOM API正确Mock (requestAnimationFrame, getComputedStyle)\n- ✅ autoresize模块正确Mock\n\n**断言策略**:\n\n- ✅ 使用语义化查询 (getByRole)\n- ✅ 重视用户感知的断言\n- ✅ 避免过度测试实现细节\n\n## 📋 待办事项\n\n### 🔴 高优先级\n\n1. **源码修复**: 修复autoResize功能的useEffect依赖数组问题，然后移除对应的it.skip\n\n### 🟡 中优先级\n\n1. **源码修复**: 实现minHeight设置功能，然后移除对应的it.skip\n\n### 🟢 低优先级\n\n_无_\n\n## 🚀 后续优化建议\n\n1. **功能修复**:\n   - **立即处理**: 修复autoResize功能的useEffect依赖数组问题\n   - **计划实现**: 完善minHeight自动计算功能\n\n2. **测试增强**:\n   - 源码修复后，重新启用autoResize相关测试\n   - 考虑添加更多复杂autoResize场景测试\n\n3. **性能优化**:\n   - 验证autoresize功能的性能影响\n   - 考虑debounce机制\n\n---\n\n**文档更新时间**: 2025-01-22 17:20:30  \n**测试框架**: Vitest + @testing-library/react  \n**可访问性工具**: vitest-axe\n", "type": "registry:ui"}], "dependencies": ["@imd/context"], "tags": ["form-control", "input", "textarea", "输入框"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "6bbd0a164dc5fad98cecc5d911b01068aaf4938a"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-poptextarea", "pro-textarea"]}