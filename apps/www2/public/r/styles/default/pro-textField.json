{"name": "pro-textField", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-textField/index.tsx", "content": "import React from 'react'\n\nimport { But<PERSON> } from '@/registry/default/ui/pro-button'\nimport { EyeIcon, EyeOffIcon } from '@/registry/default/ui/ui-textField/icon'\nimport {\n  TextField as BaseTextField,\n  TextFieldAffix,\n  TextFieldInlineAffix,\n  TextFieldInput,\n  TextFieldWrapper,\n  type TextFieldProps as BaseTextFieldProps,\n} from '@/registry/default/ui/ui-textField/index'\n\n/**\n * ProTextField 组件，单标签形式，所有属性均通过 props 传入\n */\ninterface ProTextFieldProps\n  extends Omit<BaseTextFieldProps, 'prefix' | 'suffix' | 'onKeyDown'>,\n    Pick<\n      React.InputHTMLAttributes<HTMLInputElement>,\n      | 'type'\n      | 'min'\n      | 'max'\n      | 'step'\n      | 'pattern'\n      | 'autoComplete'\n      | 'autoFocus'\n      | 'onKeyDown'\n    > {\n  /** 是否禁用 */\n  disabled?: boolean\n  /** 前缀 */\n  prefix?: React.ReactNode\n  /** 后缀 */\n  suffix?: React.ReactNode\n  /** 前缀标签 */\n  addBefore?: React.ReactNode\n  /** 后缀标签 */\n  addAfter?: React.ReactNode\n  /** 是否为搜索按钮 */\n  enterButton?: boolean\n  /** 值 */\n  value?: string | number\n  /** 默认值 */\n  defaultValue?: string | number\n  /** 占位符 */\n  placeholder?: string\n}\n\nconst ProTextField = React.forwardRef<HTMLInputElement, ProTextFieldProps>(\n  (props, forwardRef) => {\n    const {\n      disabled,\n      prefix,\n      suffix,\n      addBefore,\n      addAfter,\n      enterButton = false,\n      placeholder,\n      value,\n      defaultValue,\n      hasError,\n      size,\n      variant,\n      allowClear,\n      ...rest\n    } = props\n    return (\n      <BaseTextField\n        allowClear={allowClear}\n        hasError={hasError}\n        size={size}\n        variant={variant}\n      >\n        {addBefore && <TextFieldAffix>{addBefore}</TextFieldAffix>}\n        <TextFieldWrapper>\n          {prefix && <TextFieldInlineAffix>{prefix}</TextFieldInlineAffix>}\n          <TextFieldInput\n            ref={forwardRef}\n            placeholder={placeholder}\n            disabled={disabled}\n            value={value}\n            defaultValue={defaultValue}\n            {...rest}\n          />\n          {suffix && <TextFieldInlineAffix>{suffix}</TextFieldInlineAffix>}\n        </TextFieldWrapper>\n        {addAfter &&\n          (enterButton ? (\n            addAfter\n          ) : (\n            <TextFieldAffix>{addAfter}</TextFieldAffix>\n          ))}\n      </BaseTextField>\n    )\n  }\n)\n\nProTextField.displayName = 'ProTextField'\n\n/**\n * 搜索框组件属性\n */\ninterface SearchProps extends Omit<ProTextFieldProps, 'addAfter'> {\n  /** 搜索按钮文本，默认为空则显示图标 */\n  searchText?: string\n  /** 搜索按钮点击回调 */\n  onSearch?: (value: string | number) => void\n  /** 搜索按钮属性 */\n  buttonProps?: React.ComponentProps<typeof Button>\n  /** 是否在按钮中显示加载状态 */\n  loading?: boolean\n  /** 是否在按下 Enter 键时触发搜索 */\n  enterToSearch?: boolean\n}\n\n/**\n * 搜索框上下文类型\n */\ninterface SearchContextType {\n  /** 搜索框大小 */\n  size: 'small' | 'medium' | 'large'\n  /** 当前值 */\n  value: string | number\n  /** 是否加载中 */\n  loading?: boolean\n  /** 是否禁用 */\n  disabled?: boolean\n  /** 搜索按钮文本 */\n  searchText?: string\n  /** 搜索回调 */\n  onSearch?: (value: string | number) => void\n  /** 按钮属性 */\n  buttonProps?: React.ComponentProps<typeof Button>\n}\n\n/**\n * 搜索框上下文\n */\nconst SearchContext = React.createContext<SearchContextType>({\n  size: 'large',\n  value: '',\n  loading: false,\n  disabled: false,\n  searchText: '',\n  onSearch: undefined,\n  buttonProps: undefined,\n})\n\n/**\n * 搜索框按钮组件\n */\nconst SearchButton: React.FC = () => {\n  const { size, value, loading, disabled, searchText, onSearch, buttonProps } =\n    React.useContext(SearchContext)\n\n  const getButtonPadding = () => {\n    if (searchText) {\n      switch (size) {\n        case 'small':\n          return '!px-2' // 小号按钮padding\n        case 'medium':\n          return '!px-3' // 中号按钮padding\n        case 'large':\n        default:\n          return '!px-3' // 大号按钮padding\n      }\n    }\n    return ''\n  }\n\n  // 处理搜索按钮点击\n  const handleSearch = React.useCallback(() => {\n    onSearch?.(value)\n  }, [onSearch, value])\n\n  return (\n    <Button\n      className={`rounded-none ${getButtonPadding()}`}\n      onClick={handleSearch}\n      size={size}\n      loading={loading}\n      disabled={disabled}\n      icon={!searchText ? <SearchIcon /> : undefined}\n      type=\"submit\"\n      {...buttonProps}\n    >\n      {searchText}\n    </Button>\n  )\n}\n\n/**\n * 搜索框组件，内置搜索按钮\n */\nconst Search = React.forwardRef<HTMLInputElement, SearchProps>(\n  (props, forwardRef) => {\n    const {\n      searchText,\n      onSearch,\n      buttonProps,\n      loading,\n      value,\n      defaultValue,\n      onChange,\n      onKeyDown,\n      size = 'large',\n      disabled,\n      enterToSearch = true,\n      ...rest\n    } = props\n\n    // 处理受控和非受控模式\n    const [internalValue, setInternalValue] = React.useState(defaultValue || '')\n    const isControlled = value !== undefined\n    const finalValue = isControlled ? value : internalValue\n\n    // 处理输入变化\n    const handleChange = React.useCallback(\n      (e: React.ChangeEvent<HTMLInputElement>) => {\n        if (!isControlled) {\n          setInternalValue(e.target.value)\n        }\n        onChange?.(e)\n      },\n      [isControlled, onChange]\n    )\n\n    // 处理键盘事件 - 添加 Enter 键搜索支持\n    const handleKeyDown = React.useCallback(\n      (e: React.KeyboardEvent<HTMLInputElement>) => {\n        if (enterToSearch && e.key === 'Enter' && !disabled && !loading) {\n          e.preventDefault()\n          onSearch?.(finalValue)\n        }\n        onKeyDown?.(e)\n      },\n      [enterToSearch, disabled, loading, onSearch, finalValue, onKeyDown]\n    )\n\n    // 上下文值\n    const contextValue = React.useMemo(\n      () => ({\n        size,\n        value: finalValue,\n        loading,\n        disabled,\n        searchText,\n        onSearch,\n        buttonProps,\n      }),\n      [size, finalValue, loading, disabled, searchText, onSearch, buttonProps]\n    )\n\n    return (\n      <SearchContext.Provider value={contextValue}>\n        <ProTextField\n          {...rest}\n          size={size}\n          value={value}\n          defaultValue={defaultValue}\n          onChange={handleChange}\n          onKeyDown={handleKeyDown}\n          disabled={disabled}\n          ref={forwardRef}\n          addAfter={<SearchButton />}\n          enterButton\n        />\n      </SearchContext.Provider>\n    )\n  }\n)\n\nSearch.displayName = 'TextField.Search'\n\n// 搜索图标组件\nconst SearchIcon = (props: React.SVGProps<SVGSVGElement>) => {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        d=\"M12.0207 11.0781L14.8762 13.9336L13.9336 14.8762L11.0781 12.0207C10.0158 12.8722 8.68271 13.3352 7.33337 13.3333C4.02127 13.3333 1.33337 10.6454 1.33337 7.33333C1.33337 4.02133 4.02127 1.33333 7.33337 1.33333C10.6454 1.33333 13.3334 4.02133 13.3334 7.33333C13.3353 8.68267 12.8722 10.0158 12.0207 11.0781ZM10.6834 10.5833C11.5294 9.71333 12.0019 8.54693 12 7.33333C12 4.75533 9.91137 2.66667 7.33337 2.66667C4.75537 2.66667 2.66671 4.75533 2.66671 7.33333C2.66671 9.91133 4.75537 12 7.33337 12C8.54697 12.0019 9.71337 11.5294 10.5834 10.6833L10.6834 10.5833Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n\n/**\n * 密码输入框组件属性\n */\ninterface PasswordProps extends Omit<ProTextFieldProps, 'type' | 'suffix'> {\n  /** 初始显示状态，true为显示密码，false为隐藏密码 */\n  defaultVisibility?: boolean\n  /** 受控的显示状态 */\n  visibility?: boolean\n  /** 显示状态变化回调 */\n  onVisibilityChange?: (visible: boolean) => void\n}\n\n/**\n * 密码输入框上下文类型\n */\ninterface PasswordContextType {\n  /** 密码是否可见 */\n  isVisible: boolean\n  /** 切换密码可见性 */\n  toggleVisibility: () => void\n}\n\n/**\n * 密码输入框上下文\n */\nconst PasswordContext = React.createContext<PasswordContextType>({\n  isVisible: false,\n  toggleVisibility: () => {},\n})\n\n/**\n * 密码可见性切换按钮组件\n */\nconst PasswordToggle: React.FC = () => {\n  const { isVisible, toggleVisibility } = React.useContext(PasswordContext)\n  return (\n    <button\n      type=\"button\"\n      onClick={toggleVisibility}\n      className=\"hover:text-primary flex cursor-pointer items-center justify-center text-[#444757] transition-colors\"\n      tabIndex={-1}\n    >\n      {isVisible ? <EyeIcon /> : <EyeOffIcon />}\n    </button>\n  )\n}\n\n/**\n * 密码输入框组件\n */\nconst Password = React.forwardRef<HTMLInputElement, PasswordProps>(\n  (props, forwardRef) => {\n    const {\n      defaultVisibility = false,\n      visibility,\n      onVisibilityChange,\n      ...rest\n    } = props\n\n    // 处理受控和非受控模式\n    const [internalVisibility, setInternalVisibility] =\n      React.useState(defaultVisibility)\n    const isControlled = visibility !== undefined\n    const isVisible = isControlled ? visibility : internalVisibility\n\n    // 处理可见性切换\n    const toggleVisibility = React.useCallback(() => {\n      const newVisibility = !isVisible\n      if (!isControlled) {\n        setInternalVisibility(newVisibility)\n      }\n      onVisibilityChange?.(newVisibility)\n    }, [isVisible, isControlled, onVisibilityChange])\n\n    // 上下文值\n    const contextValue = React.useMemo(\n      () => ({\n        isVisible,\n        toggleVisibility,\n      }),\n      [isVisible, toggleVisibility]\n    )\n\n    return (\n      <PasswordContext.Provider value={contextValue}>\n        <ProTextField\n          {...rest}\n          type={isVisible ? 'text' : 'password'}\n          suffix={<PasswordToggle />}\n          ref={forwardRef}\n        />\n      </PasswordContext.Provider>\n    )\n  }\n)\n\nPassword.displayName = 'TextField.Password'\n\n// 将Search和Password组件作为TextField的静态属性\nconst TextField = ProTextField as typeof ProTextField & {\n  Search: typeof Search\n  Password: typeof Password\n}\nTextField.Search = Search\nTextField.Password = Password\n\nexport { TextField }\nexport type { ProTextFieldProps as TextFieldProps, SearchProps, PasswordProps }\n", "type": "registry:ui"}], "registryDependencies": ["pro-button", "ui-textField"], "tags": ["form-control", "input", "password", "search", "textField", "密码框", "搜索框", "输入框"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "6bbd0a164dc5fad98cecc5d911b01068aaf4938a"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 2, "instability": 0.6666666666666666}, "dependents": ["pro-batch-input"]}