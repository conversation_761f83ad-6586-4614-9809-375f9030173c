{"name": "pro-input-number", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-input-number/clear-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [size, setSize] = useState<'small' | 'medium' | 'large'>('large')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 尺寸选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">尺寸</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={size}\n            onChange={(v) => setSize(v as 'small' | 'medium' | 'large')}\n          >\n            <RadioCard value=\"small\">小</RadioCard>\n            <RadioCard value=\"medium\">中</RadioCard>\n            <RadioCard value=\"large\">大</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">带清除按钮的数字输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            allowClear\n            size={size}\n            placeholder=\"请输入\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n            defaultValue={1234.56}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            设置 allowClear 属性后，输入框有内容时会显示清除按钮\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/control-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\nimport { Button } from '@/registry/default/ui/ui-button'\n\nexport default function Demo() {\n  const [value, setValue] = useState<number>(10086)\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 非受控示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">非受控模式</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            defaultValue={8888}\n            placeholder=\"使用defaultValue，组件内部管理状态\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n          />\n          <div className=\"text-sm text-gray-500\">\n            使用 defaultValue 时，组件内部管理自己的状态，无需外部 state\n          </div>\n        </div>\n      </div>\n\n      {/* 受控示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">受控模式</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            value={value}\n            placeholder=\"使用value和onChange，由React状态控制\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n            min={0}\n            max={99999}\n            onChange={(newValue) => {\n              console.log('value changed:', newValue)\n              setValue(newValue ?? 0)\n            }}\n          />\n\n          <div className=\"flex items-center gap-3\">\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={() => setValue(12345)}\n            >\n              设为12345\n            </Button>\n            <Button variant=\"outlined\" size=\"small\" onClick={() => setValue(0)}>\n              设为0\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={() => setValue(999)}\n            >\n              设为999\n            </Button>\n          </div>\n\n          <div className=\"text-sm text-gray-500\">当前值: {value}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/default-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\n\nexport default function Demo() {\n  const [value, setValue] = useState<number>(1234.56)\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 基本样式 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">基本样式</h3>\n        <div className=\"flex flex-wrap gap-6\">\n          <InputNumber\n            placeholder=\"默认样式\"\n            style={{ width: '100%' }}\n            min={-100}\n          />\n          <InputNumber placeholder=\"禁用状态\" disabled />\n          <InputNumber placeholder=\"错误状态\" hasError />\n        </div>\n      </div>\n\n      {/* 带交互功能 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">带交互功能</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            placeholder=\"请输入\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n            scale={2}\n            value={value}\n            onChange={(newValue) => {\n              setValue(newValue ?? 0)\n              console.log('值已更改:', newValue)\n            }}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            当前值: {value} (格式化后: ￥{value.toLocaleString()}元)\n          </div>\n        </div>\n      </div>\n\n      {/* 格式设置 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">格式设置</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            placeholder=\"小数点后保留2位\"\n            scale={2}\n            defaultValue={123.456}\n          />\n\n          <InputNumber\n            placeholder=\"使用千分位分隔符\"\n            thousandsSeparator=\",\"\n            defaultValue={1234567.89}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            通过 scale 和 thousandsSeparator 属性可设置数字格式\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/disabled-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [disabled, setDisabled] = useState(true)\n  const [size, setSize] = useState<'small' | 'medium' | 'large'>('large')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-4\">\n        {/* 禁用状态选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">禁用状态</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={disabled ? 'true' : 'false'}\n              onChange={(v) => setDisabled(v === 'true')}\n            >\n              <RadioCard value=\"false\">启用</RadioCard>\n              <RadioCard value=\"true\">禁用</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n\n        {/* 尺寸选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">尺寸</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={size}\n              onChange={(v) => setSize(v as 'small' | 'medium' | 'large')}\n            >\n              <RadioCard value=\"small\">小</RadioCard>\n              <RadioCard value=\"medium\">中</RadioCard>\n              <RadioCard value=\"large\">大</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">禁用状态的数字输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            size={size}\n            placeholder=\"请输入\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n            disabled={disabled}\n            defaultValue={disabled ? 1234.56 : undefined}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            禁用状态下，输入框呈现灰色外观，用户无法与之交互\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/form-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { DollarSign, Package, ShoppingCart, Users } from 'lucide-react'\nimport { useForm } from 'react-hook-form'\nimport * as z from 'zod'\n\nimport { Button } from '@/registry/default/ui/pro-button'\nimport { Form } from '@/registry/default/ui/pro-form'\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\n\nconst formSchema = z.object({\n  price: z\n    .number()\n    .min(0.01, { message: '价格必须大于0' })\n    .max(999999, { message: '价格不能超过999999' }),\n  quantity: z\n    .number()\n    .int({ message: '数量必须为整数' })\n    .min(1, { message: '数量至少为1' })\n    .max(9999, { message: '数量不能超过9999' }),\n  discount: z\n    .number()\n    .min(0, { message: '折扣不能小于0' })\n    .max(100, { message: '折扣不能超过100' })\n    .optional(),\n  weight: z\n    .number()\n    .min(0.1, { message: '重量至少为0.1kg' })\n    .max(1000, { message: '重量不能超过1000kg' })\n    .optional(),\n})\n\nexport default function ProInputNumberFormDemo() {\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      price: 0.1,\n      quantity: 1,\n      discount: 0,\n      weight: 0.1,\n    },\n  })\n\n  const onSubmit = (values: z.infer<typeof formSchema>) => {\n    console.log('表单提交数据:', values)\n    alert(`表单提交成功！\\n${JSON.stringify(values, null, 2)}`)\n  }\n\n  const watchedPrice = form.watch('price')\n  const watchedQuantity = form.watch('quantity')\n  const watchedDiscount = form.watch('discount') || 0\n\n  // 计算总价\n  const totalPrice = watchedPrice * watchedQuantity\n  const finalPrice = totalPrice * (1 - watchedDiscount / 100)\n\n  return (\n    <div className=\"mx-auto max-w-2xl space-y-6\">\n      <div className=\"space-y-2 text-center\">\n        <h2 className=\"text-lg font-semibold\">商品订单表单</h2>\n        <p className=\"text-sm text-gray-600\">\n          展示 ProInputNumber 与 ProForm 结合的高级数字输入场景\n        </p>\n      </div>\n\n      <Form form={form} onSubmit={onSubmit} className=\"space-y-4\">\n        {/* 商品价格 */}\n        <Form.Item\n          name=\"price\"\n          label=\"商品价格\"\n          required\n          tooltip=\"请输入商品的单价\"\n        >\n          <InputNumber\n            placeholder=\"请输入价格\"\n            prefix={<DollarSign className=\"h-3.5 w-3.5 text-gray-400\" />}\n            suffix=\"¥\"\n            min={0.01}\n            max={999999}\n            scale={2}\n            step={0.01}\n            allowClear\n            size=\"large\"\n            variant=\"outlined\"\n          />\n        </Form.Item>\n\n        {/* 购买数量 */}\n        <Form.Item\n          name=\"quantity\"\n          label=\"购买数量\"\n          required\n          tooltip=\"请输入购买的商品数量\"\n        >\n          <InputNumber\n            placeholder=\"请输入数量\"\n            prefix={<ShoppingCart className=\"h-3.5 w-3.5 text-gray-400\" />}\n            suffix=\"件\"\n            min={1}\n            max={9999}\n            step={1}\n            allowClear\n            size=\"medium\"\n            variant=\"filled\"\n          />\n        </Form.Item>\n\n        {/* 折扣百分比 */}\n        <Form.Item name=\"discount\" label=\"折扣百分比\">\n          <InputNumber\n            placeholder=\"请输入折扣\"\n            suffix=\"%\"\n            min={0}\n            max={100}\n            scale={1}\n            step={5}\n            allowClear\n            size=\"medium\"\n            variant=\"outlined\"\n          />\n        </Form.Item>\n\n        {/* 商品重量 */}\n        <Form.Item\n          name=\"weight\"\n          label=\"商品重量\"\n          tooltip=\"用于计算运费的商品重量\"\n        >\n          <InputNumber\n            placeholder=\"请输入重量\"\n            prefix={<Package className=\"h-3.5 w-3.5 text-gray-400\" />}\n            suffix=\"kg\"\n            min={0.1}\n            max={1000}\n            scale={2}\n            step={0.1}\n            allowClear\n            size=\"small\"\n            variant=\"ghost\"\n          />\n        </Form.Item>\n\n        {/* 价格计算展示 */}\n        <div className=\"rounded-lg bg-blue-50 p-4\">\n          <h3 className=\"mb-2 text-sm font-medium text-blue-900\">价格计算</h3>\n          <div className=\"space-y-1 text-sm text-blue-800\">\n            <div>单价: ¥{watchedPrice?.toFixed(2) || '0.00'}</div>\n            <div>数量: {watchedQuantity || 0} 件</div>\n            <div>小计: ¥{totalPrice?.toFixed(2) || '0.00'}</div>\n            <div>折扣: {watchedDiscount}%</div>\n            <div className=\"border-t border-blue-200 pt-1 font-medium\">\n              总价: ¥{finalPrice?.toFixed(2) || '0.00'}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex gap-3 pt-4\">\n          <Button type=\"submit\" color=\"primary\" className=\"flex-1\">\n            提交订单\n          </Button>\n          <Button type=\"button\" variant=\"outlined\" onClick={() => form.reset()}>\n            重置表单\n          </Button>\n        </div>\n      </Form>\n\n      {/* 表单状态展示 */}\n      <div className=\"mt-6 rounded-lg bg-gray-50 p-4\">\n        <h3 className=\"mb-2 text-sm font-medium\">表单状态</h3>\n        <div className=\"space-y-1 text-xs\">\n          <div>是否有效: {form.formState.isValid ? '✅' : '❌'}</div>\n          <div>是否已提交: {form.formState.isSubmitted ? '✅' : '❌'}</div>\n          <div>错误数量: {Object.keys(form.formState.errors).length}</div>\n          <div>\n            已修改字段: {Object.keys(form.formState.dirtyFields).length}\n          </div>\n        </div>\n\n        {/* 当前表单值展示 */}\n        <div className=\"mt-3 border-t border-gray-200 pt-3\">\n          <h4 className=\"mb-2 text-xs font-medium\">当前值:</h4>\n          <pre className=\"overflow-auto text-xs text-gray-600\">\n            {JSON.stringify(form.watch(), null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/hasError-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [hasError, setHasError] = useState(true)\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-4\">\n        {/* 错误状态选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">错误状态</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={hasError ? 'true' : 'false'}\n              onChange={(v) => setHasError(v === 'true')}\n            >\n              <RadioCard value=\"false\">正常</RadioCard>\n              <RadioCard value=\"true\">错误</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n\n        {/* 变体选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">变体</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={variant}\n              onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n            >\n              <RadioCard value=\"outlined\">outlined</RadioCard>\n              <RadioCard value=\"filled\">filled</RadioCard>\n              <RadioCard value=\"ghost\">ghost</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">错误状态的数字输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            variant={variant}\n            hasError={hasError}\n            placeholder=\"请输入有效金额\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n            defaultValue={hasError ? 9999999 : 1000}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            错误状态下，输入框呈现红色外观，用于表单验证失败时提供视觉反馈\n          </div>\n        </div>\n      </div>\n\n      {/* 错误状态下的禁用组件 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">禁用且错误状态的数字输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            variant={variant}\n            hasError={hasError}\n            disabled\n            placeholder=\"请输入有效金额\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n            defaultValue={9999999}\n          />\n\n          <div className=\"text-sm text-gray-500\">禁用状态下的错误样式展示</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/icon.tsx", "content": "export const RoomIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 16 16\"\n      fill=\"currentColor\"\n      {...props}\n    >\n      <path\n        d=\"M7.07926 0.222253C7.31275 -0.007434 7.6873 -0.007434 7.92079 0.222253L14.6708 6.86227C14.907 7.09465 14.9101 7.47453 14.6778 7.71076C14.4454 7.947 14.0655 7.95012 13.8293 7.71773L13 6.90201V12.5C13 12.7761 12.7762 13 12.5 13H2.50002C2.22388 13 2.00002 12.7761 2.00002 12.5V6.90201L1.17079 7.71773C0.934558 7.95012 0.554672 7.947 0.32229 7.71076C0.0899079 7.47453 0.0930283 7.09465 0.32926 6.86227L7.07926 0.222253ZM7.50002 1.49163L12 5.91831V12H10V8.49999C10 8.22385 9.77617 7.99999 9.50002 7.99999H6.50002C6.22388 7.99999 6.00002 8.22385 6.00002 8.49999V12H3.00002V5.91831L7.50002 1.49163ZM7.00002 12H9.00002V8.99999H7.00002V12Z\"\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n      ></path>\n    </svg>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/prefix-suffix-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\n\nimport { RoomIcon } from './icon'\n\nexport default function Demo() {\n  return (\n    <div className=\"space-y-8\">\n      {/* 文本前缀后缀 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">文本前缀后缀</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            placeholder=\"请输入金额\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n            defaultValue={1234.56}\n          />\n\n          <InputNumber\n            placeholder=\"请输入长度\"\n            thousandsSeparator=\".\"\n            suffix=\"cm\"\n            defaultValue={180}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            可以在输入框前后添加文本标识，如货币符号、单位等\n          </div>\n        </div>\n      </div>\n\n      {/* 图标前缀后缀 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">图标前缀后缀</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            placeholder=\"请输入楼号\"\n            thousandsSeparator=\",\"\n            prefix={<RoomIcon className=\"flex h-3.5 w-3.5 items-center\" />}\n            suffix=\"栋\"\n            defaultValue={8}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            可以在输入框前添加图标，提高视觉识别度\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/size-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [size, setSize] = useState<'small' | 'medium' | 'large'>('medium')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 尺寸选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">尺寸</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={size}\n            onChange={(v) => setSize(v as 'small' | 'medium' | 'large')}\n          >\n            <RadioCard value=\"small\">小</RadioCard>\n            <RadioCard value=\"medium\">中</RadioCard>\n            <RadioCard value=\"large\">大</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同尺寸的数字输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            size={size}\n            placeholder=\"请输入\"\n            thousandsSeparator=\",\"\n            prefix=\"￥\"\n            suffix=\"元\"\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            当前尺寸:{' '}\n            {size === 'small' ? '小' : size === 'medium' ? '中' : '大'}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-input-number/variant-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { InputNumber } from '@/registry/default/ui/pro-input-number'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 变体选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">变体</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={variant}\n            onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n          >\n            <RadioCard value=\"outlined\">outlined</RadioCard>\n            <RadioCard value=\"filled\">filled</RadioCard>\n            <RadioCard value=\"ghost\">ghost</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同变体的输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            allowClear\n            placeholder=\"请输入\"\n            min={-10}\n            max={10000}\n            scale={2}\n            variant={variant}\n          />\n          <InputNumber\n            disabled\n            allowClear\n            placeholder=\"请输入\"\n            min={-10}\n            max={10000}\n            scale={2}\n            variant={variant}\n          />\n          <div className=\"text-sm text-gray-500\">当前变体: {variant}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "fea4e1d82bc25f380ede74f21aca061cb5fd3db5"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}