{"name": "ui-input-number", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-input-number/affix-demo.tsx", "content": "import {\n  InputNumber,\n  InputNumberAffix,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nexport default function Demo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber allowClear thousandsSeparator=\",\">\n          <InputNumberAffix>距离</InputNumberAffix>\n          <InputNumberWrapper>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n          <InputNumberAffix>KM</InputNumberAffix>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber allowClear thousandsSeparator=\",\">\n          <InputNumberAffix>距离</InputNumberAffix>\n          <InputNumberWrapper>\n            <InputNumberInput disabled placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n          <InputNumberAffix>KM</InputNumberAffix>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/clear-demo.tsx", "content": "import {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nexport default function Demo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber\n          size=\"small\"\n          min={10}\n          max={99999}\n          allowClear\n          thousandsSeparator=\",\"\n        >\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber size=\"medium\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber size=\"large\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/control-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Button } from '@/components/ui/button'\nimport {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nexport default function Demo() {\n  const [value1, setValue1] = useState<number | string | undefined>(100)\n\n  const handleValue1Change = (value: number | null) => {\n    setValue1(value === null ? undefined : value)\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 基础受控示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">基础受控示例</h3>\n        <div className=\"flex items-center gap-6\">\n          <InputNumber\n            allowClear\n            min={0}\n            max={9999}\n            thousandsSeparator=\",\"\n            value={value1}\n            onChange={handleValue1Change}\n            className=\"w-[240px]\"\n          >\n            <InputNumberWrapper>\n              <InputNumberInlineAffix>$</InputNumberInlineAffix>\n              <InputNumberInput placeholder=\"请输入\" />\n            </InputNumberWrapper>\n            <InputNumberControl>\n              <InputNumberIncrementTrigger />\n              <InputNumberDecrementTrigger />\n            </InputNumberControl>\n          </InputNumber>\n          <div className=\"space-x-3\">\n            <Button size=\"sm\" variant=\"outline\" onClick={() => setValue1(1000)}>\n              设为 1,000\n            </Button>\n            <Button size=\"sm\" variant=\"outline\" onClick={() => setValue1(2000)}>\n              设为 2,000\n            </Button>\n          </div>\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          当前值: {value1 !== undefined ? value1 : '空'}\n        </div>\n      </div>\n\n      {/* 使用默认值示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">使用默认值示例</h3>\n        <div className=\"flex items-center gap-6\">\n          <InputNumber\n            allowClear\n            min={0}\n            max={9999}\n            thousandsSeparator=\",\"\n            className=\"w-[240px]\"\n          >\n            <InputNumberWrapper>\n              <InputNumberInlineAffix>¥</InputNumberInlineAffix>\n              <InputNumberInput\n                placeholder=\"请输入\"\n                defaultValue={500}\n                onChange={(e) => console.log(e)}\n              />\n            </InputNumberWrapper>\n            <InputNumberControl>\n              <InputNumberIncrementTrigger />\n              <InputNumberDecrementTrigger />\n            </InputNumberControl>\n          </InputNumber>\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          使用 defaultValue 时，组件内部管理自己的状态，无需外部 state\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/default-demo.tsx", "content": "'use client'\n\nimport {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nexport default function Demo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber min={-10} max={10000} scale={2}>\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput\n              placeholder=\"请输入\"\n              defaultValue={1.1}\n              onChange={(value) => {\n                console.log('value', value)\n              }}\n            />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/disabled-demo.tsx", "content": "import {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nexport default function Demo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber size=\"small\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput disabled placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber size=\"medium\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput disabled placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber size=\"large\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput disabled placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/form-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { DollarSign, Package, ShoppingCart } from 'lucide-react'\nimport { useForm } from 'react-hook-form'\nimport * as z from 'zod'\n\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/registry/default/ui/ui-form'\nimport {\n  InputNumber,\n  InputNumberAffix,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIcon,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nconst formSchema = z.object({\n  price: z\n    .number()\n    .min(0.01, { message: '价格必须大于0' })\n    .max(999999, { message: '价格不能超过999999' }),\n  quantity: z\n    .number()\n    .int({ message: '数量必须为整数' })\n    .min(1, { message: '数量至少为1' })\n    .max(9999, { message: '数量不能超过9999' }),\n  discount: z\n    .number()\n    .min(0, { message: '折扣不能小于0' })\n    .max(100, { message: '折扣不能超过100' })\n    .optional(),\n  weight: z\n    .number()\n    .min(0.1, { message: '重量至少为0.1kg' })\n    .max(1000, { message: '重量不能超过1000kg' })\n    .optional(),\n})\n\nexport default function InputNumberFormDemo() {\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      price: 1,\n      quantity: 1,\n      discount: 0,\n      weight: 1,\n    },\n  })\n\n  const onSubmit = (values: z.infer<typeof formSchema>) => {\n    console.log('表单提交数据:', values)\n    alert(`表单提交成功！\\n${JSON.stringify(values, null, 2)}`)\n  }\n\n  const watchedPrice = form.watch('price')\n  const watchedQuantity = form.watch('quantity')\n  const watchedDiscount = form.watch('discount') || 0\n\n  // 计算总价\n  const totalPrice = watchedPrice * watchedQuantity\n  const finalPrice = totalPrice * (1 - watchedDiscount / 100)\n  return (\n    <div className=\"mx-auto max-w-2xl space-y-6\">\n      <div className=\"space-y-2 text-center\">\n        <h2 className=\"text-lg font-semibold\">商品订单表单</h2>\n        <p className=\"text-sm text-gray-600\">\n          展示 InputNumber 与 Form 结合的完整数字输入场景\n        </p>\n      </div>\n\n      <Form {...form} onSubmit={onSubmit} className=\"space-y-6\">\n        {/* 商品价格 */}\n        <FormField\n          control={form.control}\n          name=\"price\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"flex items-center gap-2\">\n                <DollarSign className=\"h-3.5 w-3.5\" />\n                商品价格 *\n              </FormLabel>\n              <FormControl>\n                <InputNumber\n                  min={0.01}\n                  max={999999}\n                  scale={2}\n                  step={0.01}\n                  allowClear\n                  size=\"large\"\n                  variant=\"outlined\"\n                  {...field}\n                >\n                  <InputNumberAffix>¥</InputNumberAffix>\n                  <InputNumberWrapper>\n                    <InputNumberInput placeholder=\"请输入价格\" />\n                  </InputNumberWrapper>\n                  <InputNumberControl>\n                    <InputNumberIncrementTrigger />\n                    <InputNumberDecrementTrigger />\n                  </InputNumberControl>\n                </InputNumber>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        {/* 购买数量 */}\n        <FormField\n          control={form.control}\n          name=\"quantity\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"flex items-center gap-2\">\n                <ShoppingCart className=\"h-3.5 w-3.5\" />\n                购买数量 *\n              </FormLabel>\n              <FormControl>\n                <InputNumber\n                  min={1}\n                  max={9999}\n                  step={1}\n                  allowClear\n                  size=\"medium\"\n                  variant=\"filled\"\n                  {...field}\n                >\n                  <InputNumberWrapper>\n                    <InputNumberIcon>\n                      <ShoppingCart className=\"h-3.5 w-3.5\" />\n                    </InputNumberIcon>\n                    <InputNumberInput placeholder=\"请输入数量\" />\n                    <InputNumberInlineAffix>件</InputNumberInlineAffix>\n                  </InputNumberWrapper>\n                  <InputNumberControl>\n                    <InputNumberIncrementTrigger />\n                    <InputNumberDecrementTrigger />\n                  </InputNumberControl>\n                </InputNumber>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        {/* 折扣百分比 */}\n        <FormField\n          control={form.control}\n          name=\"discount\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>折扣百分比</FormLabel>\n              <FormControl>\n                <InputNumber\n                  min={0}\n                  max={100}\n                  scale={1}\n                  step={5}\n                  allowClear\n                  size=\"medium\"\n                  variant=\"outlined\"\n                  {...field}\n                >\n                  <InputNumberWrapper>\n                    <InputNumberInput placeholder=\"请输入折扣\" />\n                    <InputNumberInlineAffix>%</InputNumberInlineAffix>\n                  </InputNumberWrapper>\n                  <InputNumberControl>\n                    <InputNumberIncrementTrigger />\n                    <InputNumberDecrementTrigger />\n                  </InputNumberControl>\n                </InputNumber>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        {/* 商品重量 */}\n        <FormField\n          control={form.control}\n          name=\"weight\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel className=\"flex items-center gap-2\">\n                <Package className=\"h-3.5 w-3.5\" />\n                商品重量\n              </FormLabel>\n              <FormControl>\n                <InputNumber\n                  min={0.1}\n                  max={1000}\n                  scale={2}\n                  step={0.1}\n                  allowClear\n                  size=\"small\"\n                  variant=\"ghost\"\n                  {...field}\n                >\n                  <InputNumberWrapper>\n                    <InputNumberInput placeholder=\"请输入重量\" />\n                    <InputNumberInlineAffix>kg</InputNumberInlineAffix>\n                  </InputNumberWrapper>\n                  <InputNumberControl>\n                    <InputNumberIncrementTrigger />\n                    <InputNumberDecrementTrigger />\n                  </InputNumberControl>\n                </InputNumber>\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        {/* 价格计算展示 */}\n        <div className=\"rounded-lg bg-blue-50 p-4\">\n          <h3 className=\"mb-2 text-sm font-medium text-blue-900\">价格计算</h3>\n          <div className=\"space-y-1 text-sm text-blue-800\">\n            <div>单价: ¥{watchedPrice || '0.00'}</div>\n            <div>数量: {watchedQuantity || 0} 件</div>\n            <div>小计: ¥{totalPrice || '0.00'}</div>\n            <div>折扣: {watchedDiscount}%</div>\n            <div className=\"border-t border-blue-200 pt-1 font-medium\">\n              总价: ¥{finalPrice || '0.00'}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex gap-3 pt-4\">\n          <Button type=\"submit\" className=\"flex-1\">\n            提交订单\n          </Button>\n          <Button type=\"button\" variant=\"outlined\" onClick={() => form.reset()}>\n            重置表单\n          </Button>\n        </div>\n      </Form>\n\n      {/* 表单状态展示 */}\n      <div className=\"mt-6 rounded-lg bg-gray-50 p-4\">\n        <h3 className=\"mb-2 text-sm font-medium\">表单状态</h3>\n        <div className=\"space-y-1 text-xs\">\n          <div>是否有效: {form.formState.isValid ? '✅' : '❌'}</div>\n          <div>是否已提交: {form.formState.isSubmitted ? '✅' : '❌'}</div>\n          <div>错误数量: {Object.keys(form.formState.errors).length}</div>\n          <div>\n            已修改字段: {Object.keys(form.formState.dirtyFields).length}\n          </div>\n        </div>\n\n        {/* 当前表单值展示 */}\n        <div className=\"mt-3 border-t border-gray-200 pt-3\">\n          <h4 className=\"mb-2 text-xs font-medium\">当前值:</h4>\n          <pre className=\"overflow-auto text-xs text-gray-600\">\n            {JSON.stringify(form.watch(), null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/hasError-demo.tsx", "content": "import React from 'react'\n\nimport {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nexport default function Demo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber hasError size=\"small\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber hasError size=\"medium\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber hasError size=\"large\" allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/icon-demo.tsx", "content": "import React from 'react'\n\nimport {\n  InputNumber,\n  InputNumberAffix,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIcon,\n  InputNumberIncrementTrigger,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nimport { RoomIcon } from './icon'\n\nexport default function Demo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberIcon>\n              <RoomIcon />\n            </InputNumberIcon>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n          <InputNumberAffix>栋</InputNumberAffix>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/icon.tsx", "content": "export const RoomIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 16 16\"\n      fill=\"currentColor\"\n      {...props}\n    >\n      <path\n        d=\"M7.07926 0.222253C7.31275 -0.007434 7.6873 -0.007434 7.92079 0.222253L14.6708 6.86227C14.907 7.09465 14.9101 7.47453 14.6778 7.71076C14.4454 7.947 14.0655 7.95012 13.8293 7.71773L13 6.90201V12.5C13 12.7761 12.7762 13 12.5 13H2.50002C2.22388 13 2.00002 12.7761 2.00002 12.5V6.90201L1.17079 7.71773C0.934558 7.95012 0.554672 7.947 0.32229 7.71076C0.0899079 7.47453 0.0930283 7.09465 0.32926 6.86227L7.07926 0.222253ZM7.50002 1.49163L12 5.91831V12H10V8.49999C10 8.22385 9.77617 7.99999 9.50002 7.99999H6.50002C6.22388 7.99999 6.00002 8.22385 6.00002 8.49999V12H3.00002V5.91831L7.50002 1.49163ZM7.00002 12H9.00002V8.99999H7.00002V12Z\"\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n      ></path>\n    </svg>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/inline-affix-demo.tsx", "content": "import {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\n\nexport default function Demo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>￥</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n            <InputNumberInlineAffix>RMB</InputNumberInlineAffix>\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <InputNumber allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInput placeholder=\"请输入\" />\n            <InputNumberInlineAffix>美元</InputNumberInlineAffix>\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/size-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nconst SIZES = [\n  { value: 'small', label: '小' },\n  { value: 'medium', label: '中' },\n  { value: 'large', label: '大' },\n] as const\n\ntype Size = 'small' | 'medium' | 'large'\n\nexport default function Demo() {\n  const [selectedSize, setSelectedSize] = useState<Size>('medium')\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h3 className=\"mb-3 text-sm font-medium\">选择尺寸：</h3>\n        <RadioCards\n          value={selectedSize}\n          onChange={(value) => setSelectedSize(value as Size)}\n        >\n          {SIZES.map((size) => (\n            <RadioCard key={size.value} value={size.value}>\n              {size.label}\n            </RadioCard>\n          ))}\n        </RadioCards>\n      </div>\n\n      <div className=\"w-[300px]\">\n        <InputNumber size={selectedSize} allowClear thousandsSeparator=\",\">\n          <InputNumberWrapper>\n            <InputNumberInlineAffix>$</InputNumberInlineAffix>\n            <InputNumberInput placeholder=\"请输入\" />\n          </InputNumberWrapper>\n          <InputNumberControl>\n            <InputNumberIncrementTrigger />\n            <InputNumberDecrementTrigger />\n          </InputNumberControl>\n        </InputNumber>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-input-number/variant-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport {\n  InputNumber,\n  InputNumberControl,\n  InputNumberDecrementTrigger,\n  InputNumberIncrementTrigger,\n  InputNumberInlineAffix,\n  InputNumberInput,\n  InputNumberWrapper,\n} from '@/registry/default/ui/ui-input-number'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 变体选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">变体</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={variant}\n            onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n          >\n            <RadioCard value=\"outlined\">outlined</RadioCard>\n            <RadioCard value=\"filled\">filled</RadioCard>\n            <RadioCard value=\"ghost\">ghost</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同变体的输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <InputNumber\n            allowClear\n            min={-10}\n            max={10000}\n            scale={2}\n            variant={variant}\n          >\n            <InputNumberWrapper>\n              <InputNumberInlineAffix>$</InputNumberInlineAffix>\n              <InputNumberInput placeholder=\"请输入\" defaultValue={1.1} />\n            </InputNumberWrapper>\n            <InputNumberControl>\n              <InputNumberIncrementTrigger />\n              <InputNumberDecrementTrigger />\n            </InputNumberControl>\n          </InputNumber>\n          <InputNumber\n            allowClear\n            min={-10}\n            max={10000}\n            scale={2}\n            variant={variant}\n          >\n            <InputNumberWrapper>\n              <InputNumberInlineAffix>$</InputNumberInlineAffix>\n              <InputNumberInput\n                disabled\n                placeholder=\"请输入\"\n                defaultValue={1.1}\n              />\n            </InputNumberWrapper>\n            <InputNumberControl>\n              <InputNumberIncrementTrigger />\n              <InputNumberDecrementTrigger />\n            </InputNumberControl>\n          </InputNumber>\n          <div className=\"text-sm text-gray-500\">当前变体: {variant}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "fea4e1d82bc25f380ede74f21aca061cb5fd3db5"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-input-number"]}