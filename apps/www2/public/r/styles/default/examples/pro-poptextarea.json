{"name": "pro-poptextarea", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-poptextarea/control-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Poptextarea } from '@/registry/default/ui/pro-poptextarea'\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nexport default function Demo() {\n  const [value, setValue] = useState(\n    'SF123456789012\\nSF234567890123\\nSF345678901234\\nSF456789012345\\nSF567890123456'\n  )\n  const [items, setItems] = useState<string[]>([\n    'SF123456789012',\n    'SF234567890123',\n    'SF345678901234',\n    'SF456789012345',\n    'SF567890123456',\n  ])\n  return (\n    <div className=\"w-[400px] space-y-8\">\n      <TextField\n        variant=\"outlined\"\n        placeholder=\"请输入\"\n        value={value}\n        onChange={(e) => setValue(e.target.value)}\n        suffix={\n          <Poptextarea\n            placeholder=\"多行搜索输入\"\n            onConfirm={(items, value) => {\n              setValue(value)\n              setItems(items)\n            }}\n            value={value}\n            onChange={(e) => setValue(e.target.value)}\n          />\n        }\n      />\n      <div className=\"mb-2 text-sm text-gray-500\">分割结果（受控模式）</div>\n      <div className=\"rounded-md border border-gray-200 p-3\">\n        <pre className=\"overflow-auto text-sm text-gray-700\">\n          {JSON.stringify(items, null, 2)}\n        </pre>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-poptextarea/custom-split-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button } from '@/registry/default/ui/pro-button'\nimport { TextField } from '@/registry/default/ui/pro-textField'\nimport { Poptextarea } from '@/registry/default/ui/pro-poptextarea'\n\nimport { ExpandIcon } from './icon'\n\nexport default function Demo() {\n  const [items, setItems] = React.useState<string[]>([])\n  const [rawValue, setRawValue] = React.useState<string>('')\n  // 仅支持换行分隔，不支持逗号\n  const customSplitBatchValue = (input: string) => {\n    return input\n      .split('\\n')\n      .map((line) => line.trim())\n      .filter((line) => line.length > 0)\n  }\n\n  return (\n    <div className=\"w-[400px] space-y-8\">\n      <TextField\n        variant=\"outlined\"\n        placeholder=\"请输入（仅支持换行分隔）\"\n        value={rawValue}\n        suffix={\n          <Poptextarea\n            customSplitBatchValue={customSplitBatchValue}\n            placeholder=\"每行输入一个内容，不支持逗号分隔\"\n            onConfirm={(items, rawValue) => {\n              setItems(items)\n              setRawValue(rawValue)\n            }}\n            trigger={\n              <Button\n                className=\"mr-[-8px]\"\n                size=\"small\"\n                color=\"secondary\"\n                icon={<ExpandIcon />}\n              />\n            }\n          />\n        }\n      />\n      <div className=\"mb-2 text-sm text-gray-500\">分割结果（仅换行分隔）</div>\n      <div className=\"rounded-md border border-gray-200 p-3\">\n        <pre className=\"overflow-auto text-sm text-gray-700\">\n          {JSON.stringify(items, null, 2)}\n        </pre>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-poptextarea/default-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { But<PERSON> } from '@/registry/default/ui/pro-button'\nimport { Poptextarea } from '@/registry/default/ui/pro-poptextarea'\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nimport { ExpandIcon } from './icon'\n\nexport default function Demo() {\n  return (\n    <div className=\"space-y-8\">\n      <div>\n        <div className=\"mb-1 text-sm text-gray-500\">默认触发器</div>\n        <Poptextarea />\n      </div>\n      <div>\n        <div className=\"mb-1 text-sm text-gray-500\">结合TextField使用</div>\n        <TextField\n          variant=\"outlined\"\n          placeholder=\"请输入\"\n          suffix={\n            <Poptextarea\n              trigger={\n                <Button\n                  className=\"mr-[-8px]\"\n                  size=\"small\"\n                  color=\"secondary\"\n                  icon={<ExpandIcon />}\n                />\n              }\n            />\n          }\n        />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-poptextarea/icon.tsx", "content": "const ExpandIcon = () => {\n  return (\n    <svg\n      fill=\"currentColor\"\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path d=\"M10.2584 2.91667H8.16667V1.75H12.25V5.83333H11.0833V3.74162L8.57914 6.24581L7.75419 5.42085L10.2584 2.91667ZM1.75 8.16667H2.91667V10.2584L5.42085 7.75419L6.24581 8.57914L3.74162 11.0833H5.83333V12.25H1.75V8.16667Z\" />\n    </svg>\n  )\n}\n\nexport { ExpandIcon }\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "e985dbe3995ecab41158e5abc6b261a04a7d678d"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-batch-input"]}