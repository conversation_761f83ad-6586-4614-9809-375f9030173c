{"name": "pro-batch-input", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-batch-input/controlled-uncontrolled-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { ProBatchInput } from '@/registry/default/ui/pro-batch-input'\n\nexport default function Demo() {\n  // 受控模式状态\n  const [controlledValue, setControlledValue] =\n    React.useState('初始值,示例数据,测试内容')\n  const [controlledResult, setControlledResult] = React.useState<string[]>([])\n\n  // 非受控模式结果\n  const [uncontrolledResult, setUncontrolledResult] = React.useState<string[]>(\n    []\n  )\n\n  // 受控模式处理函数\n  const handleControlledChange = React.useCallback(\n    (e: React.ChangeEvent<HTMLInputElement>) => {\n      setControlledValue(e.target.value)\n    },\n    []\n  )\n\n  const handleControlledBatchConfirm = React.useCallback(\n    (items: string[], rawValue: string) => {\n      console.log('受控模式批量输入:', items, rawValue)\n      setControlledResult(items)\n      // 可以选择将批量输入的内容设置到主输入框\n      if (items.length > 0) {\n        setControlledValue(items.join(', '))\n      }\n    },\n    []\n  )\n\n  // 非受控模式处理函数\n  const handleUncontrolledBatchConfirm = React.useCallback(\n    (items: string[], rawValue: string) => {\n      console.log('非受控模式批量输入:', items, rawValue)\n      setUncontrolledResult(items)\n    },\n    []\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 受控模式示例 */}\n      <div className=\"space-y-4\">\n        <div>\n          <div className=\"mb-2 text-sm font-medium text-gray-700\">受控模式</div>\n          <div className=\"mb-2 text-xs text-gray-500\">\n            组件值由父组件的 state 管理，通过 value 和 onChange 控制\n          </div>\n          <ProBatchInput\n            variant=\"outlined\"\n            placeholder=\"受控模式 - 当前值由父组件管理\"\n            value={controlledValue}\n            onChange={handleControlledChange}\n            onConfirm={handleControlledBatchConfirm}\n          />\n          <div className=\"mt-2 text-xs text-gray-400\">\n            当前值: &quot;{controlledValue}&quot; - 点击批量输入按钮查看回显效果\n          </div>\n        </div>\n\n        {controlledResult.length > 0 && (\n          <div>\n            <div className=\"mb-2 text-sm text-gray-500\">\n              受控模式分割结果 ({controlledResult.length} 项):\n            </div>\n            <div className=\"rounded-md border border-gray-200 p-3\">\n              <pre className=\"overflow-auto text-sm text-gray-700\">\n                {JSON.stringify(controlledResult, null, 2)}\n              </pre>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 非受控模式示例 */}\n      <div className=\"space-y-4\">\n        <div>\n          <div className=\"mb-2 text-sm font-medium text-gray-700\">\n            非受控模式\n          </div>\n          <div className=\"mb-2 text-xs text-gray-500\">\n            使用 defaultValue 设置初始值，组件内部管理状态\n          </div>\n          <ProBatchInput\n            variant=\"outlined\"\n            placeholder=\"非受控模式 - 内部管理状态\"\n            defaultValue=\"默认值,可以回显到弹窗\"\n            onConfirm={handleUncontrolledBatchConfirm}\n          />\n        </div>\n\n        {uncontrolledResult.length > 0 && (\n          <div>\n            <div className=\"mb-2 text-sm text-gray-500\">\n              非受控模式分割结果 ({uncontrolledResult.length} 项):\n            </div>\n            <div className=\"rounded-md border border-gray-200 p-3\">\n              <pre className=\"overflow-auto text-sm text-gray-700\">\n                {JSON.stringify(uncontrolledResult, null, 2)}\n              </pre>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 说明文档 */}\n      <div className=\"rounded border bg-gray-50 p-4\">\n        <div className=\"mb-2 text-sm font-medium text-gray-700\">使用说明</div>\n        <ul className=\"space-y-1 text-xs text-gray-600\">\n          <li>\n            <strong>受控模式：</strong>使用 <code>value</code> 和{' '}\n            <code>onChange</code> 属性，\n            父组件管理所有状态，包括主输入框和批量输入弹窗的内容\n          </li>\n          <li>\n            <strong>非受控模式：</strong>使用 <code>defaultValue</code>{' '}\n            属性设置初值， 组件内部管理状态，批量输入确认后会自动清空弹窗内容\n          </li>\n          <li>\n            <strong>值回显：</strong>点击批量输入按钮时，TextField\n            的当前值会自动回显到弹窗中， 方便用户在现有内容基础上进行编辑\n          </li>\n          <li>\n            <strong>确认更新：</strong>在批量输入过程中不会实时更新 TextField，\n            只有点击确认时才会将分割后的第一个项目设置到主输入框\n          </li>\n        </ul>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-batch-input/custom-split-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { ProBatchInput } from '@/registry/default/ui/pro-batch-input'\n\nexport default function Demo() {\n  const [value, setValue] = React.useState('')\n  const [result, setResult] = React.useState<string[]>([])\n\n  // 自定义分割函数：只支持换行分割，不支持逗号和分号\n  const customSplitBatchValue = React.useCallback((input: string): string[] => {\n    return input\n      .split(/\\n/)\n      .map((line) => line.trim())\n      .filter((line) => line.length > 0)\n  }, [])\n\n  const handleBatchConfirm = React.useCallback(\n    (items: string[], rawValue: string) => {\n      console.log('自定义分割结果:', items, rawValue)\n      setResult(items)\n      if (items.length > 0) {\n        setValue(items[0])\n      }\n    },\n    []\n  )\n\n  const handleChange = React.useCallback(\n    (e: React.ChangeEvent<HTMLInputElement>) => {\n      setValue(e.target.value)\n    },\n    []\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <div className=\"mb-2 text-sm text-gray-500\">\n          自定义分割函数（仅支持换行分割）\n        </div>\n        <ProBatchInput\n          variant=\"outlined\"\n          placeholder=\"使用自定义分割规则\"\n          value={value}\n          onChange={handleChange}\n          onConfirm={handleBatchConfirm}\n          customSplitBatchValue={customSplitBatchValue}\n          poptextareaProps={{\n            placeholder: '请输入内容，每行一个，不支持逗号、分号分割',\n          }}\n        />\n        <div className=\"mt-1 text-xs text-gray-400\">\n          此示例使用自定义分割函数，仅支持换行分割，不支持逗号、分号等其他分隔符\n        </div>\n      </div>\n\n      {result.length > 0 && (\n        <div>\n          <div className=\"mb-2 text-sm text-gray-500\">\n            分割结果 ({result.length} 项):\n          </div>\n          <div className=\"rounded-md border border-gray-200 p-3\">\n            <pre className=\"overflow-auto text-sm text-gray-700\">\n              {JSON.stringify(result, null, 2)}\n            </pre>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-batch-input/default-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  ProBatchInput,\n  splitBatchValue,\n} from '@/registry/default/ui/pro-batch-input'\n\nexport default function Demo() {\n  const [value, setValue] = React.useState('')\n  const [result, setResult] = React.useState<string[]>([])\n\n  const handleBatchConfirm = React.useCallback(\n    (items: string[], rawValue: string) => {\n      console.log('批量输入确认:', items, rawValue)\n      setResult(items)\n      // 可以选择将第一个项目设置为主输入框的值\n      if (items.length > 0) {\n        setValue(items[0])\n      }\n    },\n    []\n  )\n\n  const handleChange = React.useCallback(\n    (e: React.ChangeEvent<HTMLInputElement>) => {\n      console.log('批量输入值变化:', e.target.value)\n      setValue(e.target.value)\n      setResult(splitBatchValue(e.target.value))\n    },\n    []\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <div className=\"mb-2 text-sm text-gray-500\">基础用法</div>\n        <ProBatchInput\n          className=\"w-[300px]\"\n          variant=\"outlined\"\n          placeholder=\"请输入内容或点击右侧按钮批量输入\"\n          onChange={handleChange}\n          onConfirm={handleBatchConfirm}\n          poptextareaProps={{\n            placeholder: '请输入',\n          }}\n        />\n      </div>\n\n      {result.length > 0 && (\n        <div>\n          <div className=\"mb-2 text-sm text-gray-500\">\n            分割结果 ({result.length} 项):\n          </div>\n          <div className=\"rounded-md border border-gray-200 p-3\">\n            <pre className=\"overflow-auto text-sm text-gray-700\">\n              {JSON.stringify(result, null, 2)}\n            </pre>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-batch-input/icon.tsx", "content": "const ExpandIcon = () => {\n  return (\n    <svg\n      fill=\"currentColor\"\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path d=\"M10.2584 2.91667H8.16667V1.75H12.25V5.83333H11.0833V3.74162L8.57914 6.24581L7.75419 5.42085L10.2584 2.91667ZM1.75 8.16667H2.91667V10.2584L5.42085 7.75419L6.24581 8.57914L3.74162 11.0833H5.83333V12.25H1.75V8.16667Z\" />\n    </svg>\n  )\n}\n\nexport { ExpandIcon }\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "e985dbe3995ecab41158e5abc6b261a04a7d678d"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}