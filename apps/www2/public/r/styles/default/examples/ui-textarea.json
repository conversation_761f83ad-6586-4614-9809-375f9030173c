{"name": "ui-textarea", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-textarea/autoResize-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Textarea } from '@/registry/default/ui/ui-textarea'\n\nexport default function TextareaDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea\n          placeholder=\"请输入\"\n          rows={2}\n          autoResize\n          onChange={(e) => {}}\n        />\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea\n          placeholder=\"请输入\"\n          rows={5}\n          autoResize\n          defaultValue={'你好'}\n          onChange={(e) => {}}\n        />\n      </div>\n      <div className=\"w-[300px]\"></div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/control-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Button } from '@/components/ui/button'\nimport {\n  Textarea,\n  TextareaCharCounter,\n} from '@/registry/default/ui/ui-textarea'\n\nexport default function TextareaControlDemo() {\n  const [value, setValue] = useState<string>('你好，世界！')\n\n  const handleValueChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    setValue(e.target.value)\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 基本受控示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">基本受控示例</h3>\n        <div className=\"flex items-center gap-6\">\n          <Textarea\n            value={value}\n            onChange={handleValueChange}\n            className=\"w-[240px]\"\n            rows={3}\n          />\n\n          <div className=\"space-x-3\">\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => setValue('你好，世界！')}\n            >\n              重置\n            </Button>\n            <Button size=\"sm\" variant=\"default\" onClick={() => setValue('')}>\n              清空\n            </Button>\n          </div>\n        </div>\n        <div className=\"text-sm text-gray-500\">当前值: {value}</div>\n      </div>\n\n      {/* 带字符计数的受控示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">带字符计数的受控示例</h3>\n        <div className=\"flex items-center gap-6\">\n          <Textarea\n            value={value}\n            onChange={handleValueChange}\n            className=\"w-[240px]\"\n            rows={3}\n          >\n            <TextareaCharCounter current={value.length} maxCount={50} />\n          </Textarea>\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          输入字符会自动更新计数器，最大字符数为 50\n        </div>\n      </div>\n\n      {/* 使用默认值示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">使用默认值示例</h3>\n        <div className=\"flex items-center gap-6\">\n          <Textarea\n            defaultValue=\"这是一个默认值示例\"\n            className=\"w-[240px]\"\n            rows={3}\n            onChange={(e) => console.log('文本变化:', e.target.value)}\n          />\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          使用 defaultValue 时，组件内部管理自己的状态，无需外部 state\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/default-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Textarea } from '@/registry/default/ui/ui-textarea'\n\nexport default function TextareaDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea\n          placeholder=\"请输入\"\n          rows={2}\n          onChange={(e) => {\n            console.log(e.target.value)\n          }}\n        />\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea\n          placeholder=\"请输入\"\n          rows={5}\n          defaultValue={'你好'}\n          onChange={(e) => {\n            console.log(e.target.value)\n          }}\n        />\n      </div>\n      <div className=\"w-[300px]\"></div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/disabled-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Textarea } from '@/registry/default/ui/ui-textarea'\n\nexport default function TextAreaDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea disabled placeholder=\"请输入\" />\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea disabled value={'你好'} />\n      </div>\n      <div className=\"w-[300px]\"></div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/form-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { AlertCircle, FileText, MessageSquare, Star } from 'lucide-react'\nimport { useForm } from 'react-hook-form'\nimport * as z from 'zod'\n\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/registry/default/ui/ui-form'\nimport {\n  Textarea,\n  TextareaCharCounter,\n} from '@/registry/default/ui/ui-textarea'\n\nconst formSchema = z.object({\n  description: z\n    .string()\n    .min(1, { message: '请输入产品描述' })\n    .min(10, { message: '产品描述至少需要10个字符' })\n    .max(300, { message: '产品描述不能超过300个字符' }),\n  comments: z\n    .string()\n    .max(500, { message: '评论不能超过500个字符' })\n    .optional(),\n  feedback: z\n    .string()\n    .min(1, { message: '请输入反馈内容' })\n    .min(20, { message: '反馈内容至少需要20个字符' })\n    .max(1000, { message: '反馈内容不能超过1000个字符' }),\n  issues: z\n    .string()\n    .max(800, { message: '问题描述不能超过800个字符' })\n    .optional(),\n})\n\nexport default function TextareaFormDemo() {\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      description: '',\n      comments: '',\n      feedback: '',\n      issues: '',\n    },\n  })\n\n  const watchedDescription = form.watch('description')\n  const watchedComments = form.watch('comments')\n  const watchedFeedback = form.watch('feedback')\n  const watchedIssues = form.watch('issues')\n\n  const onSubmit = (values: z.infer<typeof formSchema>) => {\n    console.log('表单提交数据:', values)\n    alert(`表单提交成功！\\n${JSON.stringify(values, null, 2)}`)\n  }\n\n  return (\n    <div className=\"mx-auto max-w-2xl space-y-6\">\n      <div className=\"space-y-2 text-center\">\n        <h2 className=\"text-lg font-semibold\">产品反馈表单</h2>\n        <p className=\"text-sm text-gray-600\">\n          展示 Textarea 与 Form 结合的完整多行文本输入场景\n        </p>\n      </div>\n\n      <Form {...form} onSubmit={onSubmit} className=\"space-y-6\">\n        {/* 产品描述 */}\n        <FormField control={form.control} name=\"description\">\n          <FormItem>\n            <FormLabel className=\"flex items-center gap-2\" required>\n              <FileText className=\"h-4 w-4\" />\n              产品描述\n            </FormLabel>\n            <FormControl>\n              <Textarea\n                placeholder=\"请详细描述产品的特性、功能和优势...\"\n                rows={4}\n                variant=\"outlined\"\n                size=\"large\"\n              >\n                <TextareaCharCounter\n                  maxCount={300}\n                  current={watchedDescription?.length || 0}\n                />\n              </Textarea>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        {/* 用户反馈 */}\n        <FormField control={form.control} name=\"feedback\">\n          <FormItem>\n            <FormLabel className=\"flex items-center gap-2\" required>\n              <Star className=\"h-4 w-4\" />\n              用户反馈\n            </FormLabel>\n            <FormControl>\n              <Textarea\n                placeholder=\"请分享您的使用体验和建议...\"\n                rows={5}\n                variant=\"filled\"\n                autoResize\n              >\n                <TextareaCharCounter\n                  maxCount={1000}\n                  current={watchedFeedback?.length || 0}\n                />\n              </Textarea>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        {/* 问题描述 */}\n        <FormField control={form.control} name=\"issues\">\n          <FormItem>\n            <FormLabel className=\"flex items-center gap-2\">\n              <AlertCircle className=\"h-4 w-4\" />\n              问题描述\n            </FormLabel>\n            <FormControl>\n              <Textarea\n                placeholder=\"如果遇到问题，请详细描述（可选）...\"\n                rows={4}\n                variant=\"outlined\"\n                hasError={!!form.formState.errors.issues}\n              >\n                <TextareaCharCounter\n                  maxCount={800}\n                  current={watchedIssues?.length || 0}\n                />\n              </Textarea>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        {/* 额外备注 */}\n        <FormField control={form.control} name=\"comments\">\n          <FormItem>\n            <FormLabel className=\"flex items-center gap-2\">\n              <MessageSquare className=\"h-4 w-4\" />\n              额外备注\n            </FormLabel>\n            <FormControl>\n              <Textarea\n                placeholder=\"请输入其他补充信息（可选）...\"\n                rows={3}\n                variant=\"ghost\"\n                size=\"small\"\n              >\n                <TextareaCharCounter\n                  maxCount={500}\n                  current={watchedComments?.length || 0}\n                />\n              </Textarea>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        <div className=\"flex gap-3 pt-4\">\n          <Button type=\"submit\" className=\"flex-1\">\n            提交反馈\n          </Button>\n          <Button type=\"button\" variant=\"outlined\" onClick={() => form.reset()}>\n            重置表单\n          </Button>\n        </div>\n      </Form>\n\n      {/* 表单状态展示 */}\n      <div className=\"mt-6 rounded-lg bg-gray-50 p-4\">\n        <h3 className=\"mb-2 text-sm font-medium\">表单状态</h3>\n        <div className=\"space-y-1 text-xs\">\n          <div>是否有效: {form.formState.isValid ? '✅' : '❌'}</div>\n          <div>是否已提交: {form.formState.isSubmitted ? '✅' : '❌'}</div>\n          <div>错误数量: {Object.keys(form.formState.errors).length}</div>\n          <div>\n            已修改字段: {Object.keys(form.formState.dirtyFields).length}\n          </div>\n          <div>\n            字符统计: 描述({watchedDescription?.length || 0}/300) | 反馈(\n            {watchedFeedback?.length || 0}/1000)\n          </div>\n        </div>\n\n        {/* 当前表单值展示 */}\n        <div className=\"mt-3 border-t border-gray-200 pt-3\">\n          <h4 className=\"mb-2 text-xs font-medium\">当前值:</h4>\n          <pre className=\"overflow-auto text-xs text-gray-600\">\n            {JSON.stringify(form.watch(), null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/form-validation-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { useForm } from 'react-hook-form'\nimport * as z from 'zod'\n\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Form,\n  FormControl,\n  FormDescription,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/registry/default/ui/ui-form'\nimport { Toaster, toast } from '@/registry/default/ui/ui-message'\nimport {\n  Textarea,\n  TextareaCharCounter,\n} from '@/registry/default/ui/ui-textarea'\n\n// 定义表单验证 Schema\nconst formSchema = z.object({\n  feedback: z\n    .string()\n    .min(10, {\n      message: '反馈内容至少需要 10 个字符',\n    })\n    .max(200, {\n      message: '反馈内容不能超过 200 个字符',\n    }),\n})\n\nexport default function TextareaFormDemo() {\n  // 初始化表单\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      feedback: '',\n    },\n  })\n\n  // 表单提交处理\n  function onSubmit(values: z.infer<typeof formSchema>) {\n    // 在实际应用中处理表单提交\n    console.log(values)\n    toast.success(`提交的反馈: ${values.feedback}`)\n  }\n\n  return (\n    <div className=\"w-full\">\n      <Toaster />\n      <Form {...form} onSubmit={onSubmit} className=\"space-y-6\">\n        <FormField\n          control={form.control}\n          name=\"feedback\"\n          render={({ field }) => (\n            <FormItem>\n              <FormLabel>您的反馈</FormLabel>\n              <FormControl>\n                <Textarea\n                  placeholder=\"请输入您的反馈...\"\n                  hasError={!!form.formState.errors.feedback}\n                  autoResize\n                  rows={3}\n                  {...field}\n                >\n                  <TextareaCharCounter\n                    current={field.value.length}\n                    maxCount={200}\n                  />\n                </Textarea>\n              </FormControl>\n              <FormDescription>请分享您对我们产品的想法和建议</FormDescription>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n        <div className=\"flex justify-end\">\n          <Button type=\"submit\">提交反馈</Button>\n        </div>\n      </Form>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/hasError-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Textarea } from '@/registry/default/ui/ui-textarea'\n\nexport default function TextAreaDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea\n          hasError\n          rows={2}\n          defaultValue={'你好'}\n          onChange={(e) => {\n            console.log(e.target.value)\n          }}\n        />\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea disabled hasError defaultValue=\"你好啊\" />\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea hasError rows={5} defaultValue=\"你好啊\" />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/maxCount-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  Textarea,\n  TextareaCharCounter,\n} from '@/registry/default/ui/ui-textarea'\n\nexport default function TextAreaDemo() {\n  const [inputValue, setInputValue] = React.useState('你好')\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea\n          value={inputValue}\n          onChange={(e) => setInputValue(e.target.value)}\n        >\n          <TextareaCharCounter current={inputValue.length} maxCount={10} />\n        </Textarea>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <Textarea disabled value={'你好'}>\n          <TextareaCharCounter current={2} maxCount={1000} />\n        </Textarea>\n      </div>\n      <div className=\"w-[300px]\"></div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/size-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\nimport { Textarea } from '@/registry/default/ui/ui-textarea'\n\nexport default function TextAreaSizeDemo() {\n  const [size, setSize] = useState<'small' | 'large'>('small')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 尺寸选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">尺寸</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={size}\n            onChange={(v) => setSize(v as 'small' | 'large')}\n          >\n            <RadioCard value=\"small\">小</RadioCard>\n            <RadioCard value=\"large\">大</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同尺寸的文本域</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            size={size}\n            placeholder=\"请输入文本内容...\"\n            defaultValue=\"这是一个文本域示例，您可以看到不同尺寸的文本域外观。\"\n            rows={4}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            当前尺寸: {size === 'small' ? '小' : '大'}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textarea/variant-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\nimport { Textarea } from '@/registry/default/ui/ui-textarea'\n\nexport default function TextareaDemo() {\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 变体选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">变体</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={variant}\n            onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n          >\n            <RadioCard value=\"outlined\">outlined</RadioCard>\n            <RadioCard value=\"filled\">filled</RadioCard>\n            <RadioCard value=\"ghost\">ghost</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同变体的文本域</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea variant={variant} placeholder=\"请输入短文本\" rows={2} />\n          <Textarea\n            variant={variant}\n            placeholder=\"请输入长文本\"\n            rows={5}\n            defaultValue=\"这是一段示例文本，展示不同变体下的文本域外观。\"\n          />\n          <div className=\"text-sm text-gray-500\">当前变体: {variant}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "c6cc5f18a42991e4b8ee3044297ef6697c479e53"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-poptextarea", "pro-textarea"]}