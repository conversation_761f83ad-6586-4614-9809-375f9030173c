{"name": "pro-textarea", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-textarea/autoResize-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ProTextareaAutoResizeDemo() {\n  const [autoResize, setAutoResize] = useState(true)\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">自动调整高度</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={autoResize ? 'true' : 'false'}\n            onChange={(v) => setAutoResize(v === 'true')}\n          >\n            <RadioCard value=\"true\">开启</RadioCard>\n            <RadioCard value=\"false\">关闭</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 示例区域 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">自动调整高度示例</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            placeholder=\"请输入内容，当内容增加时文本域会自动增高\"\n            rows={2}\n            autoResize={autoResize}\n            defaultValue={\n              autoResize\n                ? '这是一段示例文本。\\n当内容增加时，文本域会根据内容自动调整高度。\\n可以继续输入更多内容来查看效果。'\n                : ''\n            }\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            {autoResize\n              ? 'autoResize=true: 文本域高度会随着内容增加而自动调整'\n              : 'autoResize=false: 文本域保持固定高度，内容过多时出现滚动条'}\n          </div>\n        </div>\n      </div>\n\n      {/* 初始高度较大的示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">设置初始行数</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            placeholder=\"初始高度设置为4行，输入更多内容会继续增高\"\n            rows={4}\n            autoResize={autoResize}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            通过rows属性设置初始行数，配合autoResize可以控制最小高度\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/control-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\nimport { Button } from '@/registry/default/ui/ui-button'\n\nexport default function ProTextareaControlDemo() {\n  const [value, setValue] = useState('这是受控文本')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 受控模式 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">受控模式</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            placeholder=\"请输入内容\"\n            rows={3}\n            value={value}\n            onChange={(e) => setValue(e.target.value)}\n          />\n\n          <div className=\"flex items-center gap-3\">\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={() => setValue('重置后的文本')}\n            >\n              重置文本\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={() => setValue('')}\n            >\n              清空\n            </Button>\n          </div>\n\n          <div className=\"text-sm text-gray-500\">\n            当前值: {value ? `\"${value}\"` : '空'}\n          </div>\n        </div>\n      </div>\n\n      {/* 非受控模式 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">非受控模式</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            placeholder=\"请输入内容\"\n            rows={3}\n            defaultValue=\"这是默认文本，使用defaultValue属性设置\"\n            onChange={(e) => console.log(e.target.value)}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            使用 defaultValue 时，组件内部管理状态，无需外部 state\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/default-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\n\nexport default function ProTextareaDefaultDemo() {\n  const [value, setValue] = useState('这是一段示例文本')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 基本样式 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">基本样式</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea placeholder=\"默认文本域\" rows={3} />\n\n          <Textarea placeholder=\"禁用状态\" rows={3} disabled />\n\n          <Textarea placeholder=\"错误状态\" rows={3} hasError />\n        </div>\n      </div>\n\n      {/* 交互示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">交互示例</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            placeholder=\"请输入内容\"\n            rows={3}\n            value={value}\n            onChange={(e) => setValue(e.target.value)}\n          />\n\n          <div className=\"text-sm text-gray-500\">当前输入: {value}</div>\n        </div>\n      </div>\n\n      {/* 带字数统计 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">带字数统计</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            placeholder=\"请输入内容\"\n            rows={3}\n            showCount\n            maxCount={100}\n            defaultValue=\"文本域支持显示字数统计，可以设置最大字数限制。\"\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            设置 showCount 属性可显示字数统计，maxCount 属性可设置最大字数\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/disabled-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ProTextareaDisabledDemo() {\n  const [disabled, setDisabled] = useState(true)\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-4\">\n        {/* 禁用状态选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">禁用状态</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={disabled ? 'true' : 'false'}\n              onChange={(v) => setDisabled(v === 'true')}\n            >\n              <RadioCard value=\"false\">启用</RadioCard>\n              <RadioCard value=\"true\">禁用</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n\n        {/* 变体选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">变体</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={variant}\n              onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n            >\n              <RadioCard value=\"outlined\">outlined</RadioCard>\n              <RadioCard value=\"filled\">filled</RadioCard>\n              <RadioCard value=\"ghost\">ghost</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">禁用状态的文本域</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            variant={variant}\n            disabled={disabled}\n            placeholder=\"请输入内容\"\n            rows={3}\n            defaultValue={disabled ? '禁用状态下无法编辑此文本' : ''}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            禁用状态下，文本域呈现灰色外观，用户无法与之交互\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/form-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { AlertCircle, FileText, MessageSquare, Star } from 'lucide-react'\nimport { useForm } from 'react-hook-form'\nimport * as z from 'zod'\n\nimport { Button } from '@/registry/default/ui/pro-button'\nimport { Form } from '@/registry/default/ui/pro-form'\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\n\nconst formSchema = z.object({\n  description: z\n    .string()\n    .min(1, { message: '请输入产品描述' })\n    .min(10, { message: '产品描述至少需要10个字符' })\n    .max(300, { message: '产品描述不能超过300个字符' }),\n  feedback: z\n    .string()\n    .min(1, { message: '请输入用户反馈' })\n    .min(20, { message: '用户反馈至少需要20个字符' })\n    .max(1000, { message: '用户反馈不能超过1000个字符' }),\n  issues: z\n    .string()\n    .max(800, { message: '问题描述不能超过800个字符' })\n    .optional(),\n  comments: z\n    .string()\n    .max(500, { message: '额外备注不能超过500个字符' })\n    .optional(),\n})\n\nexport default function ProTextareaFormDemo() {\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      description: '',\n      feedback: '',\n      issues: '',\n      comments: '',\n    },\n  })\n\n  const onSubmit = (values: z.infer<typeof formSchema>) => {\n    console.log('表单提交数据:', values)\n    alert(`表单提交成功！\\n${JSON.stringify(values, null, 2)}`)\n  }\n\n  return (\n    <div className=\"mx-auto max-w-2xl space-y-6\">\n      <div className=\"space-y-2 text-center\">\n        <h2 className=\"text-lg font-semibold\">产品反馈表单</h2>\n        <p className=\"text-sm text-gray-600\">\n          展示 ProTextarea 与 ProForm 结合的高级多行文本输入场景\n        </p>\n      </div>\n\n      <Form form={form} onSubmit={onSubmit} className=\"space-y-4\">\n        {/* 产品描述 */}\n        <Form.Item\n          name=\"description\"\n          label=\"产品描述\"\n          required\n          tooltip=\"请详细描述产品的特性和功能\"\n        >\n          <Textarea\n            placeholder=\"请详细描述产品的特性、功能和优势...\"\n            rows={4}\n            variant=\"outlined\"\n            showCount\n            maxCount={300}\n            size=\"large\"\n          />\n        </Form.Item>\n\n        {/* 用户反馈 */}\n        <Form.Item\n          name=\"feedback\"\n          label=\"用户反馈\"\n          required\n          tooltip=\"请分享您的使用体验和建议\"\n        >\n          <Textarea\n            placeholder=\"请分享您的使用体验和建议...\"\n            rows={5}\n            variant=\"filled\"\n            showCount\n            maxCount={1000}\n            autoResize\n          />\n        </Form.Item>\n\n        {/* 问题描述 */}\n        <Form.Item name=\"issues\" label=\"问题描述\">\n          <Textarea\n            placeholder=\"如果遇到问题，请详细描述（可选）...\"\n            rows={4}\n            variant=\"outlined\"\n            showCount\n            maxCount={800}\n          />\n        </Form.Item>\n\n        {/* 额外备注 */}\n        <Form.Item\n          name=\"comments\"\n          label=\"额外备注\"\n          tooltip=\"可选填写其他补充信息\"\n        >\n          <Textarea\n            placeholder=\"请输入其他补充信息（可选）...\"\n            rows={3}\n            variant=\"ghost\"\n            showCount\n            maxCount={500}\n            size=\"small\"\n          />\n        </Form.Item>\n\n        <div className=\"flex gap-3 pt-4\">\n          <Button type=\"submit\" color=\"primary\" className=\"flex-1\">\n            提交反馈\n          </Button>\n          <Button type=\"button\" variant=\"outlined\" onClick={() => form.reset()}>\n            重置表单\n          </Button>\n        </div>\n      </Form>\n\n      {/* 表单状态展示 */}\n      <div className=\"mt-6 rounded-lg bg-gray-50 p-4\">\n        <h3 className=\"mb-2 text-sm font-medium\">表单状态</h3>\n        <div className=\"space-y-1 text-xs\">\n          <div>是否有效: {form.formState.isValid ? '✅' : '❌'}</div>\n          <div>是否已提交: {form.formState.isSubmitted ? '✅' : '❌'}</div>\n          <div>错误数量: {Object.keys(form.formState.errors).length}</div>\n          <div>\n            已修改字段: {Object.keys(form.formState.dirtyFields).length}\n          </div>\n        </div>\n\n        {/* 当前表单值展示 */}\n        <div className=\"mt-3 border-t border-gray-200 pt-3\">\n          <h4 className=\"mb-2 text-xs font-medium\">当前值:</h4>\n          <pre className=\"overflow-auto text-xs text-gray-600\">\n            {JSON.stringify(form.watch(), null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/hasError-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ProTextareaHasErrorDemo() {\n  const [hasError, setHasError] = useState(true)\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-4\">\n        {/* 错误状态选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">错误状态</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={hasError ? 'true' : 'false'}\n              onChange={(v) => setHasError(v === 'true')}\n            >\n              <RadioCard value=\"false\">正常</RadioCard>\n              <RadioCard value=\"true\">错误</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n\n        {/* 变体选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">变体</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={variant}\n              onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n            >\n              <RadioCard value=\"outlined\">outlined</RadioCard>\n              <RadioCard value=\"filled\">filled</RadioCard>\n              <RadioCard value=\"ghost\">ghost</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">错误状态的文本域</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            variant={variant}\n            hasError={hasError}\n            rows={4}\n            placeholder=\"请输入内容\"\n            defaultValue={hasError ? '这是一段包含错误的内容示例' : ''}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            错误状态下，文本域呈现红色外观，用于表单验证失败时提供视觉反馈\n          </div>\n        </div>\n      </div>\n\n      {/* 错误状态下的禁用组件 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">禁用且错误状态的文本域</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            variant={variant}\n            hasError={hasError}\n            disabled\n            rows={4}\n            placeholder=\"请输入内容\"\n            defaultValue=\"禁用且有错误的文本域示例\"\n          />\n\n          <div className=\"text-sm text-gray-500\">禁用状态下的错误样式展示</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/maxCount-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ProTextareaMaxCountDemo() {\n  const [inputValue, setInputValue] = useState('这是一段示例文本，你可以编辑')\n  const [maxCount, setMaxCount] = useState<number>(100)\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">最大字数限制</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={maxCount.toString()}\n            onChange={(v) => setMaxCount(Number(v))}\n          >\n            <RadioCard value=\"20\">20字</RadioCard>\n            <RadioCard value=\"50\">50字</RadioCard>\n            <RadioCard value=\"100\">100字</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 受控字数限制示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">受控字数限制示例</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            showCount\n            maxCount={maxCount}\n            rows={4}\n            placeholder=\"请输入内容\"\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            当前字数: {inputValue.length}/{maxCount}，剩余可输入:{' '}\n            {maxCount - inputValue.length}字\n          </div>\n        </div>\n      </div>\n\n      {/* 不同显示方式 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同的字数统计展示</h3>\n        <div className=\"flex flex-col gap-4\">\n          <div className=\"space-y-1\">\n            <div className=\"text-xs text-gray-500\">标准字数统计</div>\n            <Textarea\n              defaultValue=\"默认显示 已输入/最大可输入 格式的字数统计\"\n              showCount\n              maxCount={200}\n              rows={3}\n            />\n          </div>\n\n          <div className=\"space-y-1\">\n            <div className=\"text-xs text-gray-500\">无最大限制的字数统计</div>\n            <Textarea\n              defaultValue=\"只显示已输入字数，不限制最大输入\"\n              showCount\n              rows={3}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/size-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ProTextareaSizeDemo() {\n  const [size, setSize] = useState<'small' | 'large'>('small')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 尺寸选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">尺寸</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={size}\n            onChange={(v) => setSize(v as 'small' | 'large')}\n          >\n            <RadioCard value=\"small\">小</RadioCard>\n            <RadioCard value=\"large\">大</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同尺寸的文本域</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea\n            size={size}\n            placeholder=\"请输入内容\"\n            defaultValue=\"这是一个文本域示例，您可以看到不同尺寸的文本域外观。\"\n            rows={4}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            当前尺寸: {size === 'small' ? '小' : '大'}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textarea/variant-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Textarea } from '@/registry/default/ui/pro-textarea'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function VariantDemo() {\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 变体选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">变体</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={variant}\n            onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n          >\n            <RadioCard value=\"outlined\">outlined</RadioCard>\n            <RadioCard value=\"filled\">filled</RadioCard>\n            <RadioCard value=\"ghost\">ghost</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同变体的文本域</h3>\n        <div className=\"flex flex-col gap-4\">\n          <Textarea variant={variant} placeholder=\"普通文本域\" rows={3} />\n          <Textarea\n            variant={variant}\n            placeholder=\"带字数统计的文本域\"\n            showCount\n            maxCount={100}\n            rows={3}\n            defaultValue=\"这是一段示例文本，展示不同变体下的文本域外观。\"\n          />\n          <div className=\"text-sm text-gray-500\">当前变体: {variant}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "c6cc5f18a42991e4b8ee3044297ef6697c479e53"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}