{"name": "pro-button", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-button/color-demo.tsx", "content": "'use client'\n\nimport { But<PERSON> } from '@/registry/default/ui/pro-button'\n\nexport default function ColorButtonDemo() {\n  return (\n    <div className=\"flex flex-col gap-4\">\n      <div className=\"flex gap-4\">\n        <Button color=\"primary\">primary</Button>\n        <Button color=\"secondary\">secondary</Button>\n        <Button color=\"default\">default</Button>\n        <Button color=\"error\">error</Button>\n        <Button color=\"light-error\">light-error</Button>\n      </div>\n      <div className=\"flex gap-4\">\n        <Button color=\"linear-gradient\">linear-gradient</Button>\n        <Button color=\"search\">search</Button>\n        <Button color=\"reset\">reset</Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/default-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button, ButtonProps } from '@/registry/default/ui/pro-button'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <div className=\"flex flex-wrap gap-4\">\n        <Button size={size}>default button</Button>\n        <Button size={size} color=\"secondary\">\n          secondary button\n        </Button>\n        <Button size={size} variant=\"outlined\">\n          outline button\n        </Button>\n      </div>\n      <div className=\"flex flex-wrap gap-4\">\n        <Button size={size} variant=\"text\">\n          ghost primary button\n        </Button>\n        <Button size={size} color=\"default\" variant=\"text\">\n          ghost button\n        </Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/disabled-demo.tsx", "content": "'use client'\n\nimport { Search } from 'lucide-react'\n\nimport { Button } from '@/registry/default/ui/pro-button'\n\nexport default function DisabledButtonDemo() {\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">禁用状态</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button disabled>禁用按钮</Button>\n          <Button color=\"secondary\" disabled>\n            禁用按钮\n          </Button>\n          <Button variant=\"outlined\" disabled>\n            禁用按钮\n          </Button>\n          <Button variant=\"text\" disabled>\n            禁用按钮\n          </Button>\n          <Button color=\"default\" variant=\"text\" disabled>\n            禁用按钮\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">带图标的禁用状态</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button disabled leftIcon={<Search />}>\n            禁用按钮\n          </Button>\n          <Button disabled icon={<Search />} />\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/group-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { Search, Settings, Trash } from 'lucide-react'\n\nimport { Button, ButtonProps } from '@/registry/default/ui/pro-button'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function GroupButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">水平按钮组</h3>\n        <div className=\"inline-flex gap-4 rounded-md\">\n          <Button size={size} className=\"rounded-r-none\" leftIcon={<Search />}>\n            搜索\n          </Button>\n          <Button\n            size={size}\n            className=\"rounded-none border-l-0\"\n            leftIcon={<Settings />}\n          >\n            设置\n          </Button>\n          <Button\n            size={size}\n            className=\"rounded-l-none border-l-0\"\n            leftIcon={<Trash />}\n          >\n            删除\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">只有图标的按钮组</h3>\n        <div className=\"inline-flex gap-4 rounded-md\">\n          <Button size={size} className=\"rounded-r-none\" icon={<Search />} />\n          <Button\n            size={size}\n            className=\"rounded-none border-l-0\"\n            icon={<Settings />}\n          />\n          <Button\n            size={size}\n            className=\"rounded-l-none border-l-0\"\n            icon={<Trash />}\n          />\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/icon-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport {\n  <PERSON>Right,\n  Bell,\n  Calendar,\n  Check,\n  Home,\n  Search,\n  Settings,\n  X,\n} from 'lucide-react'\n\nimport { Button, ButtonProps } from '@/registry/default/ui/pro-button'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function IconButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">左侧图标</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button size={size} leftIcon={<Search />}>\n            搜索\n          </Button>\n          <Button size={size} color=\"secondary\" leftIcon={<Home />}>\n            主页\n          </Button>\n          <Button size={size} variant=\"outlined\" leftIcon={<Settings />}>\n            设置\n          </Button>\n          <Button size={size} variant=\"text\" leftIcon={<Bell />}>\n            通知\n          </Button>\n          <Button\n            size={size}\n            color=\"default\"\n            variant=\"text\"\n            leftIcon={<Calendar />}\n          >\n            日历\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">右侧图标</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button size={size} rightIcon={<ArrowRight />}>\n            下一步\n          </Button>\n          <Button size={size} color=\"secondary\" rightIcon={<Check />}>\n            确认\n          </Button>\n          <Button size={size} variant=\"outlined\" rightIcon={<X />}>\n            取消\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">仅图标按钮</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button size={size} icon={<Search />} />\n          <Button size={size} color=\"secondary\" icon={<Home />} />\n          <Button size={size} variant=\"outlined\" icon={<Settings />} />\n          <Button size={size} variant=\"text\" icon={<Bell />} />\n          <Button\n            size={size}\n            color=\"default\"\n            variant=\"text\"\n            icon={<Calendar />}\n          />\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/loading-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button, ButtonProps } from '@/registry/default/ui/pro-button'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function LoadingButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <div className=\"mb-4 flex flex-wrap gap-4 align-middle\">\n        <Button size={size} loading>\n          Button\n        </Button>\n        <Button size={size} color=\"secondary\" loading>\n          Button\n        </Button>\n        <Button size={size} variant=\"outlined\" loading>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\" loading>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\" color=\"default\" loading>\n          Button\n        </Button>\n      </div>\n      <div className=\"mb-4 flex flex-wrap gap-4 align-middle\">\n        <Button size={size} color=\"error\" loading>\n          Button\n        </Button>\n        <Button size={size} color=\"light-error\" loading>\n          Button\n        </Button>\n        <Button size={size} variant=\"outlined\" color=\"error\" loading>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\" color=\"error\" loading>\n          Button\n        </Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/onSubmit-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { Save, Search, Send } from 'lucide-react'\n\nimport { Button } from '@/registry/default/ui/pro-button'\n\nexport default function OnSubmitButtonDemo() {\n  const [results, setResults] = React.useState<string[]>([])\n\n  const handleSubmit = (message: string): Promise<boolean> => {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        setResults((prev) => [\n          ...prev,\n          `${message} 完成于 ${new Date().toLocaleTimeString()}`,\n        ])\n        resolve(true)\n      }, 1500)\n    })\n  }\n\n  const handleSubmitError = (message: string): Promise<boolean> => {\n    return new Promise((_, reject) => {\n      setTimeout(() => {\n        setResults((prev) => [\n          ...prev,\n          `${message} 完成于 ${new Date().toLocaleTimeString()}`,\n        ])\n        reject(new Error('保存失败'))\n      }, 1500)\n    })\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">提交按钮示例</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button\n            leftIcon={<Search />}\n            onSubmit={() => handleSubmit('搜索操作')}\n          >\n            搜索（默认防抖 300ms）\n          </Button>\n          <Button\n            color=\"secondary\"\n            leftIcon={<Send />}\n            onSubmit={() => handleSubmit('发送操作')}\n            debounceTime={1000}\n          >\n            发送（防抖 1000ms）\n          </Button>\n          <Button\n            variant=\"outlined\"\n            leftIcon={<Save />}\n            onSubmit={() => handleSubmit('保存操作')}\n            debounceTime={0}\n          >\n            保存（无防抖）\n          </Button>\n          <Button\n            leftIcon={<Send />}\n            onSubmit={() => handleSubmit('发送操作')}\n            debounceTime={1000}\n            handleLoading={false}\n          >\n            发送（不需要加载状态）\n          </Button>\n          <Button\n            variant=\"outlined\"\n            color=\"error\"\n            leftIcon={<Save />}\n            onSubmit={() => handleSubmitError('保存失败')}\n            onError={(e) => console.log('保存失败', e)}\n          >\n            保存失败\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-2\">\n        <h3 className=\"text-base font-medium\">操作记录</h3>\n        <div className=\"max-h-40 overflow-y-auto rounded border p-2\">\n          {results.length === 0 ? (\n            <p className=\"text-gray-500\">点击上方按钮查看效果</p>\n          ) : (\n            results.map((result, index) => (\n              <div key={index} className=\"py-1\">\n                {result}\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-button/tips-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { AlertCircle, HelpCircle, Info, Settings } from 'lucide-react'\n\nimport { Button } from '@/registry/default/ui/pro-button'\n\nexport default function TipsButtonDemo() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">带提示的按钮</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button tips=\"这是一个提示信息\">悬停查看提示</Button>\n          <Button color=\"secondary\" leftIcon={<Info />} tips=\"查看更多信息\">\n            信息按钮\n          </Button>\n          <Button\n            variant=\"outlined\"\n            rightIcon={<HelpCircle />}\n            tips=\"这里是帮助说明内容\"\n          >\n            帮助\n          </Button>\n          <Button\n            variant=\"text\"\n            leftIcon={<AlertCircle />}\n            tips=\"注意：此操作不可撤销\"\n          >\n            警告\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">图标按钮提示</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button icon={<Info />} tips=\"查看详细信息\" />\n          <Button color=\"secondary\" icon={<HelpCircle />} tips=\"获取帮助\" />\n          <Button variant=\"outlined\" icon={<Settings />} tips=\"系统设置\" />\n          <Button variant=\"text\" icon={<AlertCircle />} tips=\"重要提醒\" />\n        </div>\n      </div>\n\n      <div className=\"space-y-4\">\n        <h3 className=\"text-base font-medium\">富文本提示</h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <Button\n            tips={\n              <div className=\"space-y-2\">\n                <p className=\"font-medium\">详细说明</p>\n                <p>这是一个多行的提示内容</p>\n                <p>\n                  可以包含<span className=\"text-blue-500\">格式化文本</span>\n                </p>\n              </div>\n            }\n          >\n            富文本提示\n          </Button>\n          <Button\n            color=\"secondary\"\n            tips={\n              <div className=\"flex items-center gap-2\">\n                <AlertCircle className=\"text-yellow-500\" />\n                <span>带图标的提示</span>\n              </div>\n            }\n          >\n            带图标提示\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "394ca47bb1068dfb05f737cc42875c2c1ddf7515"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-batch-input", "pro-textField"]}