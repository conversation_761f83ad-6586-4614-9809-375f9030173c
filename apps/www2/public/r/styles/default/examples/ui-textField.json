{"name": "ui-textField", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-textField/affix-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldAffix,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nexport default function TextFieldDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldAffix>https://</TextFieldAffix>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <TextFieldAffix>.com</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldAffix>+86</TextFieldAffix>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <TextFieldAffix>RMB</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"medium\">\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <TextFieldAffix>RMB</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"small\">\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <TextFieldAffix>RMB</TextFieldAffix>\n        </TextField>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/button-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button, ButtonIcon } from '@/registry/default/ui/ui-button'\nimport {\n  TextField,\n  TextFieldIcon,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nimport { SearchIcon } from './icon'\n\nexport default function TextFieldDemo() {\n  const [size, setSize] = React.useState<'small' | 'medium' | 'large'>('medium')\n  const [variant, setVariant] = React.useState<'outlined' | 'filled' | 'ghost'>(\n    'filled'\n  )\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"mb-6 flex gap-x-2\">\n        <Button\n          variant={size === 'small' ? undefined : 'outlined'}\n          onClick={() => setSize('small')}\n          size=\"small\"\n        >\n          小\n        </Button>\n        <Button\n          variant={size === 'medium' ? undefined : 'outlined'}\n          onClick={() => setSize('medium')}\n          size=\"small\"\n        >\n          中\n        </Button>\n        <Button\n          variant={size === 'large' ? undefined : 'outlined'}\n          onClick={() => setSize('large')}\n          size=\"small\"\n        >\n          大\n        </Button>\n      </div>\n\n      <div className=\"mb-6 flex gap-x-2\">\n        <Button\n          variant={variant === 'outlined' ? undefined : 'outlined'}\n          onClick={() => setVariant('outlined')}\n          size=\"small\"\n        >\n          outlined\n        </Button>\n        <Button\n          variant={variant === 'filled' ? undefined : 'outlined'}\n          onClick={() => setVariant('filled')}\n          size=\"small\"\n        >\n          filled\n        </Button>\n        <Button\n          variant={variant === 'ghost' ? undefined : 'outlined'}\n          onClick={() => setVariant('ghost')}\n          size=\"small\"\n        >\n          ghost\n        </Button>\n      </div>\n\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size={size} variant={variant}>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <Button className=\"rounded-none !px-3 !py-2\" size={size}>\n            搜索\n          </Button>\n        </TextField>\n      </div>\n\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size={size} variant={variant}>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <Button className=\"rounded-none\" size={size}>\n            <ButtonIcon>\n              <SearchIcon />\n            </ButtonIcon>\n          </Button>\n        </TextField>\n      </div>\n\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size={size} variant={variant}>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <Button className=\"rounded-none\" size={size} color=\"secondary\">\n            <ButtonIcon>\n              <SearchIcon />\n            </ButtonIcon>\n          </Button>\n        </TextField>\n      </div>\n\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size={size} variant={variant}>\n          <TextFieldWrapper>\n            <TextFieldIcon className=\"text-[#878B9C]\">\n              <SearchIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/clear-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nexport default function TextFieldDemo() {\n  return (\n    <div className=\"mb-6 w-[300px]\">\n      <TextField size=\"large\" allowClear>\n        <TextFieldWrapper>\n          <TextFieldInput placeholder=\"请输入\" />\n        </TextFieldWrapper>\n      </TextField>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/control-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Button } from '@/components/ui/button'\nimport {\n  TextField,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nexport default function TextFieldDemo() {\n  const [value, setValue] = useState('')\n\n  const handleValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setValue(e.target.value)\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 受控组件示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">受控组件示例</h3>\n        <div className=\"flex items-center gap-6\">\n          <TextField className=\"w-[240px]\">\n            <TextFieldWrapper>\n              <TextFieldInput\n                placeholder=\"请输入文本\"\n                value={value}\n                onChange={handleValueChange}\n              />\n            </TextFieldWrapper>\n          </TextField>\n\n          <div className=\"space-x-3\">\n            <Button size=\"sm\" variant=\"outline\" onClick={() => setValue('')}>\n              清空\n            </Button>\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => setValue('Hello World')}\n            >\n              设为 Hello World\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"text-sm text-gray-500\">当前值: {value || '(空)'}</div>\n      </div>\n\n      {/* 使用默认值示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">使用默认值示例</h3>\n        <div className=\"flex items-center gap-6\">\n          <TextField>\n            <TextFieldWrapper>\n              <TextFieldInput\n                placeholder=\"请输入文本\"\n                defaultValue=\"默认文本\"\n                className=\"w-[240px]\"\n              />\n            </TextFieldWrapper>\n          </TextField>\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          使用 defaultValue 时，组件内部管理自己的状态，无需外部 state\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/default-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldAffix,\n  TextFieldIcon,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nimport { EyeIcon, UserIcon } from './icon'\n\nexport default function TextAreaDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <TextField allowClear>\n          <TextFieldAffix>http://</TextFieldAffix>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <TextFieldAffix>com</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"medium\">\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"small\">\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField>\n          <TextFieldWrapper>\n            <TextFieldInput disabled placeholder=\"请输入\" />\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/disabled-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldAffix,\n  TextFieldIcon,\n  TextFieldInlineAffix,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nimport { UserIcon } from './icon'\n\nexport default function TextFieldDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" disabled />\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput value={'你好你好'} placeholder=\"请输入\" disabled />\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" disabled />\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldAffix>https://</TextFieldAffix>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" value={'imile.com'} disabled />\n          </TextFieldWrapper>\n          <TextFieldAffix>.com</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldAffix>https://</TextFieldAffix>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" disabled />\n          </TextFieldWrapper>\n          <TextFieldAffix>.com</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldInlineAffix>$</TextFieldInlineAffix>\n            <TextFieldInput placeholder=\"请输入\" value={1000} disabled />\n          </TextFieldWrapper>\n          <TextFieldAffix>.com</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldInlineAffix>$</TextFieldInlineAffix>\n            <TextFieldInput placeholder=\"请输入\" disabled />\n          </TextFieldWrapper>\n          <TextFieldAffix>.com</TextFieldAffix>\n        </TextField>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/form-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { Building, Mail, Phone, User } from 'lucide-react'\nimport { useForm } from 'react-hook-form'\nimport * as z from 'zod'\n\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/registry/default/ui/ui-form'\nimport {\n  TextField,\n  TextFieldAffix,\n  TextFieldInlineAffix,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nconst formSchema = z.object({\n  username: z\n    .string()\n    .min(1, { message: '请输入用户名' })\n    .min(2, { message: '用户名至少需要2个字符' }),\n  email: z\n    .string()\n    .min(1, { message: '请输入邮箱' })\n    .email({ message: '请输入有效的邮箱地址' }),\n  phone: z\n    .string()\n    .min(1, { message: '请输入手机号' })\n    .regex(/^1[3-9]\\d{9}$/, { message: '请输入有效的手机号' }),\n  company: z\n    .string()\n    .min(1, { message: '请输入公司名称' })\n    .min(2, { message: '公司名称至少需要2个字符' }),\n})\n\nexport default function TextFieldFormDemo() {\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      username: '小明',\n      email: '',\n      phone: '',\n      company: '',\n    },\n  })\n\n  const onSubmit = (values: z.infer<typeof formSchema>) => {\n    console.log('表单提交数据:', values)\n    alert(`表单提交成功！\\n${JSON.stringify(values, null, 2)}`)\n  }\n\n  return (\n    <div className=\"mx-auto max-w-md space-y-6\">\n      <div className=\"space-y-2 text-center\">\n        <h2 className=\"text-lg font-semibold\">用户注册表单</h2>\n        <p className=\"text-sm text-gray-600\">\n          展示 TextField 与 Form 结合的完整输入场景\n        </p>\n      </div>\n\n      <Form {...form} onSubmit={onSubmit} className=\"space-y-4\">\n        {/* 用户名输入框 */}\n        <FormField control={form.control} name=\"username\">\n          <FormItem>\n            <FormLabel required>用户名</FormLabel>\n            <FormControl>\n              <TextField allowClear size=\"medium\">\n                <TextFieldWrapper>\n                  <TextFieldInlineAffix>\n                    <User className=\"h-3.5 w-3.5 text-gray-400\" />\n                  </TextFieldInlineAffix>\n                  <TextFieldInput placeholder=\"请输入用户名\" />\n                </TextFieldWrapper>\n              </TextField>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        {/* 邮箱输入框 */}\n        <FormField control={form.control} name=\"email\">\n          <FormItem>\n            <FormLabel required>邮箱地址</FormLabel>\n            <FormControl>\n              <TextField allowClear size=\"medium\">\n                <TextFieldWrapper>\n                  <TextFieldInlineAffix>\n                    <Mail className=\"h-3.5 w-3.5 text-gray-400\" />\n                  </TextFieldInlineAffix>\n                  <TextFieldInput type=\"email\" placeholder=\"请输入邮箱地址\" />\n                </TextFieldWrapper>\n              </TextField>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        {/* 手机号输入框 */}\n        <FormField control={form.control} name=\"phone\">\n          <FormItem>\n            <FormLabel required>手机号码</FormLabel>\n            <FormControl>\n              <TextField allowClear size=\"medium\">\n                <TextFieldAffix>+86</TextFieldAffix>\n                <TextFieldWrapper>\n                  <TextFieldInlineAffix>\n                    <Phone className=\"h-3.5 w-3.5 text-gray-400\" />\n                  </TextFieldInlineAffix>\n                  <TextFieldInput type=\"tel\" placeholder=\"请输入手机号码\" />\n                </TextFieldWrapper>\n              </TextField>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        {/* 公司名称输入框 */}\n        <FormField control={form.control} name=\"company\">\n          <FormItem>\n            <FormLabel required>公司名称</FormLabel>\n            <FormControl>\n              <TextField allowClear size=\"medium\">\n                <TextFieldWrapper>\n                  <TextFieldInlineAffix>\n                    <Building className=\"h-3.5 w-3.5 text-gray-400\" />\n                  </TextFieldInlineAffix>\n                  <TextFieldInput placeholder=\"请输入公司名称\" />\n                  <TextFieldInlineAffix>公司</TextFieldInlineAffix>\n                </TextFieldWrapper>\n              </TextField>\n            </FormControl>\n            <FormMessage />\n          </FormItem>\n        </FormField>\n\n        <div className=\"flex gap-3 pt-4\">\n          <Button type=\"submit\" className=\"flex-1\">\n            注册账户\n          </Button>\n          <Button type=\"button\" variant=\"outlined\" onClick={() => form.reset()}>\n            重置表单\n          </Button>\n        </div>\n      </Form>\n\n      {/* 表单状态展示 */}\n      <div className=\"mt-6 rounded-lg bg-gray-50 p-4\">\n        <h3 className=\"mb-2 text-sm font-medium\">表单状态</h3>\n        <div className=\"space-y-1 text-xs\">\n          <div>是否有效: {form.formState.isValid ? '✅' : '❌'}</div>\n          <div>是否已提交: {form.formState.isSubmitted ? '✅' : '❌'}</div>\n          <div>错误数量: {Object.keys(form.formState.errors).length}</div>\n          <div>\n            已修改字段: {Object.keys(form.formState.dirtyFields).length}\n          </div>\n        </div>\n\n        {/* 当前表单值展示 */}\n        <div className=\"mt-3 border-t border-gray-200 pt-3\">\n          <h4 className=\"mb-2 text-xs font-medium\">当前值:</h4>\n          <pre className=\"overflow-auto text-xs text-gray-600\">\n            {JSON.stringify(form.watch(), null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/hasError-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport {\n  TextField,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nexport default function TextFieldDemo() {\n  // 添加验证状态\n  const [username, setUsername] = useState('')\n  const [email, setEmail] = useState('')\n  const [phone, setPhone] = useState('')\n\n  // 添加验证函数\n  const validateUsername = (value: string) => {\n    return value.length >= 3 || value === ''\n  }\n\n  const validateEmail = (value: string) => {\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value) || value === ''\n  }\n\n  const validatePhone = (value: string) => {\n    return /^\\d{11}$/.test(value) || value === ''\n  }\n\n  // 错误状态\n  const usernameError = !validateUsername(username)\n  const emailError = !validateEmail(email)\n  const phoneError = !validatePhone(phone)\n\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <div className=\"mb-1 text-sm font-medium\">用户名 (至少3个字符)</div>\n        <TextField hasError={usernameError} allowClear defaultValue=\"123\">\n          <TextFieldWrapper>\n            <TextFieldInput\n              placeholder=\"请输入用户名\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n            />\n          </TextFieldWrapper>\n        </TextField>\n        {usernameError && username !== '' && (\n          <div className=\"text-red-6 mt-1 text-xs\">用户名至少需要3个字符</div>\n        )}\n      </div>\n\n      <div className=\"mb-6 w-[300px]\">\n        <div className=\"mb-1 text-sm font-medium\">电子邮件</div>\n        <TextField hasError={emailError} allowClear>\n          <TextFieldWrapper>\n            <TextFieldInput\n              placeholder=\"请输入电子邮件\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n            />\n          </TextFieldWrapper>\n        </TextField>\n        {emailError && email !== '' && (\n          <div className=\"text-red-6 mt-1 text-xs\">\n            请输入有效的电子邮件地址\n          </div>\n        )}\n      </div>\n\n      <div className=\"mb-6 w-[300px]\">\n        <div className=\"mb-1 text-sm font-medium\">手机号码 (11位数字)</div>\n        <TextField hasError={phoneError} allowClear>\n          <TextFieldWrapper>\n            <TextFieldInput\n              placeholder=\"请输入手机号码\"\n              value={phone}\n              onChange={(e) => setPhone(e.target.value)}\n            />\n          </TextFieldWrapper>\n        </TextField>\n        {phoneError && phone !== '' && (\n          <div className=\"text-red-6 mt-1 text-xs\">\n            请输入有效的11位手机号码\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/icon-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldIcon,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nimport { EyeIcon, UserIcon } from './icon'\n\nexport default function TextFieldDemo() {\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"medium\">\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"small\">\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/icon.tsx", "content": "export const UserIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 24 24\"\n      fill=\"currentColor\"\n      {...props}\n    >\n      <path d=\"M20 22H18V20C18 18.3431 16.6569 17 15 17H9C7.34315 17 6 18.3431 6 20V22H4V20C4 17.2386 6.23858 15 9 15H15C17.7614 15 20 17.2386 20 20V22ZM12 13C8.68629 13 6 10.3137 6 7C6 3.68629 8.68629 1 12 1C15.3137 1 18 3.68629 18 7C18 10.3137 15.3137 13 12 13ZM12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z\"></path>\n    </svg>\n  )\n}\n\nexport const EyeIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 16 16\"\n      fill=\"currentColor\"\n      {...props}\n    >\n      <path d=\"M0.787598 8C1.4146 4.58651 4.40525 2 8.00004 2C11.5948 2 14.5854 4.58651 15.2124 8C14.5854 11.4135 11.5948 14 8.00004 14C4.40525 14 1.4146 11.4135 0.787598 8ZM8.00004 11.3333C9.84097 11.3333 11.3334 9.84093 11.3334 8C11.3334 6.15905 9.84097 4.66667 8.00004 4.66667C6.15906 4.66667 4.66668 6.15905 4.66668 8C4.66668 9.84093 6.15906 11.3333 8.00004 11.3333ZM8.00004 10C6.89544 10 6.00001 9.1046 6.00001 8C6.00001 6.8954 6.89544 6 8.00004 6C9.10457 6 10 6.8954 10 8C10 9.1046 9.10457 10 8.00004 10Z\" />\n    </svg>\n  )\n}\n\nexport const SearchIcon = (props: React.HTMLAttributes<SVGSVGElement>) => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 24 24\"\n      fill=\"currentColor\"\n      {...props}\n    >\n      <path d=\"M18.031 16.617L22.314 20.899L20.899 22.314L16.617 18.031C15.0237 19.3082 13.042 20.0029 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20.0029 13.042 19.3082 15.0237 18.031 16.617ZM16.025 15.875C17.2941 14.5699 18.0029 12.8204 18 11C18 7.132 14.867 4 11 4C7.132 4 4 7.132 4 11C4 14.867 7.132 18 11 18C12.8204 18.0029 14.5699 17.2941 15.875 16.025L16.025 15.875Z\"></path>\n    </svg>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/inline-affix-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldInlineAffix,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nexport default function TextFieldDemo() {\n  return (\n    <div className=\"mb-6 w-[300px]\">\n      <TextField>\n        <TextFieldWrapper>\n          <TextFieldInlineAffix>￥</TextFieldInlineAffix>\n          <TextFieldInput placeholder=\"请输入\" />\n          <TextFieldInlineAffix>RMB</TextFieldInlineAffix>\n        </TextFieldWrapper>\n      </TextField>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/password-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldIcon,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\nimport { EyeIcon, EyeOffIcon } from '@/registry/default/ui/ui-textField/icon'\n\nexport default function PasswordDemo() {\n  const [password, setPassword] = React.useState('')\n  const [isVisible, setIsVisible] = React.useState(false)\n\n  const toggleVisibility = () => {\n    setIsVisible(!isVisible)\n  }\n\n  return (\n    <div className=\"w-[300px] space-y-4\">\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">密码输入框</label>\n        <TextField>\n          <TextFieldWrapper>\n            <TextFieldInput\n              type={isVisible ? 'text' : 'password'}\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              placeholder=\"请输入密码\"\n            />\n            <TextFieldIcon\n              className=\"hover:text-imileBlue-6 cursor-pointer text-[#444757] transition-colors\"\n              onClick={toggleVisibility}\n            >\n              {isVisible ? <EyeOffIcon /> : <EyeIcon />}\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n        <div className=\"text-gray-6 mt-2 text-xs\">\n          <p>密码长度: {password.length}</p>\n          <p>显示状态: {isVisible ? '显示' : '隐藏'}</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/size-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  TextField,\n  TextFieldIcon,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nimport { EyeIcon } from './icon'\n\nexport default function TextFieldDemo() {\n  return (\n    <div className=\"w-[300px] space-y-6\">\n      <div className=\"w-[300px]\">\n        <div className=\"mb-2 text-sm text-gray-600\">small</div>\n        <TextField size=\"small\">\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"w-[300px]\">\n        <div className=\"mb-2 text-sm text-gray-600\">medium (默认)</div>\n        <TextField size=\"medium\">\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"w-[300px]\">\n        <div className=\"mb-2 text-sm text-gray-600\">large</div>\n        <TextField size=\"large\">\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-textField/variant-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  TextField,\n  TextFieldAffix,\n  TextFieldIcon,\n  TextFieldInput,\n  TextFieldWrapper,\n} from '@/registry/default/ui/ui-textField'\n\nimport { EyeIcon, UserIcon } from './icon'\n\nexport default function TextAreaDemo() {\n  const [variant, setVariant] = React.useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n  return (\n    <div className=\"w-[300px]\">\n      <div className=\"mb-6 flex gap-x-2\">\n        <Button\n          variant={variant === 'outlined' ? undefined : 'outlined'}\n          onClick={() => setVariant('outlined')}\n        >\n          outlined\n        </Button>\n        <Button\n          variant={variant === 'filled' ? undefined : 'outlined'}\n          onClick={() => setVariant('filled')}\n        >\n          filled\n        </Button>\n        <Button\n          variant={variant === 'ghost' ? undefined : 'outlined'}\n          onClick={() => setVariant('ghost')}\n        >\n          ghost\n        </Button>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField allowClear variant={variant}>\n          <TextFieldAffix>http://</TextFieldAffix>\n          <TextFieldWrapper>\n            <TextFieldInput placeholder=\"请输入\" />\n          </TextFieldWrapper>\n          <TextFieldAffix>com</TextFieldAffix>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"medium\" variant={variant}>\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"small\" variant={variant}>\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n      <div className=\"mb-6 w-[300px]\">\n        <TextField size=\"small\" variant={variant}>\n          <TextFieldWrapper>\n            <TextFieldIcon>\n              <UserIcon />\n            </TextFieldIcon>\n            <TextFieldInput disabled placeholder=\"请输入\" />\n            <TextFieldIcon className=\"hover:text-primary cursor-pointer\">\n              <EyeIcon />\n            </TextFieldIcon>\n          </TextFieldWrapper>\n        </TextField>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:02:14.034Z", "gitCommit": "fea4e1d82bc25f380ede74f21aca061cb5fd3db5"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-form", "pro-textField"]}