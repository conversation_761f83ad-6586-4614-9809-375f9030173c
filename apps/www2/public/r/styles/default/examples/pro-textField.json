{"name": "pro-textField", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/pro-textField/add-before-after-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nexport default function Demo() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">添加前置和后置内容</h3>\n        <div className=\"flex flex-col gap-4\">\n          <div className=\"space-y-2\">\n            <h4 className=\"text-xs text-gray-500\">前置内容</h4>\n            <TextField placeholder=\"输入域名\" addBefore=\"http://\" />\n          </div>\n\n          <div className=\"space-y-2\">\n            <h4 className=\"text-xs text-gray-500\">后置内容</h4>\n            <TextField placeholder=\"输入域名\" addAfter=\".com\" />\n          </div>\n\n          <div className=\"space-y-2\">\n            <h4 className=\"text-xs text-gray-500\">前置和后置内容</h4>\n            <TextField\n              placeholder=\"输入域名\"\n              addBefore=\"http://\"\n              addAfter=\".com\"\n            />\n          </div>\n\n          <div className=\"text-sm text-gray-500\">\n            addBefore 和 addAfter\n            可以添加不可编辑的前置和后置内容，如URL协议和域名后缀\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/clear-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [size, setSize] = useState<'small' | 'medium' | 'large'>('medium')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 尺寸选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">尺寸</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={size}\n            onChange={(v) => setSize(v as 'small' | 'medium' | 'large')}\n          >\n            <RadioCard value=\"small\">小</RadioCard>\n            <RadioCard value=\"medium\">中</RadioCard>\n            <RadioCard value=\"large\">大</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">带清除按钮的输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            allowClear\n            size={size}\n            placeholder=\"请输入后显示清除按钮\"\n            suffix=\".com\"\n            defaultValue=\"可点击右侧清除\"\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            设置 allowClear 属性后，输入框有内容时会显示清除按钮\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/control-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { Button } from '@/components/ui/button'\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nexport default function Demo() {\n  const [value, setValue] = useState('小明')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 非受控示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">非受控模式</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            defaultValue=\"李四\"\n            placeholder=\"使用defaultValue，组件内部管理状态\"\n          />\n          <div className=\"text-sm text-gray-500\">\n            使用 defaultValue 时，组件内部管理自己的状态，无需外部 state\n          </div>\n        </div>\n      </div>\n\n      {/* 受控示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">受控模式</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            value={value}\n            placeholder=\"使用value和onChange，由React状态控制\"\n            onChange={(e) => setValue(e.target.value)}\n          />\n\n          <div className=\"flex items-center gap-3\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setValue('小李')}\n            >\n              设为 小李\n            </Button>\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setValue('')}>\n              清空\n            </Button>\n          </div>\n\n          <div className=\"text-sm text-gray-500\">\n            当前值: {value ? `\"${value}\"` : '空'}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/default-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nimport { UserIcon } from './icon'\n\nexport default function Demo() {\n  const [value, setValue] = useState('Hello World')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 基本样式 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">基本样式</h3>\n        <div className=\"flex flex-wrap gap-6\">\n          <TextField placeholder=\"默认样式\" />\n          <TextField placeholder=\"禁用状态\" disabled />\n          <TextField placeholder=\"错误状态\" hasError />\n        </div>\n      </div>\n\n      {/* 带图标和交互 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">带图标和交互</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            placeholder=\"请输入\"\n            defaultValue={value}\n            prefix={<UserIcon className=\"h-3.5 w-3.5\" />}\n            onChange={(e) => {\n              setValue(e.target.value)\n              console.log(e.target.value)\n            }}\n          />\n\n          <div className=\"text-sm text-gray-500\">当前输入值: {value}</div>\n        </div>\n      </div>\n\n      {/* 常用配置 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">常用配置</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            placeholder=\"带清除按钮\"\n            allowClear\n            defaultValue=\"可点击清除\"\n          />\n\n          <TextField placeholder=\"带前后缀\" prefix=\"￥\" suffix=\"RMB\" />\n\n          <div className=\"text-sm text-gray-500\">\n            TextField组件支持多种配置组合，满足不同使用场景\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/disabled-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [disabled, setDisabled] = useState<boolean>(true)\n  const [size, setSize] = useState<'small' | 'medium' | 'large'>('medium')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-4\">\n        {/* 禁用状态选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">禁用状态</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={disabled ? 'true' : 'false'}\n              onChange={(v) => setDisabled(v === 'true')}\n            >\n              <RadioCard value=\"false\">启用</RadioCard>\n              <RadioCard value=\"true\">禁用</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n\n        {/* 尺寸选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">尺寸</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={size}\n              onChange={(v) => setSize(v as 'small' | 'medium' | 'large')}\n            >\n              <RadioCard value=\"small\">小</RadioCard>\n              <RadioCard value=\"medium\">中</RadioCard>\n              <RadioCard value=\"large\">大</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">禁用状态的输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            size={size}\n            placeholder=\"禁用状态的输入框\"\n            suffix=\".com\"\n            disabled={disabled}\n            defaultValue={disabled ? '禁用状态无法编辑' : ''}\n          />\n          <TextField\n            variant=\"outlined\"\n            size={size}\n            placeholder=\"禁用状态的输入框\"\n            suffix=\".com\"\n            disabled={disabled}\n            defaultValue={disabled ? '禁用状态无法编辑' : ''}\n          />\n          <div className=\"text-sm text-gray-500\">\n            禁用状态下，输入框呈现灰色外观，用户无法与之交互\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/form-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { Building, Mail, Phone, User } from 'lucide-react'\nimport { useForm } from 'react-hook-form'\nimport * as z from 'zod'\n\nimport { Button } from '@/registry/default/ui/pro-button'\nimport { Form } from '@/registry/default/ui/pro-form'\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nconst formSchema = z.object({\n  username: z\n    .string()\n    .min(1, { message: '请输入用户名' })\n    .min(2, { message: '用户名至少需要2个字符' }),\n  email: z\n    .string()\n    .min(1, { message: '请输入邮箱' })\n    .email({ message: '请输入有效的邮箱地址' }),\n  phone: z\n    .string()\n    .min(1, { message: '请输入手机号' })\n    .regex(/^1[3-9]\\d{9}$/, { message: '请输入有效的手机号' }),\n  company: z\n    .string()\n    .min(1, { message: '请输入公司名称' })\n    .min(2, { message: '公司名称至少需要2个字符' }),\n})\n\nexport default function ProTextFieldFormDemo() {\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      username: '',\n      email: '',\n      phone: '',\n      company: '',\n    },\n  })\n\n  const onSubmit = (values: z.infer<typeof formSchema>) => {\n    console.log('表单提交数据:', values)\n    alert(`表单提交成功！\\n${JSON.stringify(values, null, 2)}`)\n  }\n\n  return (\n    <div className=\"mx-auto max-w-md space-y-6\">\n      <div className=\"space-y-2 text-center\">\n        <h2 className=\"text-lg font-semibold\">用户注册表单</h2>\n        <p className=\"text-sm text-gray-600\">\n          展示 ProTextField 与 ProForm 结合的高级输入场景\n        </p>\n      </div>\n\n      <Form form={form} onSubmit={onSubmit} className=\"space-y-4\">\n        {/* 用户名输入框 */}\n        <Form.Item name=\"username\" label=\"用户名\" required>\n          <TextField\n            placeholder=\"请输入用户名\"\n            prefix={<User className=\"h-3.5 w-3.5 text-gray-400\" />}\n            allowClear\n            size=\"medium\"\n          />\n        </Form.Item>\n\n        {/* 邮箱输入框 */}\n        <Form.Item name=\"email\" label=\"邮箱地址\" required>\n          <TextField\n            type=\"email\"\n            placeholder=\"请输入邮箱地址\"\n            prefix={<Mail className=\"h-3.5 w-3.5 text-gray-400\" />}\n            allowClear\n            size=\"medium\"\n          />\n        </Form.Item>\n\n        {/* 手机号输入框 */}\n        <Form.Item name=\"phone\" label=\"手机号码\" required>\n          <TextField\n            type=\"tel\"\n            placeholder=\"请输入手机号码\"\n            prefix={<Phone className=\"h-3.5 w-3.5 text-gray-400\" />}\n            addBefore=\"+86\"\n            allowClear\n            size=\"medium\"\n          />\n        </Form.Item>\n\n        {/* 公司名称输入框 */}\n        <Form.Item name=\"company\" label=\"公司名称\" required>\n          <TextField\n            placeholder=\"请输入公司名称\"\n            prefix={<Building className=\"h-3.5 w-3.5 text-gray-400\" />}\n            suffix=\"公司\"\n            allowClear\n            size=\"medium\"\n          />\n        </Form.Item>\n\n        <div className=\"flex gap-3 pt-4\">\n          <Button type=\"submit\" className=\"flex-1\">\n            注册账户\n          </Button>\n          <Button type=\"button\" variant=\"outlined\" onClick={() => form.reset()}>\n            重置表单\n          </Button>\n        </div>\n      </Form>\n\n      {/* 表单状态展示 */}\n      <div className=\"mt-6 rounded-lg bg-gray-50 p-4\">\n        <h3 className=\"mb-2 text-sm font-medium\">表单状态</h3>\n        <div className=\"space-y-1 text-xs\">\n          <div>是否有效: {form.formState.isValid ? '✅' : '❌'}</div>\n          <div>是否已提交: {form.formState.isSubmitted ? '✅' : '❌'}</div>\n          <div>错误数量: {Object.keys(form.formState.errors).length}</div>\n          <div>\n            已修改字段: {Object.keys(form.formState.dirtyFields).length}\n          </div>\n        </div>\n\n        {/* 当前表单值展示 */}\n        <div className=\"mt-3 border-t border-gray-200 pt-3\">\n          <h4 className=\"mb-2 text-xs font-medium\">当前值:</h4>\n          <pre className=\"overflow-auto text-xs text-gray-600\">\n            {JSON.stringify(form.watch(), null, 2)}\n          </pre>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/hasError-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [hasError, setHasError] = useState<boolean>(true)\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-4\">\n        {/* 错误状态选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">错误状态</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={hasError ? 'true' : 'false'}\n              onChange={(v) => setHasError(v === 'true')}\n            >\n              <RadioCard value=\"false\">正常</RadioCard>\n              <RadioCard value=\"true\">错误</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n\n        {/* 变体选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">变体</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={variant}\n              onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n            >\n              <RadioCard value=\"outlined\">outlined</RadioCard>\n              <RadioCard value=\"filled\">filled</RadioCard>\n              <RadioCard value=\"ghost\">ghost</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">错误状态的输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            variant={variant}\n            placeholder=\"请输入有效邮箱\"\n            hasError={hasError}\n            defaultValue={hasError ? 'invalid-email' : ''}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            错误状态下，输入框呈现红色外观，用于表单验证失败时提供视觉反馈\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/icon.tsx", "content": "export const UserIcon = (props: React.SVGProps<SVGSVGElement>) => {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        d=\"M13.3333 14.6667C13.3333 11.7212 10.9454 9.33342 7.99992 9.33342C5.05439 9.33342 2.66658 11.7212 2.66658 14.6667H13.3333ZM7.99992 8.66675C10.2099 8.66675 11.9999 6.87675 11.9999 4.66675C11.9999 2.45675 10.2099 0.666748 7.99992 0.666748C5.78992 0.666748 3.99992 2.45675 3.99992 4.66675C3.99992 6.87675 5.78992 8.66675 7.99992 8.66675Z\"\n        fill=\"#878B9C\"\n      />\n    </svg>\n  )\n}\n\nexport const EyeIcon = (props: React.SVGProps<SVGSVGElement>) => {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        d=\"M0.787598 8C1.4146 4.58651 4.40525 2 8.00004 2C11.5948 2 14.5854 4.58651 15.2124 8C14.5854 11.4135 11.5948 14 8.00004 14C4.40525 14 1.4146 11.4135 0.787598 8ZM8.00004 11.3333C9.84097 11.3333 11.3334 9.84093 11.3334 8C11.3334 6.15905 9.84097 4.66667 8.00004 4.66667C6.15906 4.66667 4.66668 6.15905 4.66668 8C4.66668 9.84093 6.15906 11.3333 8.00004 11.3333ZM8.00004 10C6.89544 10 6.00001 9.1046 6.00001 8C6.00001 6.8954 6.89544 6 8.00004 6C9.10457 6 10 6.8954 10 8C10 9.1046 9.10457 10 8.00004 10Z\"\n        fill=\"#444757\"\n      />\n    </svg>\n  )\n}\n\nexport const SearchIcon = (props: React.SVGProps<SVGSVGElement>) => {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        d=\"M12.0207 11.0781L14.8762 13.9336L13.9336 14.8762L11.0781 12.0207C10.0158 12.8722 8.68271 13.3352 7.33337 13.3333C4.02127 13.3333 1.33337 10.6454 1.33337 7.33333C1.33337 4.02133 4.02127 1.33333 7.33337 1.33333C10.6454 1.33333 13.3334 4.02133 13.3334 7.33333C13.3353 8.68267 12.8722 10.0158 12.0207 11.0781ZM10.6834 10.5833C11.5294 9.71333 12.0019 8.54693 12 7.33333C12 4.75533 9.91137 2.66667 7.33337 2.66667C4.75537 2.66667 2.66671 4.75533 2.66671 7.33333C2.66671 9.91133 4.75537 12 7.33337 12C8.54697 12.0019 9.71337 11.5294 10.5834 10.6833L10.6834 10.5833Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-controlled-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nexport default function PasswordControlledDemo() {\n  const [password, setPassword] = React.useState('')\n  const [isVisible, setIsVisible] = React.useState(false)\n\n  return (\n    <div className=\"flex w-80 flex-col gap-4\">\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">受控密码输入框</label>\n        <TextField.Password\n          value={password}\n          onChange={(e) => setPassword(e.target.value)}\n          visibility={isVisible}\n          onVisibilityChange={setIsVisible}\n          placeholder=\"请输入密码\"\n        />\n        <div className=\"text-gray-6 mt-2 text-xs\">\n          <p>密码长度: {password.length}</p>\n          <p>可见状态: {isVisible ? '显示' : '隐藏'}</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nexport default function PasswordDemo() {\n  return (\n    <div className=\"flex w-80 flex-col gap-4\">\n      <TextField.Password placeholder=\"请输入密码\" allowClear />\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-size-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nexport default function PasswordSizeDemo() {\n  return (\n    <div className=\"flex w-80 flex-col gap-4\">\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">小尺寸</label>\n        <TextField.Password size=\"small\" placeholder=\"请输入密码\" />\n      </div>\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">\n          中等尺寸（默认）\n        </label>\n        <TextField.Password size=\"medium\" placeholder=\"请输入密码\" />\n      </div>\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">大尺寸</label>\n        <TextField.Password size=\"large\" placeholder=\"请输入密码\" />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/password-variant-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nexport default function PasswordVariantDemo() {\n  return (\n    <div className=\"flex w-80 flex-col gap-4\">\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">Outlined 样式</label>\n        <TextField.Password variant=\"outlined\" placeholder=\"请输入密码\" />\n      </div>\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">\n          Filled 样式（默认）\n        </label>\n        <TextField.Password variant=\"filled\" placeholder=\"请输入密码\" />\n      </div>\n      <div>\n        <label className=\"mb-2 block text-sm font-medium\">Ghost 样式</label>\n        <TextField.Password variant=\"ghost\" placeholder=\"请输入密码\" />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/prefix-suffix-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\n\nimport { EyeIcon, UserIcon } from './icon'\n\nexport default function Demo() {\n  return (\n    <div className=\"space-y-8\">\n      {/* 图标前缀后缀 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">图标前缀后缀</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField\n            placeholder=\"带用户图标前缀\"\n            prefix={<UserIcon className=\"h-3.5 w-3.5\" />}\n          />\n\n          <TextField\n            placeholder=\"带眼睛图标后缀\"\n            suffix={<EyeIcon className=\"h-3.5 w-3.5\" />}\n          />\n\n          <div className=\"text-sm text-gray-500\">\n            可以在输入框前后添加图标，提高视觉识别度\n          </div>\n        </div>\n      </div>\n\n      {/* 文本前缀后缀 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">文本前缀后缀</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField placeholder=\"请输入金额\" prefix=\"$\" suffix=\"美元\" />\n\n          <div className=\"text-sm text-gray-500\">\n            可以在输入框前后添加文本标识，如货币符号、单位等\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/search-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function TextFieldSearchDemo() {\n  const [size, setSize] = useState<'small' | 'medium' | 'large'>('medium')\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'filled'\n  )\n  const [searchValue, setSearchValue] = useState('')\n  const [searchResult, setSearchResult] = useState('')\n\n  const handleSearch = (value: string | number) => {\n    setSearchResult(`搜索内容: ${value}`)\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 控制区域 */}\n      <div className=\"space-y-4\">\n        {/* 尺寸选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">尺寸</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={size}\n              onChange={(v) => setSize(v as 'small' | 'medium' | 'large')}\n            >\n              <RadioCard value=\"small\">小</RadioCard>\n              <RadioCard value=\"medium\">中</RadioCard>\n              <RadioCard value=\"large\">大</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n\n        {/* 变体选择 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">变体</h3>\n          <div className=\"flex items-center gap-2\">\n            <RadioCards\n              value={variant}\n              onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n            >\n              <RadioCard value=\"outlined\">outlined</RadioCard>\n              <RadioCard value=\"filled\">filled</RadioCard>\n              <RadioCard value=\"ghost\">ghost</RadioCard>\n            </RadioCards>\n          </div>\n        </div>\n      </div>\n\n      {/* 搜索框示例 */}\n      <div className=\"space-y-6\">\n        {/* 带图标按钮的搜索框 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">带图标按钮的搜索框</h3>\n          <div className=\"flex flex-col gap-4\">\n            <TextField.Search\n              size={size}\n              variant={variant}\n              placeholder=\"点击按钮搜索\"\n              value={searchValue}\n              onChange={(e) => setSearchValue(e.target.value)}\n              onSearch={handleSearch}\n            />\n          </div>\n        </div>\n\n        {/* 带文本按钮的搜索框 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">带文本按钮的搜索框</h3>\n          <div className=\"flex flex-col gap-4\">\n            <TextField.Search\n              size={size}\n              variant={variant}\n              placeholder=\"点击按钮搜索\"\n              searchText=\"搜索\"\n              onSearch={handleSearch}\n            />\n            <div className=\"text-sm text-gray-500\">\n              使用 searchText 属性可以设置按钮文本，搜索框会自动调整内边距\n            </div>\n          </div>\n        </div>\n\n        {/* 自定义按钮样式 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">自定义按钮样式</h3>\n          <div className=\"flex flex-col gap-4\">\n            <TextField.Search\n              size={size}\n              variant={variant}\n              placeholder=\"点击按钮搜索\"\n              buttonProps={{\n                color: 'secondary',\n              }}\n              onSearch={handleSearch}\n            />\n            <div className=\"text-sm text-gray-500\">\n              通过 buttonProps 属性可以自定义按钮的样式\n            </div>\n          </div>\n        </div>\n\n        {/* 禁用状态 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">禁用状态的搜索框</h3>\n          <div className=\"flex flex-col gap-4\">\n            <TextField.Search\n              size={size}\n              variant={variant}\n              placeholder=\"禁用状态\"\n              searchText=\"搜索\"\n              onSearch={handleSearch}\n              disabled\n            />\n          </div>\n        </div>\n\n        {/* Loading状态 */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">Loading状态的搜索框</h3>\n          <div className=\"flex flex-col gap-4\">\n            <TextField.Search\n              size={size}\n              variant={variant}\n              placeholder=\"Loading状态\"\n              searchText=\"搜索\"\n              onSearch={handleSearch}\n              loading\n            />\n          </div>\n        </div>\n\n        {/* 彩色背景下的Ghost变体 */}\n        {variant === 'ghost' && (\n          <div className=\"space-y-2\">\n            <h3 className=\"text-sm font-medium\">彩色背景下的Ghost变体</h3>\n            <div className=\"flex flex-col gap-4\">\n              <div className=\"bg-gray-2 rounded p-4\">\n                <TextField.Search\n                  size={size}\n                  variant=\"ghost\"\n                  placeholder=\"Ghost变体适合彩色背景\"\n                  searchText=\"搜索\"\n                  onSearch={handleSearch}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 搜索结果 */}\n      {searchResult && (\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium\">搜索结果</h3>\n          <div className=\"bg-gray-1 rounded p-3 text-sm\">{searchResult}</div>\n        </div>\n      )}\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/size-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [size, setSize] = useState<'small' | 'medium' | 'large'>('medium')\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 尺寸选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">尺寸</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={size}\n            onChange={(v) => setSize(v as 'small' | 'medium' | 'large')}\n          >\n            <RadioCard value=\"small\">小</RadioCard>\n            <RadioCard value=\"medium\">中</RadioCard>\n            <RadioCard value=\"large\">大</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同尺寸的输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField size={size} placeholder=\"请输入\" suffix=\".com\" />\n\n          <div className=\"text-sm text-gray-500\">当前尺寸: {size}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/pro-textField/variant-demo.tsx", "content": "'use client'\n\nimport React, { useState } from 'react'\n\nimport { TextField } from '@/registry/default/ui/pro-textField'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function Demo() {\n  const [variant, setVariant] = useState<'outlined' | 'filled' | 'ghost'>(\n    'outlined'\n  )\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 变体选择控制 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">变体</h3>\n        <div className=\"flex items-center gap-2\">\n          <RadioCards\n            value={variant}\n            onChange={(v) => setVariant(v as 'outlined' | 'filled' | 'ghost')}\n          >\n            <RadioCard value=\"outlined\">outlined</RadioCard>\n            <RadioCard value=\"filled\">filled</RadioCard>\n            <RadioCard value=\"ghost\">ghost</RadioCard>\n          </RadioCards>\n        </div>\n      </div>\n\n      {/* 组件展示 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">不同变体的输入框</h3>\n        <div className=\"flex flex-col gap-4\">\n          <TextField allowClear placeholder=\"请输入\" variant={variant} />\n\n          <div className=\"bg-gray-2 rounded p-4\">\n            <TextField\n              allowClear\n              placeholder=\"请输入（彩色背景示例）\"\n              variant={variant}\n            />\n          </div>\n\n          <div className=\"text-sm text-gray-500\">当前变体: {variant}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "6bbd0a164dc5fad98cecc5d911b01068aaf4938a"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-batch-input"]}