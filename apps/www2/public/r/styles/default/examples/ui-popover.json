{"name": "ui-popover", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-popover/action-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverArrow,\n  PopoverContent,\n  PopoverDescription,\n  PopoverPortal,\n  PopoverTitle,\n  PopoverTrigger,\n  type TriggerAction,\n} from '@/registry/default/ui/ui-popover'\n\nconst renderPopover = ({\n  action,\n  name,\n}: {\n  action: TriggerAction\n  name: string\n}) => (\n  <Popover key={action} action={action}>\n    <PopoverTrigger asChild>\n      <Button variant=\"outlined\">{name}</Button>\n    </PopoverTrigger>\n    <PopoverPortal>\n      <PopoverContent className=\"max-w-[263px]\">\n        <PopoverTitle>Popover title</PopoverTitle>\n        <PopoverDescription>\n          Conveniently initiate viral synergy without multi functional platforms\n        </PopoverDescription>\n        <PopoverArrow />\n      </PopoverContent>\n    </PopoverPortal>\n  </Popover>\n)\n\nconst actions = [\n  {\n    action: 'hover',\n    name: 'Hover',\n  },\n  {\n    action: 'click',\n    name: 'Click',\n  },\n  {\n    action: 'contextMenu',\n    name: 'Context Menu',\n  },\n  {\n    action: 'focus',\n    name: 'Focus',\n  },\n] as const\n\nexport default function PopoverUIActionDemo() {\n  return (\n    <div className={cn('flex justify-center gap-8')}>\n      {actions.map((action) => renderPopover(action))}\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/anchor-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverAnchor,\n  PopoverContent,\n  PopoverDescription,\n  PopoverPortal,\n  PopoverTitle,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\n\nexport default function PopoverAnchorDemo() {\n  return (\n    <div className=\"space-y-8\">\n      {/* 基本示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">基本使用</h3>\n        <div className=\"flex items-center gap-6\">\n          <Popover>\n            <PopoverTrigger asChild>\n              <Button variant=\"outlined\">触发按钮</Button>\n            </PopoverTrigger>\n            <PopoverPortal>\n              <PopoverContent>\n                <PopoverTitle>相对于触发器定位</PopoverTitle>\n                <PopoverDescription>\n                  默认情况下，弹出框相对于触发按钮定位\n                </PopoverDescription>\n              </PopoverContent>\n            </PopoverPortal>\n          </Popover>\n        </div>\n      </div>\n\n      {/* 使用锚点示例 */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium\">使用锚点定位</h3>\n        <div className=\"flex items-center gap-6\">\n          <Popover>\n            <PopoverAnchor className=\"mr-4 inline-block rounded border border-dashed border-gray-400 bg-gray-100 p-2\">\n              锚点元素\n            </PopoverAnchor>\n            <PopoverTrigger asChild>\n              <Button variant=\"outlined\">点击这里</Button>\n            </PopoverTrigger>\n            <PopoverPortal>\n              <PopoverContent>\n                <PopoverTitle>相对于锚点定位</PopoverTitle>\n                <PopoverDescription>\n                  使用 PopoverAnchor\n                  可以让弹出框相对于锚点元素定位，而不是触发按钮\n                </PopoverDescription>\n              </PopoverContent>\n            </PopoverPortal>\n          </Popover>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/arrow-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { cn } from '@/lib/utils'\nimport { But<PERSON> } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverArrow,\n  PopoverContent,\n  PopoverDescription,\n  PopoverPortal,\n  PopoverTitle,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\n\nexport default function PopoverArrowActionDemo() {\n  return (\n    <div className={cn('flex justify-center gap-8')}>\n      <Popover>\n        <PopoverTrigger asChild>\n          <Button variant=\"outlined\">With Arrow</Button>\n        </PopoverTrigger>\n        <PopoverPortal>\n          <PopoverContent className=\"max-w-[263px]\">\n            <PopoverTitle>Popover title</PopoverTitle>\n            <PopoverDescription>\n              Conveniently initiate viral synergy without multi functional\n              platforms\n            </PopoverDescription>\n            <PopoverArrow />\n          </PopoverContent>\n        </PopoverPortal>\n      </Popover>\n\n      <Popover>\n        <PopoverTrigger asChild>\n          <Button variant=\"outlined\">Without Arrow</Button>\n        </PopoverTrigger>\n        <PopoverPortal>\n          <PopoverContent className=\"max-w-[263px]\">\n            <PopoverTitle>Popover title</PopoverTitle>\n            <PopoverDescription>\n              Conveniently initiate viral synergy without multi functional\n              platforms\n            </PopoverDescription>\n          </PopoverContent>\n        </PopoverPortal>\n      </Popover>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/auto-close-demo.tsx", "content": "'use client'\n\nimport React, { useEffect, useRef } from 'react'\n\nimport { cn } from '@/lib/utils'\nimport { But<PERSON> } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverArrow,\n  PopoverClose,\n  PopoverContent,\n  PopoverDescription,\n  PopoverPortal,\n  PopoverTitle,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\n\nconst AutoClosePopover = () => {\n  const [open, setOpen] = React.useState(true)\n\n  const isCloseRef = React.useRef(false)\n\n  const timeoutRef = useRef<any>(null)\n\n  const onClose = () => {\n    isCloseRef.current = true\n    setOpen(false)\n  }\n\n  useEffect(() => {\n    timeoutRef.current = setTimeout(onClose, 10 * 1000)\n    return () => clearTimeout(timeoutRef.current)\n  }, [])\n\n  return (\n    <Popover\n      open={open}\n      onOpenChange={(status) => isCloseRef.current && setOpen(status)}\n    >\n      <PopoverTrigger asChild>\n        <Button variant=\"outlined\">Close After 10s</Button>\n      </PopoverTrigger>\n      <PopoverPortal>\n        <PopoverContent className=\"max-w-[263px]\">\n          <PopoverTitle>Popover title</PopoverTitle>\n          <PopoverDescription>Short description</PopoverDescription>\n          <PopoverArrow />\n          <PopoverClose onClick={onClose} />\n        </PopoverContent>\n      </PopoverPortal>\n    </Popover>\n  )\n}\n\nconst ManualClosePopover = () => {\n  const [open, setOpen] = React.useState(true)\n\n  const isCloseRef = React.useRef(false)\n\n  return (\n    <Popover\n      open={open}\n      onOpenChange={(status) => isCloseRef.current && setOpen(status)}\n    >\n      <PopoverTrigger asChild>\n        <Button variant=\"outlined\">Manual Close</Button>\n      </PopoverTrigger>\n      <PopoverPortal>\n        <PopoverContent className=\"max-w-[263px]\">\n          <PopoverTitle>Popover title</PopoverTitle>\n          <PopoverDescription>\n            Conveniently initiate viral synergy without multi functional\n            platforms\n          </PopoverDescription>\n          <PopoverArrow />\n          <PopoverClose\n            onClick={() => {\n              isCloseRef.current = true\n              setOpen(false)\n            }}\n          />\n        </PopoverContent>\n      </PopoverPortal>\n    </Popover>\n  )\n}\n\nexport default function PopoverUIAutoCloseDemo() {\n  return (\n    <div className={cn('flex justify-center gap-32')}>\n      <AutoClosePopover />\n      <ManualClosePopover />\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/customize-content-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverArrow,\n  PopoverContent,\n  PopoverPortal,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\n\nexport default function PopoverUICustomizeDemo() {\n  const [open, setOpen] = React.useState(false)\n\n  return (\n    <Popover open={open}>\n      <PopoverTrigger asChild>\n        <Button variant=\"outlined\" onClick={() => setOpen(true)}>\n          Click To Open\n        </Button>\n      </PopoverTrigger>\n      <PopoverPortal>\n        <PopoverContent>\n          <PopoverArrow />\n          <section className={cn('w-full')}>\n            <h2 className={cn('font-bold')}>Tip</h2>\n            <div className={cn('my-2 flex items-center justify-between')}>\n              Do any thing you like\n              <Button\n                className={cn('ml-2 text-right')}\n                onClick={() => setOpen(false)}\n              >\n                Close\n              </Button>\n            </div>\n          </section>\n        </PopoverContent>\n      </PopoverPortal>\n    </Popover>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/default-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverDescription,\n  PopoverPortal,\n  PopoverTitle,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\n\nexport default function PopoverUIDemo() {\n  return (\n    <Popover>\n      <PopoverTrigger asChild>\n        <Button variant=\"outlined\">点击显示弹出框</Button>\n      </PopoverTrigger>\n      <PopoverPortal>\n        <PopoverContent className=\"max-w-[263px]\">\n          <PopoverTitle>弹出框标题</PopoverTitle>\n          <PopoverDescription>\n            这是弹出框的描述内容，用于展示额外信息或操作选项\n          </PopoverDescription>\n        </PopoverContent>\n      </PopoverPortal>\n    </Popover>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/position-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverArrow,\n  PopoverClose,\n  PopoverContent,\n  PopoverDescription,\n  PopoverIcon,\n  PopoverPortal,\n  PopoverTitle,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\nimport { PopoverSuccessIcon } from '@/registry/default/ui/ui-popover/icon'\n\nconst StyleMap = {\n  left: { gridColumnStart: 1 },\n  right: { gridColumnStart: 5 },\n  top: { gridRowStart: 1 },\n  bottom: { gridRowStart: 5 },\n}\n\nconst sideOption = ['top', 'right', 'bottom', 'left'] as const\nconst alignOption = ['start', 'center', 'end'] as const\n\nexport default function PopoverUIPositionDemo() {\n  return (\n    <div\n      className={cn(\n        'grid w-full max-w-2xl grid-cols-5 grid-rows-5 gap-2 min-w-[600px]'\n      )}\n    >\n      {sideOption.map((side) =>\n        alignOption.map((align, j) => (\n          <div\n            key={`${side}-${align}`}\n            style={{\n              gridColumnStart: ['top', 'bottom'].includes(side) ? j + 2 : 0,\n              gridRowStart: j + 2,\n              ...StyleMap[side],\n            }}\n          >\n            <Popover>\n              <PopoverTrigger asChild>\n                <Button variant=\"outlined\" className=\"w-full\">\n                  {`${side}-${align}`}\n                </Button>\n              </PopoverTrigger>\n              <PopoverPortal>\n                <PopoverContent\n                  className=\"max-w-[263px]\"\n                  side={side}\n                  align={align}\n                >\n                  <PopoverIcon>\n                    <PopoverSuccessIcon />\n                  </PopoverIcon>\n                  <PopoverTitle>Popover title</PopoverTitle>\n                  <PopoverDescription>\n                    Conveniently initiate viral synergy without multi functional\n                    platforms.\n                  </PopoverDescription>\n                  <PopoverArrow />\n                  <PopoverClose />\n                </PopoverContent>\n              </PopoverPortal>\n            </Popover>\n          </div>\n        ))\n      )}\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-popover/status-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/registry/default/ui/ui-button'\nimport {\n  Popover,\n  PopoverArrow,\n  PopoverContent,\n  PopoverDescription,\n  PopoverIcon,\n  PopoverPortal,\n  PopoverTitle,\n  PopoverTrigger,\n} from '@/registry/default/ui/ui-popover'\nimport {\n  PopoverErrorIcon,\n  PopoverInfoIcon,\n  PopoverSuccessIcon,\n  PopoverWarningIcon,\n} from '@/registry/default/ui/ui-popover/icon'\n\nconst renderPopover = ({\n  name,\n  icon,\n}: {\n  name: string\n  icon: React.ReactNode\n}) => (\n  <Popover key={name}>\n    <PopoverTrigger asChild>\n      <Button variant=\"outlined\">{name}</Button>\n    </PopoverTrigger>\n    <PopoverPortal>\n      <PopoverContent className=\"max-w-[263px]\">\n        <PopoverIcon>{icon}</PopoverIcon>\n        <PopoverTitle>Popover title</PopoverTitle>\n        <PopoverDescription>\n          Conveniently initiate viral synergy without multi functional\n          platforms.\n        </PopoverDescription>\n        <PopoverArrow />\n      </PopoverContent>\n    </PopoverPortal>\n  </Popover>\n)\n\nconst status = [\n  { name: 'Info', icon: <PopoverInfoIcon /> },\n  { name: 'Success', icon: <PopoverSuccessIcon /> },\n  { name: 'Warning', icon: <PopoverWarningIcon /> },\n  { name: 'Error', icon: <PopoverErrorIcon /> },\n  {\n    name: 'Customize',\n    icon: (\n      <div\n        className={cn('h-[22px] w-[22px] bg-amber-300 text-center text-white')}\n      >\n        C\n      </div>\n    ),\n  },\n]\n\nexport default function PopoverProStatusDemo() {\n  return (\n    <div className={cn('flex justify-center gap-8')}>\n      {status.map(renderPopover)}\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "35db908e854616650b29777a372c7efc1e28daf5"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-popover", "pro-poptextarea"]}