{"name": "ui-button", "type": "registry:example", "files": [{"path": "registry/default/examples/ui/ui-button/asChild-demo.tsx", "content": "'use client'\n\nimport { But<PERSON> } from '@/registry/default/ui/ui-button'\n\nexport default function ButtonDemo() {\n  return (\n    <Button asChild>\n      <a href=\"#基础按钮\">asChild button</a>\n    </Button>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/color-demo.tsx", "content": "'use client'\n\nimport { Button } from '@/registry/default/ui/ui-button'\n\nexport default function ButtonDemo() {\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <h2 className=\"mb-2 text-lg\">主题色按钮</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button>default button</Button>\n        <Button color=\"secondary\">secondary button</Button>\n        <Button variant=\"outlined\">outline button</Button>\n        <Button variant=\"text\">ghost button</Button>\n      </div>\n      <h2 className=\"mb-2 text-lg\">color error 红色按钮</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button color=\"error\">default button</Button>\n        <Button color=\"light-error\">secondary button</Button>\n        <Button color=\"error\" variant=\"outlined\">\n          outline button\n        </Button>\n        <Button color=\"error\" variant=\"text\">\n          ghost button\n        </Button>\n      </div>\n      <h2 className=\"mb-2 text-lg\">linearGradient 渐变按钮</h2>\n      <div className=\"mb-4 flex gap-4 align-middle\">\n        <Button color=\"linear-gradient\">Button</Button>\n      </div>\n      <h2 className=\"mb-2 text-lg\">无框按钮-默认</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button color=\"default\" variant=\"text\">\n          ghost button\n        </Button>\n      </div>\n      <h2 className=\"mb-2 text-lg\">筛选区</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button color=\"secondary\">More</Button>\n        <Button color=\"reset\" variant=\"outlined\">\n          Reset\n        </Button>\n        <Button color=\"search\">Search</Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/default-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport { Button, ButtonProps } from '@/registry/default/ui/ui-button'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button size={size} onClick={() => console.log('click')}>\n          default button\n        </Button>\n        <Button\n          size={size}\n          onClick={() => console.log('click')}\n          color=\"secondary\"\n        >\n          secondary button\n        </Button>\n        <Button\n          size={size}\n          onClick={() => console.log('click')}\n          variant=\"outlined\"\n        >\n          outline button\n        </Button>\n        <Button size={size} onClick={() => console.log('click')} variant=\"text\">\n          ghost primary button\n        </Button>\n        <Button\n          size={size}\n          onClick={() => console.log('click')}\n          color=\"default\"\n          variant=\"text\"\n        >\n          ghost button\n        </Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/disabled-demo.tsx", "content": "'use client'\n\nimport { FolderUpIcon } from 'lucide-react'\n\nimport { Button, ButtonIcon } from '@/registry/default/ui/ui-button'\n\nexport default function ButtonDemo() {\n  return (\n    <div className=\"w-full\">\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button disabled>Button</Button>\n        <Button disabled color=\"secondary\">\n          secondary button\n        </Button>\n        <Button disabled variant=\"outlined\">\n          outline button\n        </Button>\n        <Button disabled variant=\"text\">\n          ghost button\n        </Button>\n        <Button color=\"default\" disabled variant=\"text\">\n          ghost button\n        </Button>\n      </div>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button disabled>\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button disabled color=\"secondary\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          secondary button\n        </Button>\n        <Button disabled variant=\"outlined\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          outline button\n        </Button>\n        <Button disabled variant=\"text\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          ghost button\n        </Button>\n        <Button color=\"default\" disabled variant=\"text\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          ghost button\n        </Button>\n      </div>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button color=\"error\" disabled>\n          Button\n        </Button>\n        <Button color=\"light-error\" disabled>\n          secondary button\n        </Button>\n        <Button color=\"error\" disabled variant=\"outlined\">\n          outline button\n        </Button>\n        <Button color=\"error\" disabled variant=\"text\">\n          ghost button\n        </Button>\n        <Button color=\"error\" disabled variant=\"text\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          ghost button\n        </Button>\n      </div>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button disabled>\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button disabled color=\"secondary\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button disabled variant=\"outlined\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button disabled variant=\"text\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button disabled color=\"default\" variant=\"text\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n      </div>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button color=\"linear-gradient\" disabled>\n          Button\n        </Button>\n      </div>\n      <h2 className=\"mb-2 text-lg\">筛选区</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4\">\n        <Button color=\"secondary\" disabled>\n          More\n        </Button>\n        <Button color=\"reset\" variant=\"outlined\" disabled>\n          Reset\n        </Button>\n        <Button color=\"search\" disabled>\n          Search\n        </Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/group-demo.tsx", "content": "'use client'\n\nimport React, { useMemo } from 'react'\n\nimport { Button, ButtonProps } from '@/registry/default/ui/ui-button'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n  const gap = useMemo(() => {\n    if (size === 'large') {\n      return 'gap-3'\n    } else {\n      return 'gap-2'\n    }\n  }, [size])\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <h2 className=\"mb-2 text-lg\">按钮组</h2>\n      <div className={`${gap} mb-4 flex flex-wrap`}>\n        <Button size={size} color=\"secondary\">\n          secondary button\n        </Button>\n        <Button size={size}>default button</Button>\n      </div>\n      <div className={`${gap} mb-4 flex flex-wrap`}>\n        <Button size={size} color=\"secondary\">\n          secondary button\n        </Button>\n        <Button size={size} color=\"secondary\">\n          secondary button\n        </Button>\n        <Button size={size}>default button</Button>\n      </div>\n      <div className={`${gap} mb-4 flex flex-wrap justify-end`}>\n        <Button size={size} color=\"secondary\">\n          Reset\n        </Button>\n        <Button size={size}>Search</Button>\n      </div>\n\n      <h2 className=\"mb-2 text-lg\">block 块按钮</h2>\n      <Button size={size} className=\"mb-4 w-full\">\n        Button\n      </Button>\n      <Button size={size} color=\"secondary\" className=\"mb-4 w-full\">\n        Button\n      </Button>\n      <Button size={size} variant=\"outlined\" className=\"mb-4 w-full\">\n        Button\n      </Button>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/headless-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { Search } from 'lucide-react'\n\nimport { Button, ButtonIcon } from '@/registry/default/ui/ui-button'\n\nexport default function ButtonDemo() {\n  return (\n    <div className=\"mb-4 flex items-center gap-2\">\n      <Button>\n        <ButtonIcon position=\"left\">\n          <Search />\n        </ButtonIcon>\n        搜索\n      </Button>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/icon-demo.tsx", "content": "'use client'\n\nimport React from 'react'\nimport { FolderUpIcon, Search } from 'lucide-react'\n\nimport {\n  Button,\n  ButtonIcon,\n  ButtonProps,\n} from '@/registry/default/ui/ui-button'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <h2 className=\"mb-2 text-lg\">icon 图标按钮</h2>\n      <div className=\"mb-4 flex gap-4 align-middle\">\n        <Button size={size}>\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} color=\"secondary\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} variant=\"outlined\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} variant=\"text\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} color=\"default\" variant=\"text\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n      </div>\n\n      <h2 className=\"mb-2 text-lg\">icon 图标红色按钮</h2>\n      <div className=\"mb-4 flex gap-4 align-middle\">\n        <Button size={size} color=\"error\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} color=\"light-error\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} color=\"error\" variant=\"outlined\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} color=\"error\" variant=\"text\">\n          <ButtonIcon>\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n      </div>\n\n      <h2 className=\"mb-2 text-lg\">icon 带左图标</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4 align-middle\">\n        <Button size={size}>\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} color=\"secondary\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"outlined\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\" color=\"default\">\n          <ButtonIcon position=\"left\">\n            <FolderUpIcon />\n          </ButtonIcon>\n          Button\n        </Button>\n      </div>\n\n      <h2 className=\"mb-2 text-lg\">icon 带右图标</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4 align-middle\">\n        <Button size={size}>\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} color=\"secondary\">\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} variant=\"outlined\">\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} variant=\"text\">\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} variant=\"text\" color=\"default\">\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n      </div>\n\n      <h2 className=\"mb-2 text-lg\">icon 带左右图标</h2>\n      <div className=\"mb-4 flex flex-wrap gap-4 align-middle\">\n        <Button size={size}>\n          <ButtonIcon position=\"left\">\n            <Search color=\"currentColor\" />\n          </ButtonIcon>\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} color=\"secondary\">\n          <ButtonIcon position=\"left\">\n            <Search color=\"currentColor\" />\n          </ButtonIcon>\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} variant=\"outlined\">\n          <ButtonIcon position=\"left\">\n            <Search color=\"currentColor\" />\n          </ButtonIcon>\n          Button\n          <ButtonIcon position=\"right\">\n            <FolderUpIcon />\n          </ButtonIcon>\n        </Button>\n        <Button size={size} variant=\"text\">\n          <ButtonIcon position=\"left\">\n            <Search color=\"currentColor\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\" color=\"default\">\n          <ButtonIcon position=\"left\">\n            <Search color=\"currentColor\" />\n          </ButtonIcon>\n          Button\n        </Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}, {"path": "registry/default/examples/ui/ui-button/loading-demo.tsx", "content": "'use client'\n\nimport React from 'react'\n\nimport {\n  Button,\n  ButtonIcon,\n  ButtonProps,\n} from '@/registry/default/ui/ui-button'\nimport { Loading } from '@/registry/default/ui/ui-loading'\nimport { RadioCard, RadioCards } from '@/registry/default/ui/ui-radio-group'\n\nexport default function ButtonDemo() {\n  const [size, setSize] = React.useState<ButtonProps['size']>('medium')\n\n  return (\n    <div className=\"flex h-full w-full flex-col justify-start gap-4\">\n      <div className=\"flex items-center gap-2\">\n        <div className=\"text-xs\">尺寸</div>\n        <RadioCards\n          value={size}\n          onChange={(v) => setSize(v as ButtonProps['size'])}\n        >\n          <RadioCard value=\"small\">小</RadioCard>\n          <RadioCard value=\"medium\">中</RadioCard>\n          <RadioCard value=\"large\">大</RadioCard>\n        </RadioCards>\n      </div>\n\n      <div className=\"mb-4 flex flex-wrap gap-4 align-middle\">\n        <Button size={size}>\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} color=\"secondary\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"outlined\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\" color=\"default\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n      </div>\n      <div className=\"mb-4 flex flex-wrap gap-4 align-middle\">\n        <Button size={size} color=\"error\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} color=\"light-error\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"outlined\" color=\"error\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n        <Button size={size} variant=\"text\" color=\"error\">\n          <ButtonIcon position=\"left\">\n            <Loading color=\"currentColor\" size=\"small\" />\n          </ButtonIcon>\n          Button\n        </Button>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:example"}], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "7c547122ee89fbcc40ed3d2ba53e744e6331415a"}, "generator": "imd-build-registry", "architectureLayer": "UI", "architectureMetrics": {"ca": 10, "ce": 0, "instability": 0}, "dependents": ["pro-button", "pro-dialog", "pro-drawer", "pro-dropdown-menu", "pro-fullscreen-dialog", "pro-page-filter", "pro-popconfirm", "pro-poptextarea", "ui-dropdown-menu", "ui-popconfirm"]}