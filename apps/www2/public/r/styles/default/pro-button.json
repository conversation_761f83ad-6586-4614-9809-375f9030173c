{"name": "pro-button", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-button/index.tsx", "content": "'use client'\n\nimport * as React from 'react'\nimport { useCallback, useState } from 'react'\nimport { useDebounceFn } from 'ahooks'\n\nimport { cn } from '@/lib/utils'\nimport { generateUtilityClasses } from '@/registry/default/lib/class-generator'\nimport {\n  Button as UIButton,\n  ButtonIcon as UIButtonIcon,\n  type ButtonProps as UIButtonProps,\n} from '@/registry/default/ui/ui-button'\nimport { Loading } from '@/registry/default/ui/ui-loading'\nimport {\n  Tooltip,\n  TooltipArrow,\n  TooltipContent,\n  TooltipTrigger,\n} from '@/registry/default/ui/ui-tooltip'\n\nconst buttonClasses = generateUtilityClasses('pro-button', [\n  'root',\n  'icon',\n  'left-icon',\n  'right-icon',\n  'loading-icon',\n])\n\ninterface ButtonProps extends UIButtonProps {\n  /** 图标，当按钮只显示图标时使用 */\n  icon?: React.ReactNode\n  /** 左侧图标 */\n  leftIcon?: React.ReactNode\n  /** 右侧图标 */\n  rightIcon?: React.ReactNode\n  /** 是否显示加载状态 */\n  loading?: boolean\n  /** 点击事件处理函数 */\n  onSubmit?: () => Promise<boolean> | void\n  onError?: (e: unknown) => void\n  /** 防抖时间，单位毫秒，默认 300ms */\n  debounceTime?: number\n  handleLoading?: boolean\n  /** 提示文本，鼠标悬停时显示 */\n  tips?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      children,\n      className,\n      icon,\n      leftIcon,\n      rightIcon,\n      loading: controlledLoading,\n      onSubmit,\n      debounceTime = 300,\n      handleLoading = true,\n      onError,\n      onClick,\n      tips,\n      ...props\n    },\n    ref\n  ) => {\n    const [internalLoading, setInternalLoading] = useState(false)\n    const loading = controlledLoading ?? internalLoading\n\n    const handleSubmit = useDebounceFn(\n      async () => {\n        if (!onSubmit) return\n        try {\n          handleLoading && setInternalLoading(true)\n          await onSubmit()\n        } catch (e) {\n          onError?.(e)\n        } finally {\n          handleLoading && setInternalLoading(false)\n        }\n      },\n      { wait: debounceTime }\n    )\n\n    const handleClick = useCallback(\n      async (e: React.MouseEvent<HTMLButtonElement>) => {\n        onClick?.(e)\n        await handleSubmit.run()\n      },\n      [onClick, handleSubmit]\n    )\n\n    const buttonElement = (\n      <UIButton\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        disabled={loading || props.disabled}\n        className={cn(className, buttonClasses.root)}\n      >\n        {loading && (\n          <UIButtonIcon\n            position=\"left\"\n            className={buttonClasses['loading-icon']}\n          >\n            <Loading color={'currentColor'} size=\"small\" />\n          </UIButtonIcon>\n        )}\n        {!loading && leftIcon && (\n          <UIButtonIcon position=\"left\" className={buttonClasses['left-icon']}>\n            {leftIcon}\n          </UIButtonIcon>\n        )}\n        {!loading && icon && (\n          <UIButtonIcon className={buttonClasses.icon}>{icon}</UIButtonIcon>\n        )}\n        {children}\n        {!loading && rightIcon && (\n          <UIButtonIcon\n            position=\"right\"\n            className={buttonClasses['right-icon']}\n          >\n            {rightIcon}\n          </UIButtonIcon>\n        )}\n      </UIButton>\n    )\n\n    if (tips) {\n      return (\n        <Tooltip>\n          <TooltipTrigger asChild>{buttonElement}</TooltipTrigger>\n          <TooltipContent>\n            {tips}\n            <TooltipArrow />\n          </TooltipContent>\n        </Tooltip>\n      )\n    }\n\n    return buttonElement\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button, type ButtonProps }\n", "type": "registry:ui"}], "dependencies": ["ahooks", "lodash-es"], "registryDependencies": ["class-generator", "ui-button", "ui-loading", "ui-tooltip"], "buildInfo": {"releaseTag": "release/20250625", "generatedAt": "2025-07-31T08:03:19.592Z", "gitCommit": "7c547122ee89fbcc40ed3d2ba53e744e6331415a"}, "generator": "imd-build-registry", "architectureLayer": "Pro", "architectureMetrics": {"ca": 2, "ce": 4, "instability": 0.6666666666666666}, "dependents": ["pro-batch-input", "pro-textField"]}