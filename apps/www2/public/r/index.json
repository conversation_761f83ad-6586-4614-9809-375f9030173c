[{"name": "base-affix", "type": "registry:ui", "files": [{"path": "../../packages/base-affix/src/affix.tsx", "type": "registry:ui"}, {"path": "../../packages/base-affix/src/config-provider/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-affix/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-affix/src/style/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-affix/src/utils/cn.ts", "type": "registry:ui"}, {"path": "../../packages/base-affix/src/utils/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-affix/src/utils/throttleByAnimationFrame.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-alert", "type": "registry:ui", "files": [{"path": "../../packages/base-alert/src/alert.tsx", "type": "registry:ui"}, {"path": "../../packages/base-alert/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-button", "type": "registry:ui", "files": [{"path": "../../packages/base-button/src/button.tsx", "type": "registry:ui"}, {"path": "../../packages/base-button/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-chart", "type": "registry:ui", "files": [{"path": "../../packages/base-chart/src/AreaChart/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/BarChart/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/chart.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/common/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/Area/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/Bar/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/CartesianGrid/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/ChartBrush/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/ChartLegend/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/ChartLegend/usePageInfo.ts", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/ChartTooltip/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/Line/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/Pie/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/ReferenceLine/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/svg/Direction.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/svg/Triangle.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/XAxis/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/components/YAxis/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/context/chartStateContext.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/context/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/hooks/useChartState.ts", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/hooks/useSize.ts", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/LineChart/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-chart/src/PieChart/index.tsx", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-checkbox", "type": "registry:ui", "files": [{"path": "../../packages/base-checkbox/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-checkbox/src/Root.tsx", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-dialog", "type": "registry:ui", "files": [{"path": "../../packages/base-dialog/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-drawer", "type": "registry:ui", "files": [{"path": "../../packages/base-drawer/src/drawer.tsx", "type": "registry:ui"}, {"path": "../../packages/base-drawer/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-dropdown-menu", "type": "registry:ui", "files": [{"path": "../../packages/base-dropdown-menu/src/dropdownMenu.tsx", "type": "registry:ui"}, {"path": "../../packages/base-dropdown-menu/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-ellipsis", "type": "registry:ui", "files": [{"path": "../../packages/base-ellipsis/src/ellipsis.tsx", "type": "registry:ui"}, {"path": "../../packages/base-ellipsis/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-form", "type": "registry:ui", "files": [{"path": "../../packages/base-form/src/Form.tsx", "type": "registry:ui"}, {"path": "../../packages/base-form/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-message", "type": "registry:ui", "files": [{"path": "../../packages/base-message/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-message/src/Toaster.tsx", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-popconfirm", "type": "registry:ui", "files": [{"path": "../../packages/base-popconfirm/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-popconfirm/src/popconfirm.tsx", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-popover", "type": "registry:ui", "files": [{"path": "../../packages/base-popover/src/context.ts", "type": "registry:ui"}, {"path": "../../packages/base-popover/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-popover/src/popover.tsx", "type": "registry:ui"}, {"path": "../../packages/base-popover/src/types.ts", "type": "registry:ui"}, {"path": "../../packages/base-popover/src/useTrigger.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-radio-group", "type": "registry:ui", "files": [{"path": "../../packages/base-radio-group/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-radio-group/src/Root.tsx", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-scroll-area", "type": "registry:ui", "files": [{"path": "../../packages/base-scroll-area/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-select", "type": "registry:ui", "files": [{"path": "../../packages/base-select/src/hooks/use-controllable-state.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-select/src/popover/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/popover/useTrigger.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/popper/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/popper/Popper.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/collection/Collection.stories.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/collection/Collection.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/collection/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/common/ContentEllipsis/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/common/TagList/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/common/TagList/indexOld.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/ClearIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/DownIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/FileIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/index.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/LoadingIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/SelectCheckIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/SelectMinusIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/SelectNoDataIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/SelectSearchIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/SelectSearchNoDataIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/icon/UpIcon.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/Select.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/utils/Children.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/utils/operate.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/utils/useCacheValue.tsx", "type": "registry:ui"}, {"path": "../../packages/base-select/src/select/utils/useTrigger.tsx", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-steps", "type": "registry:ui", "files": [{"path": "../../packages/base-steps/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-switch", "type": "registry:ui", "files": [{"path": "../../packages/base-switch/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-switch/src/Root.tsx", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-tabs", "type": "registry:ui", "files": [{"path": "../../packages/base-tabs/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-tag", "type": "registry:ui", "files": [{"path": "../../packages/base-tag/src/index.ts", "type": "registry:ui"}, {"path": "../../packages/base-tag/src/Tag.tsx", "type": "registry:ui"}, {"path": "../../packages/base-tag/src/utils/cn.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "base-tooltip", "type": "registry:ui", "files": [{"path": "../../packages/base-tooltip/src/index.ts", "type": "registry:ui"}], "architectureLayer": "Base", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "pro-batch-input", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-batch-input/index.tsx", "type": "registry:ui"}], "registryDependencies": ["pro-button", "pro-poptextarea", "pro-textField"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 3, "instability": 1}}, {"name": "pro-button", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-button/index.tsx", "type": "registry:ui"}], "dependencies": ["ahooks", "lodash-es"], "registryDependencies": ["class-generator", "ui-button", "ui-loading", "ui-tooltip"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 2, "ce": 4, "instability": 0.6666666666666666}, "dependents": ["pro-batch-input", "pro-textField"]}, {"name": "pro-dialog", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-dialog/index.tsx", "type": "registry:ui"}], "registryDependencies": ["class-generator", "i18n", "ui-button", "ui-dialog"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 4, "instability": 1}}, {"name": "pro-drawer", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-drawer/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-drawer/pro-drawer.test-coverage.md", "type": "registry:ui"}], "dependencies": ["@radix-ui/react-use-controllable-state", "@radix-ui/react-visually-hidden"], "registryDependencies": ["class-generator", "i18n", "ui-button", "ui-drawer"], "tags": ["drawer", "modal", "panel", "sheet", "sidebar", "侧边栏", "面板"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 4, "instability": 0.8}, "dependents": ["pro-page-filter"]}, {"name": "pro-dropdown-menu", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-dropdown-menu/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-dropdown-menu/type.ts", "type": "registry:ui"}], "registryDependencies": ["ui-button", "ui-dropdown-menu"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 2, "instability": 1}}, {"name": "pro-ellipsis", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-ellipsis/index.tsx", "type": "registry:ui"}], "registryDependencies": ["class-generator", "pro-tooltip", "ui-ellipsis"], "tags": ["clamp", "ellipsis", "overflow", "truncate", "多行省略", "文本截断", "溢出处理"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 3, "instability": 1}}, {"name": "pro-empty", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-empty/index.tsx", "type": "registry:ui"}], "registryDependencies": ["class-generator", "i18n", "ui-empty"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 3, "instability": 1}}, {"name": "pro-form", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-form/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-form/pro-form.test-coverage.md", "type": "registry:ui"}], "registryDependencies": ["class-generator", "ui-form", "ui-textField"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 3, "instability": 1}}, {"name": "pro-fullscreen-dialog", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-fullscreen-dialog/index.tsx", "type": "registry:ui"}], "dependencies": ["ahooks"], "registryDependencies": ["class-generator", "i18n", "ui-button", "ui-fullscreen-dialog"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 4, "instability": 1}}, {"name": "pro-input-number", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-input-number/index.tsx", "type": "registry:ui"}], "registryDependencies": ["ui-input-number"], "tags": ["input-number", "number-input", "数字输入框", "计数器"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 1, "instability": 1}}, {"name": "pro-page-filter", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-page-filter/Actions.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-page-filter/context.ts", "type": "registry:ui"}, {"path": "registry/default/ui/pro-page-filter/Filter.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-page-filter/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-page-filter/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-page-filter/Item.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-page-filter/useVariants.ts", "type": "registry:ui"}], "dependencies": ["@imd/context", "ahooks"], "registryDependencies": ["pro-drawer", "ui-button", "ui-drawer"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 2, "ce": 3, "instability": 0.6}, "dependents": ["crud", "crud2"]}, {"name": "pro-popconfirm", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-popconfirm/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-popconfirm/use-placement.ts", "type": "registry:ui"}], "dependencies": ["@radix-ui/react-use-controllable-state"], "registryDependencies": ["class-generator", "ui-button", "ui-popconfirm"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 3, "instability": 1}}, {"name": "pro-popover", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-popover/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-popover/use-placement.ts", "type": "registry:ui"}], "registryDependencies": ["class-generator", "ui-popover"], "tags": ["flyout", "popover", "popper", "popup", "气泡卡片"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 2, "instability": 0.6666666666666666}, "dependents": ["pro-tooltip"]}, {"name": "pro-poptextarea", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-poptextarea/index.tsx", "type": "registry:ui"}], "registryDependencies": ["ui-button", "ui-popover", "ui-textarea", "use-translation"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 4, "instability": 0.8}, "dependents": ["pro-batch-input"]}, {"name": "pro-select", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-select/hooks.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-select/hooks/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-select/hooks/useFuseFilter.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-select/hooks/useUniqueSelect.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/pro-select/index.tsx", "type": "registry:ui"}], "dependencies": ["ahooks"], "registryDependencies": ["class-generator", "i18n", "ui-select"], "tags": ["dropdown", "picker", "select", "下拉选择", "选择器"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 3, "instability": 1}}, {"name": "pro-switch", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-switch/index.tsx", "type": "registry:ui"}], "dependencies": ["ahooks"], "registryDependencies": ["class-generator", "ui-switch"], "tags": ["switch", "toggle", "切换"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 2, "instability": 1}}, {"name": "pro-tabs", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-tabs/index.tsx", "type": "registry:ui"}], "registryDependencies": ["class-generator", "ui-tabs"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 2, "instability": 1}}, {"name": "pro-tag", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-tag/index.tsx", "type": "registry:ui"}], "registryDependencies": ["class-generator", "ui-tag"], "tags": ["badge", "chip", "tag", "标记", "胶囊"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 2, "instability": 1}}, {"name": "pro-textarea", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-textarea/index.tsx", "type": "registry:ui"}], "registryDependencies": ["class-generator", "ui-textarea"], "tags": ["form-control", "input", "textarea", "输入框"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 0, "ce": 2, "instability": 1}}, {"name": "pro-textField", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-textField/index.tsx", "type": "registry:ui"}], "registryDependencies": ["pro-button", "ui-textField"], "tags": ["form-control", "input", "password", "search", "textField", "密码框", "搜索框", "输入框"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 2, "instability": 0.6666666666666666}, "dependents": ["pro-batch-input"]}, {"name": "pro-tooltip", "type": "registry:ui", "files": [{"path": "registry/default/ui/pro-tooltip/index.tsx", "type": "registry:ui"}], "registryDependencies": ["class-generator", "pro-popover", "ui-tooltip"], "architectureLayer": "Pro", "architectureMetrics": {"ca": 1, "ce": 3, "instability": 0.75}, "dependents": ["pro-ellipsis"]}, {"name": "ui-alert", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-alert/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-alert/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-alert"], "registryDependencies": ["i18n"], "tags": ["alert", "banner", "message", "notification", "横幅警告", "通知"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 1, "instability": 1}}, {"name": "ui-button", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-button/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-button"], "registryDependencies": ["ui-loading"], "architectureLayer": "UI", "architectureMetrics": {"ca": 10, "ce": 1, "instability": 0.09090909090909091}, "dependents": ["pro-button", "pro-dialog", "pro-drawer", "pro-dropdown-menu", "pro-fullscreen-dialog", "pro-page-filter", "pro-popconfirm", "pro-poptextarea", "ui-dropdown-menu", "ui-popconfirm"]}, {"name": "ui-chart", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-chart/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-chart/Legend.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-chart/Tooltip.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-chart"], "tags": ["bar", "chart", "curve", "donut", "histogram", "line", "pie", "ring", "spline", "扇区图", "曲线图", "条形图", "环形图", "趋势图"], "architectureLayer": "UI", "architectureMetrics": {"ca": 56, "ce": 0, "instability": 0}, "dependents": ["chart-area", "chart-area-base", "chart-area-brush", "chart-area-cross", "chart-area-cross-x", "chart-area-legend", "chart-area-linear", "chart-area-referenceLine", "chart-area-tooltip-direction", "chart-area-xaxis-angle", "chart-area-yaxis", "chart-bar", "chart-bar-auto-width", "chart-bar-base", "chart-bar-interval", "chart-bar-label", "chart-bar-multiple", "chart-bar-multiple2", "chart-bar-stacked", "chart-bar-stacked-bidirectional", "chart-bar-stacked-rate", "chart-bar-vertical", "chart-bar-vertical-interval", "chart-bar-vertical-label", "chart-bar-vertical-multiple", "chart-bar-vertical-multiple-label", "chart-bar-vertical-stack", "chart-bar-vertical-stack-rate", "chart-bar-vertical-stacked-bidirectional", "chart-bar-yaxis", "chart-line", "chart-line-base", "chart-line-brush", "chart-line-brush-color", "chart-line-cross", "chart-line-cross-x", "chart-line-legend", "chart-line-legend-custom", "chart-line-linear", "chart-line-referenceLine", "chart-line-tooltip-direction", "chart-line-xaxis-angle", "chart-line-yaxis", "chart-line-yaxis-type-category", "chart-pie", "chart-pie-base", "chart-pie-donut", "chart-pie-donut-active", "chart-pie-donut-text", "chart-pie-interactive", "chart-pie-label", "chart-pie-label-custom", "chart-pie-label-list", "chart-pie-legend", "chart-pie-separator-none", "chart-pie-stacked"]}, {"name": "ui-checkbox", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-checkbox/checkbox.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-checkbox/group.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-checkbox/hooks.ts", "type": "registry:ui"}, {"path": "registry/default/ui/ui-checkbox/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-checkbox/index.ts", "type": "registry:ui"}], "dependencies": ["@imd/base-checkbox", "@radix-ui/react-use-controllable-state", "@radix-ui/themes"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["ui-select"]}, {"name": "ui-dialog", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-dialog/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-dialog/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-dialog"], "tags": ["dialog", "modal", "overlay", "popup", "对话框", "弹窗", "模态框"], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-dialog", "ui-fullscreen-dialog"]}, {"name": "ui-drawer", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-drawer/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-drawer/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-drawer"], "registryDependencies": ["i18n"], "tags": ["drawer", "modal", "panel", "sheet", "sidebar", "侧边栏", "面板"], "architectureLayer": "UI", "architectureMetrics": {"ca": 4, "ce": 1, "instability": 0.2}, "dependents": ["crud", "crud2", "pro-drawer", "pro-page-filter"]}, {"name": "ui-dropdown-menu", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-dropdown-menu/context.ts", "type": "registry:ui"}, {"path": "registry/default/ui/ui-dropdown-menu/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-dropdown-menu/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-dropdown-menu/type.ts", "type": "registry:ui"}, {"path": "registry/default/ui/ui-dropdown-menu/variants.ts", "type": "registry:ui"}], "dependencies": ["@imd/base-scroll-area", "@radix-ui/react-dropdown-menu", "@radix-ui/react-use-controllable-state", "lucide-react"], "registryDependencies": ["class-generator", "ui-button"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 2, "instability": 0.6666666666666666}, "dependents": ["pro-dropdown-menu"]}, {"name": "ui-ellipsis", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-ellipsis/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-tooltip", "@radix-ui/react-tooltip"], "registryDependencies": ["ui-tooltip"], "tags": ["clamp", "ellipsis", "overflow", "truncate", "多行省略", "文本截断", "溢出处理"], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 1, "instability": 0.3333333333333333}, "dependents": ["pro-ellipsis", "ui-select"]}, {"name": "ui-empty", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-empty/assets/default.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/fail.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/maintain.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/network.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/no-permission.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/no-response.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/no-search-result.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/simple-no-data.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/simple-no-network.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/simple-no-search-result.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/assets/success.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/images.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-empty/index.tsx", "type": "registry:ui"}], "tags": ["empty", "no-data", "placeholder", "无数据", "空状态"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-empty"]}, {"name": "ui-form", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-form/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-form/index.tsx", "type": "registry:ui"}], "dependencies": ["@hookform/resolvers", "@imd/base-form", "@imd/context", "@radix-ui/react-slot", "react-hook-form", "zod"], "registryDependencies": ["ui-label", "ui-tooltip"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 2, "instability": 0.6666666666666666}, "dependents": ["pro-form"]}, {"name": "ui-formField", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-formField/index.tsx", "type": "registry:ui"}], "tags": ["field", "form", "helper-text", "label", "表单字段"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-fullscreen-dialog", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-fullscreen-dialog/index.tsx", "type": "registry:ui"}], "dependencies": ["@radix-ui/react-dialog", "tailwind-variants"], "registryDependencies": ["ui-dialog"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 1, "instability": 0.5}, "dependents": ["pro-fullscreen-dialog"]}, {"name": "ui-hello-world", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-hello-world/constants.ts", "type": "registry:ui"}, {"path": "registry/default/ui/ui-hello-world/index.tsx", "type": "registry:ui"}], "tags": ["demo", "example", "hello-world", "演示", "示例"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-input-number", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-input-number/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-input-number/index.tsx", "type": "registry:ui"}], "dependencies": ["imask"], "tags": ["input-number", "number-input"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-input-number"]}, {"name": "ui-label", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-label/index.tsx", "type": "registry:ui"}], "dependencies": ["@radix-ui/react-label"], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["ui-form", "ui-switch"]}, {"name": "ui-loading", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-loading/index.tsx", "type": "registry:ui"}], "cssVars": {"theme": {"--animate-pulse-dots-loading": "pulse-dots-loading 1s ease-in-out infinite var(--dot-delay)"}}, "css": {"@keyframes pulse-dots-loading": {"0%,100%": {"transform": "scale(0)", "opacity": "0.5"}, "50%": {"transform": "scale(1)", "opacity": "1"}}}, "tags": ["indicator", "loading", "spin", "spinner", "加载中", "旋转", "菊花图"], "architectureLayer": "UI", "architectureMetrics": {"ca": 3, "ce": 0, "instability": 0}, "dependents": ["pro-button", "ui-button", "ui-spin"]}, {"name": "ui-message", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-message/icons.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-message/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-message/styles.ts", "type": "registry:ui"}, {"path": "registry/default/ui/ui-message/types.ts", "type": "registry:ui"}], "dependencies": ["@imd/base-message"], "tags": ["alert", "message", "notification", "snackbar", "toast", "全局消息", "轻提示"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-notification", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-notification/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-notification/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-message"], "tags": ["alert", "message", "notification", "snackbar", "toast", "提醒", "通知"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-popconfirm", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-popconfirm/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-popconfirm/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-popconfirm"], "registryDependencies": ["i18n", "ui-button"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 2, "instability": 0.6666666666666666}, "dependents": ["pro-popconfirm"]}, {"name": "ui-popover", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-popover/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-popover/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-popover"], "tags": ["flyout", "popover", "popper", "popup", "气泡卡片"], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-popover", "pro-poptextarea"]}, {"name": "ui-radio-group", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-radio-group/Icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-radio-group/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-radio-group/RadioCards.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-radio-group/RadioGroup.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-radio-group/type.ts", "type": "registry:ui"}], "dependencies": ["@imd/base-radio-group"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-scroll-area", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-scroll-area/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-scroll-area/scroll-area.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-scroll-area"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-select", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-select/defined.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/ClearIcon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/CollapseIcon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/DownIcon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/ExpandIcon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/ListIcon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/LoadingIcon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/icon/UpIcon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-select/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-scroll-area", "@imd/base-select", "@imd/context", "lucide-react"], "registryDependencies": ["ui-checkbox", "ui-ellipsis", "ui-tooltip"], "tags": ["dropdown", "picker", "select", "下拉选择", "选择器"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 3, "instability": 0.75}, "dependents": ["pro-select"]}, {"name": "ui-skeleton", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-skeleton/index.tsx", "type": "registry:ui"}], "cssVars": {"theme": {"--animate-skeleton": "skeleton 1.5s linear infinite"}}, "css": {"@keyframes skeleton": {"0%": {"background-position": "100% 50%"}, "100%": {"background-position": "0 50%"}}}, "tags": ["placeholder", "skeleton", "占位图", "骨架屏"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-spin", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-spin/index.tsx", "type": "registry:ui"}], "registryDependencies": ["ui-loading"], "tags": ["indicator", "loading", "spin", "spinner", "加载中", "旋转", "菊花图"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 1, "instability": 1}}, {"name": "ui-steps", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-steps/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-steps/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-steps"], "tags": ["progress", "stepper", "steps", "wizard", "向导", "流程"], "architectureLayer": "UI", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "ui-switch", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-switch/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-switch"], "registryDependencies": ["ui-label"], "tags": ["switch", "toggle", "切换"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 1, "instability": 0.5}, "dependents": ["pro-switch"]}, {"name": "ui-tabs", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-tabs/icons.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-tabs/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-tabs"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-tabs"]}, {"name": "ui-tag", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-tag/context.ts", "type": "registry:ui"}, {"path": "registry/default/ui/ui-tag/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-tag/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-tag/style.ts", "type": "registry:ui"}], "dependencies": ["@imd/base-tag"], "tags": ["badge", "chip", "tag", "标记", "胶囊"], "architectureLayer": "UI", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-tag"]}, {"name": "ui-textarea", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-textarea/autoresize-textarea.ts", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textarea/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textarea/index.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textarea/ui-textarea.test-coverage.md", "type": "registry:ui"}], "dependencies": ["@imd/context"], "tags": ["form-control", "input", "textarea", "输入框"], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-poptextarea", "pro-textarea"]}, {"name": "ui-textField", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-textField/icon.tsx", "type": "registry:ui"}, {"path": "registry/default/ui/ui-textField/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/context"], "tags": ["form-control", "input", "textField", "输入框"], "architectureLayer": "UI", "architectureMetrics": {"ca": 2, "ce": 0, "instability": 0}, "dependents": ["pro-form", "pro-textField"]}, {"name": "ui-tooltip", "type": "registry:ui", "files": [{"path": "registry/default/ui/ui-tooltip/index.tsx", "type": "registry:ui"}], "dependencies": ["@imd/base-tooltip"], "architectureLayer": "UI", "architectureMetrics": {"ca": 5, "ce": 0, "instability": 0}, "dependents": ["pro-button", "pro-tooltip", "ui-ellipsis", "ui-form", "ui-select"]}, {"name": "class-generator", "type": "registry:lib", "files": [{"path": "registry/default/lib/class-generator.ts", "type": "registry:lib"}], "description": "用于生成组件类名的工具，支持自定义类名生成器、全局状态类名管理以及生成组件插槽类名。可配置前缀，便于构建一致的CSS类名系统。", "architectureLayer": "lib", "architectureMetrics": {"ca": 16, "ce": 0, "instability": 0}, "dependents": ["pro-button", "pro-dialog", "pro-drawer", "pro-ellipsis", "pro-empty", "pro-form", "pro-fullscreen-dialog", "pro-popconfirm", "pro-popover", "pro-select", "pro-switch", "pro-tabs", "pro-tag", "pro-textarea", "pro-tooltip", "ui-dropdown-menu"]}, {"name": "use-mobile", "type": "registry:hook", "files": [{"path": "registry/default/hooks/use-mobile.tsx", "type": "registry:hook"}], "architectureLayer": "hooks", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "use-translation", "type": "registry:hook", "files": [{"path": "registry/default/hooks/use-translation.ts", "type": "registry:hook"}], "architectureLayer": "hooks", "architectureMetrics": {"ca": 1, "ce": 0, "instability": 0}, "dependents": ["pro-poptextarea"]}, {"name": "crud", "type": "registry:template", "files": [{"path": "crud/index.ts", "type": "registry:asset"}, {"path": "crud/store/page.store.ts", "type": "registry:asset"}, {"path": "crud/table.tsx", "type": "registry:asset"}], "registryDependencies": ["pro-page-filter", "ui-drawer"], "description": "增删改查页面，包含筛选区、列表、详情弹框、编辑弹框、新增弹框等功能", "architectureLayer": "Template", "architectureMetrics": {"ca": 0, "ce": 2, "instability": 1}}, {"name": "crud2", "type": "registry:template", "files": [{"path": "crud2/index.ts", "type": "registry:asset"}, {"path": "crud2/store/page.store.ts", "type": "registry:asset"}, {"path": "crud2/table.tsx", "type": "registry:asset"}], "registryDependencies": ["pro-page-filter", "ui-drawer"], "description": "增删改查页面，包含筛选区、列表、详情弹框、编辑弹框、新增弹框等功能", "architectureLayer": "Template", "architectureMetrics": {"ca": 0, "ce": 2, "instability": 1}}, {"name": "imd", "type": "registry:template", "files": [{"path": "../../packages/design-system/src/css/chart.css", "type": "registry:template"}, {"path": "../../packages/design-system/src/css/typography.css", "type": "registry:template"}, {"path": "../../packages/design-system/src/css/utilities.css", "type": "registry:template"}, {"path": "../../packages/design-system/src/themes/imd.css", "type": "registry:template"}], "description": "imd 组件库的默认 tailwindcss 主题", "tags": ["default theme", "theme"], "architectureLayer": "unknown", "architectureMetrics": {"ca": 0, "ce": 0, "instability": 0}}, {"name": "i18n", "type": "registry:block", "files": [{"path": "registry/default/hooks/use-translation.ts", "type": "registry:hook"}], "architectureLayer": "hooks", "architectureMetrics": {"ca": 8, "ce": 0, "instability": 0}, "dependents": ["pro-dialog", "pro-drawer", "pro-empty", "pro-fullscreen-dialog", "pro-select", "ui-alert", "ui-drawer", "ui-popconfirm"]}]