---
title: Message 消息
description: 一种轻量级的方式来向用户展示全局通知或反馈信息。
component: true
links:
  doc: https://www.npmjs.com/package/sonner
---

基于 [sonner](https://www.npmjs.com/package/sonner) 构建，支持多种消息类型、自定义位置、持续时间控制以及消息队列管理，适用于需要即时反馈的交互场景。

<ExamplePreview
  showSource="false"
  componentName="base-message"
  demoName="default-demo"
  description="基础消息提示示例，展示了最常用的消息类型和交互方式"
/>

## 安装

### CLI

```imd-add
npx imd add base-message
```

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```package-install
npm install @imd/base-message
```

</Step>
</Steps>

:::

## 用法

```tsx
import { Toaster, toast } from '@imd/base-message'
```

```tsx
toast('这是一条消息')
```

:::warn

此组件需要在应用根组件添加 Toaster 组件：

`<Toaster />`

:::

## 示例

### 消息类型

不同类型的消息用于不同的场景反馈，包括成功、错误、警告、加载等状态。

showSource="false"

<ExamplePreview
  componentName="base-message"
  demoName="types-demo"
  description="展示了不同类型的消息样式和用法"
/>

### 消息位置

Message 组件支持六种不同的显示位置，可以根据应用布局选择最合适的位置。

```tsx
// 支持的位置选项：
// top-left | top-center | top-right | bottom-left | bottom-center | bottom-right
<Toaster position="top-left" />
```

### 消息堆叠

showSource="false"
当同时触发多条消息时，可以选择不同的堆叠展示方式。

<ExamplePreview
  componentName="base-message"
  demoName="expand-demo"
  description="展示了消息堆叠时的交互效果"
/>

## API

### Message

Message 组件的主要配置选项。

<PropsTable
  data={[
    {
      name: 'message',
      type: 'ReactNode',
      description: '消息内容，支持文本或 React 节点',
      required: true,
    },
    {
      name: 'description',
      type: 'ReactNode',
      description: '消息的详细描述，用于补充说明主要内容',
    },
    {
      name: 'duration',
      type: 'number',
      default: '4000',
      description: '消息自动关闭的延时时间，单位为毫秒',
    },
    {
      name: 'position',
      type: "'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'",
      default: "'bottom-right'",
      description: '消息弹出的位置，支持六个不同方位',
    },
    {
      name: 'closeButton',
      type: 'boolean',
      default: 'false',
      description: '是否显示关闭按钮，用于手动关闭消息',
    },
    {
      name: 'invert',
      type: 'boolean',
      default: 'false',
      description: '是否反转消息的颜色主题',
    },
    {
      name: 'dismissible',
      type: 'boolean',
      default: 'true',
      description: '消息是否可以通过点击或滑动手动关闭',
    },
    {
      name: 'icon',
      type: 'ReactNode',
      description: '自定义消息图标，可替换默认的状态图标',
    },
    {
      name: 'action',
      type: '{ label: string; onClick: () => void } | ReactNode',
      description: '配置消息的操作按钮，支持文本配置或自定义节点',
    },
    {
      name: 'cancel',
      type: '{ label: string; onClick: () => void } | ReactNode',
      description: '配置消息的取消按钮，支持文本配置或自定义节点',
    },
    {
      name: 'onDismiss',
      type: '() => void',
      description: '消息被手动关闭时的回调函数',
    },
    {
      name: 'onAutoClose',
      type: '() => void',
      description: '消息自动关闭时的回调函数',
    },
    {
      name: 'unstyled',
      type: 'boolean',
      default: 'false',
      description: '是否使用无样式模式，用于完全自定义消息样式',
    },
  ]}
/>

### 工具方法

Message 组件提供了一系列便捷的工具方法：

- `toast(message: ReactNode, options?: ToastOptions)` - 显示默认消息
- `toast.success(message: ReactNode, options?: ToastOptions)` - 显示成功消息
- `toast.error(message: ReactNode, options?: ToastOptions)` - 显示错误消息
- `toast.info(message: ReactNode, options?: ToastOptions)` - 显示信息消息
- `toast.warning(message: ReactNode, options?: ToastOptions)` - 显示警告消息
- `toast.loading(message: ReactNode, options?: ToastOptions)` - 显示加载消息
- `toast.custom((t) => ReactNode, options?: ToastOptions)` - 显示自定义消息
- `toast.promise(promise: Promise, options: PromiseOptions)` - 显示异步操作消息
- `toast.dismiss(toastId?: string)` - 关闭指定或所有消息

### Promise 配置选项

用于异步操作消息的配置接口：

```tsx
interface PromiseOptions {
  loading: string | { title?: string; description?: string }
  success:
    | string
    | ((data: any) => string | { title?: string; description?: string })
  error:
    | string
    | ((error: any) => string | { title?: string; description?: string })
}
```
