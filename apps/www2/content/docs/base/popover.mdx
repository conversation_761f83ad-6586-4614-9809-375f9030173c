---
title: Popover 弹出框
description: 在触发元素周围显示临时性的上下文内容, 基于 Radix 的 Popover。
component: true
links:
  doc: https://www.radix-ui.com/docs/primitives/components/popover
---

<ExamplePreview
  showSource="false"
  componentName="base-popover"
  demoName="default-demo"
  description="基础的弹出框示例"
/>

## 安装

### CLI

```imd-add
npx imd add base-popover
```

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```package-install
npm install @imd/base-popover
```

</Step>
</Steps>

## 用法

```tsx
import { Popover, PopoverContent, PopoverTrigger } from '@imd/base-popover'
```

```tsx
<Popover>
  <PopoverTrigger>Open</PopoverTrigger>
  <PopoverContent>Place content for the popover here.</PopoverContent>
</Popover>
```

## 示例

### 带箭头指示器

带有箭头指示器的弹出框，可以更清晰地指示弹出框与触发元素的关联关系。

showSource="false"

<ExamplePreview
  componentName="base-popover"
  demoName="arrow-demo"
  description="带箭头指示器的弹出框示例"
/>

### 触发方式

showSource="false"
展示不同的触发方式（点击、悬停、右键等）如何激活弹出框。

<ExamplePreview
  componentName="base-popover"
  demoName="action-demo"
  description="不同触发方式的弹出框示例"
/>

### 弹出位置

showSource="false"

展示弹出框在不同位置（上、下、左、右）的显示效果。

<ExamplePreview
  componentName="base-popover"
  demoName="position-demo"
  description="不同弹出位置的示例"
/>
showSource="false"

### 受控与非受控

展示如何通过状态控制弹出框的显示和隐藏。

<ExamplePreview
  componentName="base-popover"
  demoName="controlled-demo"
  description="受控与非受控的弹出框示例"
/>

## API

### Popover.Root

弹出框的根组件，作为所有弹出框相关组件的容器。

<PropsTable
  data={[
    {
      name: 'open',
      type: 'boolean',
      default: 'false',
      description: '控制弹出框的打开状态',
    },
    {
      name: 'defaultOpen',
      type: 'boolean',
      default: 'false',
      description: '控制弹出框的默认打开状态',
    },
    {
      name: 'onOpenChange',
      typeSimple: 'function',
      type: '(open: boolean) => void',
      description: '打开状态改变时的回调函数',
    },
    {
      name: 'modal',
      type: 'boolean',
      default: 'false',
      description:
        '弹出框的模式。当设置为 true 时，与外部元素的交互将被禁用，只有弹出框内容对屏幕阅读器可见。',
    },
    {
      name: 'action',
      type: "'hover' | 'click' | 'contextMenu' | 'focus' | ('hover' | 'click' | 'contextMenu' | 'focus')[]",
      typeSimple: 'enum || enum[]',
      default: '"hover"',
      description: '触发事件。',
    },
    {
      name: 'delay',
      type: 'number',
      default: '100',
      description: '触发延时（ms）。',
    },
  ]}
/>

### Popover.Trigger

触发弹出框的按钮。默认情况下，`Popover.Content` 会相对于触发器定位。

<PropsTable
  data={[
    {
      name: 'open',
      type: 'boolean',
      default: 'false',
      description: '控制popover的打开状态',
    },
    {
      name: 'defaultOpen',
      type: 'boolean',
      default: 'false',
      description: '控制popover的默认打开状态',
    },
    {
      name: 'onOpenChange',
      typeSimple: 'function',
      type: '(open: boolean) => void',
      description: '打开状态改变时的回调函数',
      default: '-',
    },
  ]}
/>

### Popover.Anchor

用于定位 `Popover.Content` 的可选元素。如果未使用部分，则内容将位于 `Popover.Trigger` 旁边。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
  ]}
/>

### Popover.Portal

当使用时，将遮罩层和内容部分传送到 `body` 中

<PropsTable
  data={[
    {
      name: 'forceMount',
      type: 'boolean',
      description: (
        <span>
          用于在需要更多控制时的强制渲染。在使用 React 动画库控制动画时很有用。
          如果在此部分使用，它将由 `Popover.Content` 继承。
        </span>
      ),
    },
    {
      name: 'container',
      type: 'HTMLElement',
      default: 'document.body',
      description: '指定要将内容传送到的容器元素',
    },
  ]}
/>

### Popover.Content

弹出框的内容容器。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
    {
      name: 'onOpenAutoFocus',
      type: '(event: Event) => void',
      typeSimple: 'function',
      description: (
        <span>
          打卡后自动聚焦`Content`的处理函数。可以通过调用 `event.preventDefault`
          来阻止。
        </span>
      ),
    },
    {
      name: 'onCloseAutoFocus',
      type: '(event: Event) => void',
      typeSimple: 'function',
      description: (
        <span>
          关闭后自动聚焦`Trigger`的处理函数。可以通过调用 `event.preventDefault`
          来阻止。
        </span>
      ),
    },
    {
      name: 'onEscapeKeyDown',
      type: '(event: KeyboardEvent) => void',
      typeSimple: 'function',
      description: (
        <span>
          触发`Escape`键盘事件的处理函数。可以通过调用 `event.preventDefault`
          来阻止。
        </span>
      ),
    },
    {
      name: 'onPointerDownOutside',
      type: '(event: PointerDownOutsideEvent) => void',
      typeSimple: 'function',
      description: (
        <span>
          内容边界外指针点击事件的处理函数。可以通过调用 `event.preventDefault`
          来阻止。
        </span>
      ),
    },
    {
      name: 'onFocusOutside',
      type: '(event: FocusOutsideEvent) => void',
      typeSimple: 'function',
      description: (
        <span>
          内容边界外元素聚焦的处理函数。可以通过调用 `event.preventDefault`
          来阻止。
        </span>
      ),
    },
    {
      name: 'onInteractOutside',
      type: '(event: PointerDownOutsideEvent | FocusOutsideEvent) => void',
      typeSimple: 'function',
      description: (
        <span>
          当交互（指针或焦点事件）发生在组件边界之外时调用的事件处理程序。可以通过调用
          'event.preventDefault' 来阻止它。
        </span>
      ),
    },
    {
      name: 'forceMount',
      type: 'boolean',
      description: (
        <span>
          用于在需要更多控制时强制安装。在使用 React
          动画库控制动画时很有用。它继承自 `Popover.Portal`。
        </span>
      ),
    },
    {
      name: 'side',
      type: '"top" | "right" | "bottom" | "left"',
      typeSimple: 'enum',
      default: '"bottom"',
      description: (
        <span>
          打开时要呈现的锚点的首选一侧。当发生冲突并启用 `avoidCollisions`
          时，将反转
        </span>
      ),
    },
    {
      name: 'sideOffset',
      type: 'number',
      default: '0',
      description: '与触发器的距离(px)',
    },
    {
      name: 'align',
      type: '"start" | "center" | "end"',
      typeSimple: 'enum',
      default: '"center"',
      description: (
        <span>相对于锚点的对齐方式。发生冲突时可能会发生变化。</span>
      ),
    },
    {
      name: 'alignOffset',
      type: 'number',
      default: '0',
      description: (
        <span>与 '“start”' 或 '“end”' 对齐选项的偏移量（以像素为单位）</span>
      ),
    },
    {
      name: 'avoidCollisions',
      type: 'boolean',
      default: 'true',
      description: (
        <span>
          当 'true' 时，覆盖 `side` 和 `align` 以防止与边界边发生碰撞。
        </span>
      ),
    },
    {
      name: 'collisionBoundary',
      type: 'Element | null | Array<Element | null>',
      typeSimple: 'Boundary',
      default: '[]',
      description: '用作碰撞检测边界的元素',
    },
    {
      name: 'collisionPadding',
      type: 'number | Partial<Record<Side, number>>',
      typeSimple: 'number | Padding',
      default: '0',
      description: (
        <span>
          与应进行碰撞检测的边界边缘的距离（px）。接受一个数字（所有边都相同）或部分填充对象，Example:
          '{'{ top： 20， left： 20 }'}'。
        </span>
      ),
    },
    {
      name: 'arrowPadding',
      type: 'number',
      default: '0',
      description: (
        <span>
          箭头和内容边缘之间的padding。如果你的内容有
          `border-radius`，这将防止它溢出角落
        </span>
      ),
    },
    {
      name: 'sticky',
      type: '"partial" | "always"',
      typeSimple: 'enum',
      default: '"partial"',
      description: (
        <span>
          对齐轴上的粘滞行为。当设置'“partial”'时，只要触发器至少部分位于边界中,就会将内容保留在边界中，而
          '“always”' 将无论如何将内容保留在边界中。
        </span>
      ),
    },
    {
      name: 'hideWhenDetached',
      type: 'boolean',
      default: 'false',
      description: '当触发器完全遮挡时是否隐藏内容',
    },
  ]}
/>

### Popover.Arrow

可选的箭头元素，用于视觉上连接弹出框和触发器。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
    {
      name: 'width',
      type: 'number',
      default: '10',
      description: '箭头的宽度',
    },
    {
      name: 'height',
      type: 'number',
      default: '5',
      description: '箭头的高度',
    },
  ]}
/>

### Popover.Close

关闭弹出框的按钮组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
  ]}
/>
