---
title: Tabs 标签页
description: 基于 Radix 的标签页组件，用于在同一区域内组织和切换不同内容视图
links:
  doc: https://www.radix-ui.com/docs/primitives/components/tabs
  api: https://www.radix-ui.com/docs/primitives/components/tabs#api-reference
---

Tabs 组件允许用户在不同视图之间切换，每次只显示一个视图，是组织内容和提高界面导航效率的理想解决方案。基于 Radix UI 构建，提供完全的可访问性和灵活的样式选项。

<ExamplePreview
  componentName="base-tabs"
  demoName="base-demo"
  description="基础标签页示例"
/>

## 安装

### CLI

```bash
npx imd@latest add base-tabs
```

### 手动

<Steps>

<Step>
<h4>安装以下依赖:</h4>

```bash
npm install @imd/base-tabs
```

</Step>
</Steps>

## 用法

```tsx
import * as Tabs from '@imd/base-tabs'
```

```tsx
<Tabs.Root defaultValue="tabs1">
  <Tabs.List>
    <Tabs.Trigger value="tabs1">标签 1</Tabs.Trigger>
    <Tabs.Trigger value="tabs2">标签 2</Tabs.Trigger>
    <Tabs.Trigger value="tabs3">标签 3</Tabs.Trigger>
  </Tabs.List>
  <Tabs.Content value="tabs1">标签内容 1</Tabs.Content>
  <Tabs.Content value="tabs2">标签内容 2</Tabs.Content>
  <Tabs.Content value="tabs3">标签内容 3</Tabs.Content>
</Tabs.Root>
```

## API

### Tabs

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于组件根元素的自定义 CSS 类',
    },
    {
      name: 'defaultValue',
      type: 'string',
      description: '默认选中的选项卡值',
    },
    {
      name: 'value',
      type: 'string',
      description: '当前选中的选项卡值（受控组件使用）',
    },
    {
      name: 'onValueChange',
      type: '(value: string) => void',
      typeSimple: 'function',
      description: '选项卡值变化时的回调函数',
    },
    {
      name: 'orientation',
      type: '"horizontal" | "vertical" | undefined',
      typeSimple: 'enum',
      default: 'horizontal',
      description: '组件的排列方向',
    },
    {
      name: 'dir',
      type: '"ltr" | "rtl"',
      typeSimple: 'enum',
      description:
        '标签的读取方向。如果省略，则全局继承自 DirectionProvider 或采用 LTR（从左到右）读取模式',
    },
  ]}
/>

### List

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于标签列表容器的自定义 CSS 类',
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
    {
      name: 'loop',
      type: 'boolean',
      description:
        '如果为 true，键盘导航将从最后一个选项卡循环到第一个选项卡，反之亦然',
    },
  ]}
/>

### Trigger

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于标签触发器的自定义 CSS 类',
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
    {
      name: 'value',
      type: 'string',
      description: '标签的唯一值标识',
      required: true,
    },
  ]}
/>

### Content

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于内容区域的自定义 CSS 类',
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
    {
      name: 'value',
      type: 'string',
      description: '对应的标签值，与 TabsTrigger 的 value 相匹配',
      required: true,
    },
    {
      name: 'forceMount',
      type: 'boolean',
      description: '如果为 true，则强制渲染内容，即使其未处于活动状态',
    },
  ]}
/>
