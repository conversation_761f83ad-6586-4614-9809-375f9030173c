---
title: Select 下拉框
description: 一个灵活的下拉选择组件，允许用户从预定义的选项列表中进行单选或多选。
component: true
featured: true
---

一个高度可定制的选择组件，基于 Radix UI 构建，支持多种交互模式，包括单选、多选、搜索、虚拟滚动等功能。适用于各种数据选择场景，提供了丰富的自定义选项。

<ExamplePreview
  componentName="base-select"
  demoName="select-demo"
  description="基础的下拉选择组件，展示了默认的选择行为。"
/>

## 安装

### CLI

```bash
npx imd@latest add base-select
```

### 手动

<Steps>

<Step>安装以下依赖项：</Step>

```bash
npm install @imd/base-select
```

</Steps>

## 用法

```tsx
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectIcon,
  SelectItem,
  SelectItemText,
  SelectLabel,
  SelectLeave,
  SelectTrigger,
  SelectValue,
  SelectViewport,
} from '@/components/base/select'
```

```tsx
<Select action={['hover']}>
  <SelectLeave>
    <SelectTrigger className="w-[180px]">
      <SelectValue placeholder="Select a fruit" />
      <SelectIcon />
    </SelectTrigger>
    <SelectContent>
      <SelectViewport>
        <SelectGroup>
          <SelectLabel>Fruits</SelectLabel>
          <SelectItem value="apple">
            <SelectItemText>Apple</SelectItemText>
          </SelectItem>
          <SelectItem value="banana">
            <SelectItemText>Banana</SelectItemText>
          </SelectItem>
          <SelectItem value="orange">
            <SelectItemText>Orange</SelectItemText>
          </SelectItem>
        </SelectGroup>
      </SelectViewport>
    </SelectContent>
  </SelectLeave>
</Select>
```

## 示例

### 可滚动列表

<ExamplePreview
  componentName="base-select"
  demoName="select-scrollable-demo"
  description="带有可滚动选项列表的选择组件，适用于大量选项的场景。"
/>

### 表单集成

<ExamplePreview
  componentName="base-select"
  demoName="select-form-demo"
  description="与表单组件集成的示例，展示了表单验证和提交过程中的使用方式。"
/>

### 搜索功能

<ExamplePreview
  componentName="base-select"
  demoName="select-search-demo"
  description="带有搜索过滤功能的选择组件，方便从大量选项中快速查找。"
/>

### 多选模式

<ExamplePreview
  componentName="base-select"
  demoName="select-multi-demo"
  description="支持多选的选择组件，用户可以同时选择多个选项。"
/>

### 自定义内容样式

<ExamplePreview
  componentName="base-select"
  demoName="select-content-demo"
  description="支持自定义下拉内容"
/>

## API

### Select

<PropsTable
  data={[
    {
      name: 'value',
      type: 'string | string[] | { value: string, label: string } | { value: string, label: string }[]',
      description: '选择器的当前值，可以是单选值或多选值数组',
    },
    {
      name: 'defaultValue',
      type: 'string | string[] | { value: string, label: string } | { value: string, label: string }[]',
      description: '选择器的默认值，可以是单选值或多选值数组',
    },
    {
      name: 'onChange',
      type: '(value: string | string[] | { value: string, label: string } | { value: string, label: string }[]) => void',
      typeSimple: 'function',
      description: '值变化时的回调函数',
    },
    {
      name: 'labelInValue',
      type: 'boolean',
      default: 'false',
      description:
        '是否在值中包含标签信息，启用后值的格式为 { value: string, label: string }',
    },
    {
      name: 'open',
      type: 'boolean',
      description: '控制下拉菜单是否打开',
    },
    {
      name: 'defaultOpen',
      type: 'boolean',
      description: '下拉菜单的默认打开状态',
    },
    {
      name: 'onOpenChange',
      type: '(open: boolean, type?: any) => void',
      typeSimple: 'function',
      description: '下拉菜单打开状态变化时的回调函数',
    },
    {
      name: 'dir',
      type: '"ltr" | "rtl"',
      default: 'ltr',
      description: '文本方向',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: '是否禁用选择器',
    },
    {
      name: 'required',
      type: 'boolean',
      default: 'false',
      description: '是否为必填项',
    },
    {
      name: 'loading',
      type: 'boolean',
      default: 'false',
      description: '是否显示加载状态',
    },
    {
      name: 'allowClear',
      type: 'boolean',
      default: 'false',
      description: '是否允许清除已选项',
    },
    {
      name: 'dropdownMatchSelectWidth',
      type: 'boolean',
      default: 'true',
      description: '下拉菜单的宽度是否与选择器宽度匹配',
    },
    {
      name: 'removeScroll',
      type: 'boolean',
      default: 'false',
      description: '是否在打开下拉菜单时禁用页面滚动',
    },
    {
      name: 'action',
      type: 'TriggerAction | TriggerAction[]',
      default: 'click',
      description: '触发下拉菜单打开的动作方式，可以是点击或悬停',
    },
    {
      name: 'name',
      type: 'string',
      description: '表单元素的名称',
    },
    {
      name: 'form',
      type: 'string',
      description: '关联的表单 ID',
    },
    {
      name: 'autoComplete',
      type: 'string',
      description: '自动完成属性',
    },
  ]}
/>

### SelectTrigger

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于触发器元素的自定义 CSS 类',
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.button>',
      description: '继承 button 元素的所有 HTML 属性，如 onClick、onFocus 等',
    },
  ]}
/>

### SelectLeave

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于组件容器的自定义 CSS 类',
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.button>',
      description: '继承 button 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectValue

<PropsTable
  data={[
    {
      name: 'placeholder',
      type: 'React.ReactNode',
      description: '未选择任何值时显示的占位符内容',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于值容器的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.span>',
      description: '继承 span 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectMultiValue

<PropsTable
  data={[
    {
      name: 'placeholder',
      type: 'React.ReactNode',
      description: '未选择任何值时显示的占位符内容',
    },
    {
      name: 'tagClassName',
      type: 'string',
      description: '应用于多选标签的自定义 CSS 类',
    },
    {
      name: 'hideCountTooltips',
      type: 'boolean',
      default: 'false',
      description: '是否隐藏标签数量的提示',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于值容器的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.span>',
      description: '继承 span 元素的所有 HTML 属性',
    },
  ]}
/>

#### TagList 多标签单行省略

```tsx
<SelectPrimitive.TagListRoot value={shortList}>
  <SelectPrimitive.TagListItem>
    <RenderTag /> {/* 自定义渲染内容 */}
  </SelectPrimitive.TagListItem>
  <SelectPrimitive.TagListCount>
    <RenderCount /> {/* 自定义渲染内容 */}
  </SelectPrimitive.TagListCount>
</SelectPrimitive.TagListRoot>
```

#### TagListRoot

<PropsTable
  data={[
    {
      name: 'children',
      type: 'React.ReactNode',
      description:
        'children的displayName需要是"TagListItem"或"TagListCount"，分别渲染为TagItem和TagCount',
    },
    {
      name: 'value',
      type: 'any[]',
      description: '',
    },
    {
      name: 'onItemClose',
      type: '(item: any, index: number) => void',
      description: '点击tagItem的删除回调',
    },
    {
      name: 'showAll',
      type: 'boolean',
      default: 'false',
      description: '是否展示全部，不需要超出省略',
    },
    {
      name: 'hideCountTooltips',
      type: 'boolean',
      default: 'false',
      description: '是否展示count的tooltips',
    },
  ]}
/>

#### TagListItem

TagList中渲染的TagItem
可自定义渲染内容

#### TagListCount

TagList中渲染的溢出省略TagCount
可自定义渲染内容

### SelectIcon

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于图标的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectContent

<PropsTable
  data={[
    {
      name: 'position',
      type: '"popper"',
      default: 'popper',
      description: '内容定位策略',
    },
    {
      name: 'onCloseAutoFocus',
      type: 'FocusScopeProps["onUnmountAutoFocus"]',
      typeSimple: 'function',
      description: '关闭时自动聚焦的处理函数',
    },
    {
      name: 'onEscapeKeyDown',
      type: 'DismissableLayerProps["onEscapeKeyDown"]',
      typeSimple: 'function',
      description: 'Escape 键按下时的处理函数',
    },
    {
      name: 'onPointerDownOutside',
      type: 'DismissableLayerProps["onPointerDownOutside"]',
      typeSimple: 'function',
      description: '点击外部区域时的处理函数',
    },
    {
      name: 'searchValue',
      type: 'string',
      description: '搜索框的值',
    },
    {
      name: 'onSearchValueChange',
      type: '(s: string) => void',
      typeSimple: 'function',
      description: '搜索值变化时的回调函数',
    },
    {
      name: 'filterOption',
      type: 'boolean | ((searchValue: string, label: string) => boolean)',
      typeSimple: 'boolean | function',
      defaultValue: 'true',
      description: '选项过滤函数',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于内容容器的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'PopperContentProps',
      description: '继承 Popper Content 组件的所有属性',
    },
  ]}
/>

### SelectSearch

<PropsTable
  data={[
    {
      name: 'onSearch',
      type: '(value: string) => void',
      typeSimple: 'function',
      description: '搜索值变化时的回调函数',
    },
    {
      name: 'placeholder',
      type: 'string',
      description: '搜索框的占位符文本',
    },
    {
      name: 'needSearch',
      type: 'boolean',
      default: 'false',
      description: '是否启用搜索功能',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于搜索容器的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectViewport

<PropsTable
  data={[
    {
      name: 'nonce',
      type: 'string',
      description: '用于内联样式的 nonce 值',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于视口的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectGroup

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于组的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectLabel

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于标签的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectItem

<PropsTable
  data={[
    {
      name: 'value',
      type: 'string',
      description: '选项的值',
      required: true,
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: '是否禁用该选项',
    },
    {
      name: 'textValue',
      type: 'string',
      description: '选项的文本值，用于搜索匹配',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于选项的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectItemText

<PropsTable
  data={[
    {
      name: 'highLightClassName',
      type: 'string',
      description: '匹配搜索文本时应用的高亮样式类',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于文本的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.span>',
      description: '继承 span 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectMultiItem

<PropsTable
  data={[
    {
      name: 'value',
      type: 'string',
      description: '多选项的值',
      required: true,
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: '是否禁用该多选项',
    },
    {
      name: 'textValue',
      type: 'string',
      description: '多选项的文本值，用于显示和搜索匹配',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于多选项的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectMultiAllItem

<PropsTable
  data={[
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: '是否禁用全选选项',
    },
    {
      name: 'textValue',
      type: 'string',
      description: '全选选项的文本值',
    },
    {
      name: 'indicatorRender',
      type: '(checked: boolean | "indeterminate") => React.ReactNode',
      typeSimple: 'function',
      description: '自定义渲染选中指示器的函数',
    },
    {
      name: 'checkboxClassName',
      type: 'string',
      description: '应用于复选框的自定义 CSS 类',
    },
    {
      name: 'className',
      type: 'string',
      description: '应用于全选选项的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectNoData

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于无数据提示的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>

### SelectLoading

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于加载状态的自定义 CSS 类',
    },
    {
      name: '...rest',
      type: 'React.ComponentPropsWithoutRef<typeof Primitive.div>',
      description: '继承 div 元素的所有 HTML 属性',
    },
  ]}
/>
