---
title: Tooltip 工具提示
description: 用于在用户悬停在元素上时显示简短的提示信息。
---

<ExamplePreview
  showSource="false"
  componentName="base-tooltip"
  demoName="default-demo"
  description="一个气泡组件。"
/>

## 安装

### CLI

```imd-add
npx imd add base-tooltip
```

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```package-install
npm install @imd/base-tooltip
```

</Step>
</Steps>

## 用法

```tsx
import * as Tooltip from '@imd/base-tooltip'
```

```tsx
<Tooltip.Provider>
  <Tooltip.Root>
    <Tooltip.Trigger>
      <Button variant="outline">Hover</Button>
    </Tooltip.Trigger>
    <Tooltip.Content>
      <p>Add to library</p>
    </Tooltip.Content>
  </Tooltip.Root>
</Tooltip.Provider>
```

## 示例

### 全局配置

使用 Provider 组件可以全局控制所有 Tooltip 的延迟时间和跳过延迟时间。

showSource="false"

<ExamplePreview
  componentName="base-tooltip"
  demoName="provider-demo"
  description="tooltip provider."
/>

### 箭头指示器

showSource="false"
添加箭头指示器可以更清晰地指示 Tooltip 的指向目标。

<ExamplePreview
  componentName="base-tooltip"
  demoName="arrow-demo"
  description="tooltip arrow"
/>

### 弹出位置

showSource="false"

Tooltip 支持多个弹出位置，可以根据需要进行调整。

<ExamplePreview
  componentName="base-tooltip"
  demoName="position-demo"
  description="tooltip 弹出位置"
/>

## API

### Tooltip.Root

Tooltip 的根容器组件。

<PropsTable
  data={[
    {
      name: 'defaultOpen',
      type: 'boolean',
      description: '默认打开状态。在不需要控制其打开状态时使用。',
    },
    {
      name: 'open',
      type: 'boolean',
      description: '工具提示的受控打开状态。与 `onOpenChange` 结合使用',
    },
    {
      name: 'onOpenChange',
      type: '(open: boolean) => void',
      typeSimple: 'function',
      description: 'open状态变更的处理函数',
    },
    {
      name: 'delayDuration',
      type: 'number',
      default: 700,
      description:
        '从指针进入trigger到弹出层打开的持续时间，会覆盖`Provider`的配置',
    },
    {
      name: 'disableHoverableContent',
      type: 'boolean',
      default: false,
      description:
        '控制鼠标悬停 `Tooltip.Content` 的行为。会覆盖`Provider`的配置',
    },
  ]}
/>

### Tooltip.Provider

全局配置容器组件，使用 Tooltip 组件时需要将 `Tooltip.Provider` 包裹在应用根组件中。

<PropsTable
  data={[
    {
      name: 'delayDuration',
      type: 'number',
      default: 700,
      description: '从指针进入trigger到弹出层打开的持续时间',
    },
    {
      name: 'skipDelayDuration',
      type: 'number',
      default: 300,
      description:
        '在设置的时间内再次进入trigger，会跳过delayDuration的等待时间',
    },
    {
      name: 'disableHoverableContent',
      type: 'boolean',
      description: '控制鼠标悬停 `Tooltip.Content` 的行为。',
    },
  ]}
/>

### Tooltip.Trigger

触发 Tooltip 显示的元素。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
  ]}
/>

### Tooltip.Portal

用于将 Tooltip 内容传送到 body 中的组件。

<PropsTable
  data={[
    {
      name: 'forceMount',
      type: 'boolean',
      description: (
        <span>
          用于在需要更多控制时的强制渲染。在使用 React 动画库控制动画时很有用。
          如果在此部分使用，它将由 `Popover.Content` 继承。
        </span>
      ),
    },
    {
      name: 'container',
      type: 'HTMLElement',
      default: 'document.body',
      description: '指定要将内容传送到的容器元素。',
    },
  ]}
/>

### Tooltip.Content

Tooltip 的内容组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
    {
      name: 'aria-label',
      type: 'string',
      description:
        '默认情况下，屏幕阅读器将读出组件内的内容。如果这还不够描述，或者你有无法声明的内容，请使用 aria-label 作为更具描述性的标签。',
    },
    {
      name: 'onEscapeKeyDown',
      type: '(event: KeyboardEvent) => void',
      typeSimple: 'function',
      description:
        '触发Escape键盘事件的处理函数。可以通过调用 event.preventDefault 来阻止。',
    },
    {
      name: 'onPointerDownOutside',
      type: '(event: PointerDownOutsideEvent) => void',
      typeSimple: 'function',
      description:
        '内容边界外指针点击事件的处理函数。可以通过调用 event.preventDefault 来阻止。',
    },
    {
      name: 'forceMount',
      type: 'boolean',
      description: (
        <span>
          用于在需要更多控制时强制安装。在使用 React
          动画库控制动画时很有用。它继承自 `Popover.Portal`。
        </span>
      ),
    },
    {
      name: 'side',
      type: '"top" | "right" | "bottom" | "left"',
      typeSimple: 'enum',
      default: '"top"',
      description:
        '打开时要呈现的锚点的首选一侧。当发生冲突并启用 avoidCollisions 时，将反转',
    },
    {
      name: 'sideOffset',
      type: 'number',
      default: '0',
      description: '与触发器的距离(px)。',
    },
    {
      name: 'align',
      type: '"start" | "center" | "end"',
      typeSimple: 'enum',
      default: '"center"',
      description: '相对于锚点的对齐方式。发生冲突时可能会发生变化。',
    },
    {
      name: 'alignOffset',
      type: 'number',
      default: '0',
      description: '与 "start" 或 "end" 对齐选项的偏移量（以像素为单位）',
    },
    {
      name: 'avoidCollisions',
      type: 'boolean',
      default: 'true',
      description: '当 true 时，覆盖 side 和 align 以防止与边界边发生碰撞。',
    },
    {
      name: 'collisionBoundary',
      type: 'Element | null | Array<Element | null>',
      typeSimple: 'Boundary',
      default: '[]',
      description: '用作碰撞边界的元素。默认情况下为viewport。',
    },
    {
      name: 'collisionPadding',
      type: 'number | Partial<Record<Side, number>>',
      typeSimple: 'number | Padding',
      default: '0',
      description: (
        <span>
          与应进行碰撞检测的边界边缘的距离（px）。接受一个数字（所有边都相同）或部分填充对象，Example:
          '{'{ top： 20， left： 20 }'}'。
        </span>
      ),
    },
    {
      name: 'arrowPadding',
      type: 'number',
      default: '0',
      description:
        '箭头和内容边缘之间的padding。如果你的内容有 border-radius，这将防止它溢出角落',
    },
    {
      name: 'sticky',
      type: '"partial" | "always"',
      typeSimple: 'enum',
      default: '"partial"',
      description:
        '对齐轴上的粘滞行为。当设置"partial"时，只要触发器至少部分位于边界中,就会将内容保留在边界中，而 "always" 将无论如何将内容保留在边界中。',
    },
    {
      name: 'hideWhenDetached',
      type: 'boolean',
      default: 'false',
      description: '是否在触发器完全遮挡时隐藏内容。',
    },
  ]}
/>

### Tooltip.Arrow

一个可选的箭头元素，与工具提示一起呈现。它可用于在视觉上连接触发器与 `Tooltip.Content`。必须在 `Tooltip.Content` 内部渲染。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
    {
      name: 'width',
      type: 'number',
      default: 10,
      description: '箭头的宽度',
    },
    {
      name: 'height',
      type: 'number',
      default: 5,
      description: '箭头的高度',
    },
  ]}
/>
