---
title: Switch 开关
description: 一个允许用户在选中和未选中状态之间切换的控件。
component: true
links:
  doc: https://www.radix-ui.com/docs/primitives/components/switch
  api: https://www.radix-ui.com/docs/primitives/components/switch#api-reference
---

Switch 开关组件提供了一种直观的方式来切换布尔状态，通常用于设置或选项的开关控制。基于 Radix UI 构建，提供完全的可访问性和灵活的样式选项。

<ExamplePreview
  componentName="base-switch"
  demoName="default-demo"
  description="基础开关示例"
/>

## 安装

### CLI

```bash
npx imd@latest add base-switch
```

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```bash
npm install @imd/base-switch
```

</Step>
</Steps>

## 用法

```tsx
import { Switch } from '@imd/base-switch'
```

```tsx
<Switch checked={checked} onCheckedChange={setChecked} />
```

## 示例

### 基本用法

一个基础的 Switch 组件，展示了开关的基本状态和交互方式。

<ExamplePreview
  componentName="base-switch"
  demoName="default-demo"
  description="基础开关组件示例"
/>

### 禁用状态

展示开关组件在禁用状态下的外观和行为。

<ExamplePreview
  componentName="base-switch"
  demoName="disabled-demo"
  description="禁用状态的开关组件示例"
/>

## API

### Switch

一个允许用户在选中和未选中状态之间切换的控件。

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于组件的自定义 CSS 类',
    },
    {
      name: 'checked',
      type: 'boolean',
      description: '控制开关的选中状态（受控与非受控）',
    },
    {
      name: 'defaultChecked',
      type: 'boolean',
      description: '指定开关的初始选中状态（非受控与非受控）',
    },
    {
      name: 'onCheckedChange',
      type: '(checked: boolean) => void',
      typeSimple: 'function',
      description: '开关状态改变时的回调函数',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: '是否禁用开关',
    },
    {
      name: 'required',
      type: 'boolean',
      default: 'false',
      description: '在表单提交时，开关是否必须被选中',
    },
    {
      name: 'value',
      type: 'string',
      description: '与开关关联的表单值',
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '将组件的默认渲染元素更改为子元素',
    },
    {
      name: '...rest',
      type: 'React.HTMLAttributes<HTMLButtonElement>',
      description: '继承 button 元素的所有 HTML 属性，如 onClick、onFocus 等',
    },
  ]}
/>
