---
title: Tag 标签
description: 用于对信息进行标记、分类或筛选的紧凑标签，支持移除、自定义图标和多种视觉样式。
---

<ExamplePreview
  showSource="false"
  componentName="base-tag"
  demoName="default-demo"
  description="一个Tag组件。"
/>

## 安装

### CLI

```bash
npx imd@latest add base-tag
```

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```package-install
npm install @imd/base-tag
```

</Step>
</Steps>

## 用法

:::warn

通过 `TagIcon`、`TagContent`、`TagClose` 组合使用。

其中 `TagContent` 用于包裹标签的主要内容，提供更高的扩展性与样式控制。

:::

```tsx
import * as TagPrimitive from '@imd/base-tag'
import { BookmarkCheck, X } from 'lucide-react'
```

```tsx
<TagPrimitive.Root className="border-gray-4 flex h-7 items-center gap-2 rounded-md border border-solid px-2 py-1">
  <TagPrimitive.Icon>
    <BookmarkCheck size={16} />
  </TagPrimitive.Icon>
  <TagPrimitive.Content className="typography-body-small">
    标签
  </TagPrimitive.Content>
  <TagPrimitive.Close>
    <X size={16} />
  </TagPrimitive.Close>
</TagPrimitive.Root>
```

## 示例

### 默认

<ExamplePreview
  componentName="base-tag"
  demoName="default-demo"
  description="默认Tag组件"
/>

### 带图标

添加 `Tag.Icon` 组件，通过 `children` 来自定义图标。

<ExamplePreview
  componentName="base-tag"
  demoName="icon-demo"
  description="带图标的Tag组件"
/>

### 带关闭按钮

添加 `Tag.Close` 组件，通过 `children` 来自定义图标。

<ExamplePreview
  componentName="base-tag"
  demoName="close-demo"
  description="带关闭按钮的Tag组件"
/>

### asChild

将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为

<ExamplePreview
  componentName="base-tag"
  demoName="asChild-demo"
  description="使用asChild属性的Tag组件"
/>

## API

### Tag.Root

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
  ]}
/>

### Tag.Content

用于包裹标签的主要内容，提供更高的扩展性与样式控制。在需要自定义内容结构或配合图标、关闭按钮等元素使用时包裹内容部分，确保标签结构清晰、易于维护。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
  ]}
/>

### Tag.Icon

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
  ]}
/>

### Tag.Close

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为',
    },
  ]}
/>
