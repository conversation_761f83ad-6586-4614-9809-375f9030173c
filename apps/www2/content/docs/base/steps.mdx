---
title: Steps 步骤条
description: 提示用户进度以及当前的步骤，用于引导用户按照步骤完成任务的导航条。
featured: true
component: true
---

<ExamplePreview
  componentName="base-steps"
  demoName="default-demo"
  description="基础步骤条"
/>

## 关于

步骤条是一个基于 Ark UI 构建的无头组件，用于显示任务进度和引导用户完成多步骤流程。它提供完整的状态管理、键盘导航和无障碍支持，可以完全自定义样式。

## 安装

### CLI

```bash
npx imd@latest add base-steps
```

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```bash
npm install @imd/base-steps
```

</Step>
</Steps>

## 用法

```tsx showLineNumbers
<Source src="base-steps/default-demo.tsx" />
```

你还可以查看基本结构示例代码：

```tsx showLineNumbers
<Source src="base-steps/basic-demo.tsx" />
```

```tsx showLineNumbers
import { Steps } from '@imd/base-steps'

const steps = [
  { title: '步骤一', description: '这是步骤一的描述' },
  { title: '步骤二', description: '这是步骤二的描述' },
  { title: '步骤三', description: '这是步骤三的描述' },
]

<Steps.Root step={1} count={steps.length}>
  <Steps.List>
    {steps.map((step, index) => (
      <Steps.Item key={index} index={index}>
        <Steps.Indicator />
        <Steps.Content>
          <Steps.Title>{step.title}</Steps.Title>
          <Steps.Description>{step.description}</Steps.Description>
        </Steps.Content>
      </Steps.Item>
    ))}
  </Steps.List>
</Steps.Root>
```

## 示例

### 自定义步骤条

<ExamplePreview
  componentName="base-steps"
  demoName="custom-demo"
  description="自定义步骤条"
/>

## 属性

### Root

步骤条根节点组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
    {
      name: 'count',
      type: 'number',
      description: '步骤总数',
      required: true,
    },
    {
      name: 'defaultStep',
      type: 'number',
      description: '初始步骤索引，非受控与非受控下使用',
    },
    {
      name: 'step',
      type: 'number',
      description: '当前步骤索引（受控与非受控）',
    },
    {
      name: 'linear',
      type: 'boolean',
      default: 'false',
      description: '是否线性流程，若为 true，用户需按顺序完成步骤',
    },
    {
      name: 'orientation',
      type: '"horizontal" | "vertical"',
      default: 'horizontal',
      description: '步骤条方向',
    },
    {
      name: 'onStepChange',
      type: '(details: StepChangeDetails) => void',
      typeSimple: 'function',
      description: '步骤变化时的回调',
    },
    {
      name: 'onStepComplete',
      type: '() => void',
      typeSimple: 'function',
      description: '某步骤完成时的回调',
    },
    {
      name: 'ids',
      type: 'ElementIds',
      typeSimple: 'object',
      description: '自定义步骤条元素的 id 集合',
    },
  ]}
/>

### List

步骤列表容器组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### Item

单个步骤项组件。

<PropsTable
  data={[
    {
      name: 'index',
      type: 'number',
      description: '步骤索引',
      required: true,
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### Indicator

步骤指示器组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### Content

步骤内容区域组件。

<PropsTable
  data={[
    {
      name: 'index',
      type: 'number',
      description: '步骤索引',
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### Trigger

步骤触发器组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### NextTrigger

下一步触发器组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### PrevTrigger

上一步触发器组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### Progress

步骤进度条组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### Separator

步骤分隔符组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### CompletedContent

完成内容区域组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>

### RootProvider

步骤上下文提供者组件。

<PropsTable
  data={[
    {
      name: 'value',
      type: 'UseStepsReturn',
      typeSimple: 'object',
      description: '步骤上下文对象',
      required: true,
    },
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description: '使用传入的子元素作为默认渲染元素，合并其属性和行为',
    },
  ]}
/>
