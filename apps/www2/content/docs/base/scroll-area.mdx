---
title: Scroll Area 滚动框
description: 增强本机滚动功能，实现自定义、跨浏览器样式。基于 Radix UI 的 Scroll-area构建。
component: true
---

<ExamplePreview
  componentName="base-scroll-area"
  demoName="default-demo"
  description="基本的ScrollArea组件示例"
/>
## 安装

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```bash
npm install @imd/base-scroll-area
```

</Step>

</Steps>

## 用法

```tsx
import * as ScrollArea from '@imd/base-scroll-area'
```

```tsx
<ScrollArea.Root>
  <ScrollArea.Viewport />
  <ScrollArea.Scrollbar orientation="horizontal">
    <ScrollArea.Thumb />
  </ScrollArea.Scrollbar>
  <ScrollArea.Scrollbar orientation="vertical">
    <ScrollArea.Thumb />
  </ScrollArea.Scrollbar>
  <ScrollArea.Corner />
</ScrollArea.Root>
```

## API

### Root

包裹所有元素的容器组件。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description: '更改作为子元素传递的默认渲染元素，合并它们的道具和行为',
    },
    {
      name: 'type',
      type: '"auto" | "always" | "scroll" | "hover"',
      typeSimple: 'enum',
      default: '"hover"',
      description:
        '描述滚动条可见性的性质，类似于 MacOS 中的滚动条首选项如何控制本机滚动条的可见性。',
    },
    {
      name: 'scrollHideDelay',
      type: 'number',
      default: '600',
       description:
        '如果类型设置为“滚动”或“悬停”，则此属性确定用户停止与滚动条交互后滚动条隐藏之前的时间长度（以毫秒为单位）。',
    },

    {
      name: 'dir',
      required: false,
      type: '"ltr" | "rtl"',
      typeSimple: 'enum',
      description:'滚动区域的阅读方向。如果省略，则全局继承自 DirectionProvider 或采用 LTR（从左到右）阅读模式'
      },
    {
      name: 'nonce',
      required: false,
      type: 'string',
      description:'可选的 nonce 属性，传递给内联样式，以便在使用严格规则增强安全性的 CSP 启用环境中使用。',
    },

]}
/>

### Viewport

滚动区域的视口区域。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description: '更改作为子元素传递的默认渲染元素，合并它们的道具和行为。',
    },
  ]}
/>

### Scrollbar

垂直滚动条。添加第二个带有 orientation 属性的滚动条，以启用水平滚动。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description: '更改作为子元素传递的默认渲染元素，合并它们的道具和行为。',
    },
    {
      name: 'forceMount',
      type: 'boolean',
      description:
        '当需要更多控制时，用于强制挂载。在使用 React 动画库控制动画时非常有用。',
    },
    {
      name: 'orientation',
      required: false,
      type: '"horizontal" | "vertical"',
      typeSimple: 'enum',
      default: 'vertical',
      description: '滚动条的方向',
    },
  ]}
/>

### Thumb

在`ScrollArea.Scrollbar`中使用.

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description: '更改作为子元素传递的默认渲染元素，合并它们的道具和行为。',
    },
  ]}
/>

### Corner

垂直和水平滚动条相交的角。

<PropsTable
  data={[
    {
      name: 'asChild',
      required: false,
      type: 'boolean',
      default: 'false',
      description: '更改作为子元素传递的默认渲染元素，合并它们的道具和行为。',
    },
  ]}
/>
