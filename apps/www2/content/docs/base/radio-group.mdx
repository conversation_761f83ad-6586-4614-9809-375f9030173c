---
title: RadioGroup 单选框组
description: 一个用于在一组相关且互斥的选项中进行单项选择的组件。
---

<ExamplePreview
  componentName="base-radio-group"
  demoName="default-demo"
  description="A radio group with 3 items."
/>

## 安装

### CLI

```bash
npx imd@latest add base-radio-group
```

### 手动

<Steps>

<Step>

<h4>安装以下依赖:</h4>

```package-install
npm install @imd/base-radio-group
```

</Step>
</Steps>

## 用法

```tsx
import { RadioGroup } from '@imd/base-radio-group'
```

```tsx
<RadioGroup defaultValue="option-one">
  <div className="flex items-center space-x-2">
    <RadioGroupItem value="option-one" id="option-one" />
    <Label htmlFor="option-one">Option One</Label>
  </div>
  <div className="flex items-center space-x-2">
    <RadioGroupItem value="option-two" id="option-two" />
    <Label htmlFor="option-two">Option Two</Label>
  </div>
</RadioGroup>
```

### 设置disabled

<ExamplePreview
  componentName="base-radio-group"
  demoName="disabled-demo"
  description="A radio group with 2 items."
/>

## 属性

### Root

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为。',
    },
    {
      name: 'defaultValue',
      type: 'string',
      description:
        '组件初始渲染时默认选中的单选项的值，适用于不需要受控管理单选项状态的场景。',
    },
    {
      name: 'value',
      type: 'string',
      description: '当前被选中的单选项的值，受控与非受控下使用。',
    },
    {
      name: 'onChange',
      type: '(value: string) => void',
      typeSimple: 'function',
      description: '当选中的单选项发生变化时触发的回调函数。',
    },
    {
      name: 'onRadioChange',
      type: 'FormEventHandler',
      typeSimple: 'function',
      description: '原生 onChange 事件',
    },
    {
      name: 'disabled',
      type: 'boolean',
      description: '是否禁用整个单选组，禁用后用户无法进行交互。',
    },
  ]}
/>

### Item

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为。',
    },
    {
      name: 'value',
      type: 'string',
      description: '提交表单时作为数据传递的值。',
    },
    {
      name: 'disabled',
      type: 'boolean',
      description: '是否禁用该单选项，禁用后用户无法进行交互。',
    },
  ]}
/>
### Indicator

当 radio 被选中时渲染，可以修改选中样式或者添加 icon

<PropsTable
  data={[
    {
      name: 'asChild',
      type: 'boolean',
      default: 'false',
      description:
        '将组件的默认渲染元素更改为作为子元素传递的元素，合并它们的属性和行为。',
    },
    {
      name: 'forceMount',
      type: 'boolean',
      description:
        '用于在需要更多控制时强制渲染组件，通常配合 React 动画库控制动画使用。',
    },
  ]}
/>
