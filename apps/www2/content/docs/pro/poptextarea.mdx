---
title: Poptextarea 弹窗文本域
description: 一个用于批量输入的 popover + textarea  组件，支持文本分割和统计功能。
component: true
---

<ExamplePreview
  componentName="pro-poptextarea"
  demoName="default-demo"
  description="Poptextarea 组件的基本用法示例"
/>

## 安装

### CLI

```bash
npx imd@latest add pro-poptextarea
```

### 手动

<Steps>

<Step>
<h4>将以下代码复制并粘贴到项目中:</h4>
<SourceTabs src="pro-poptextarea" />
</Step>
</Steps>

## 用法

```tsx
import { Poptextarea, splitBatchValue } from '@/components/ui/pro-poptextarea'

// 非受控模式 - 默认触发器
<Poptextarea
  placeholder="请输入多个内容，支持换行、逗号、分号等分隔符"
  defaultValue="SF123456789012\nSF234567890123"
  onConfirm={(items, rawValue) => {
    console.log('分割后的数组:', items)
    console.log('原始输入值:', rawValue)
  }}
  onCancel={() => console.log('用户取消了输入')}
  onClear={() => console.log('用户清空了内容')}
/>

// 受控模式 - 自定义触发器
const [value, setValue] = useState('SF123456789012\nSF234567890123')
<Poptextarea
  value={value}
  onChange={(e) => setValue(e.target.value)}
  placeholder="多行输入"
  onConfirm={(items, rawValue) => {
    console.log('确认输入:', items, rawValue)
  }}
  trigger={
    <Button
      size="small"
      color="secondary"
      icon={<ExpandIcon />}
    >
      批量输入
    </Button>
  }
  popoverContentProps={{
    side: "top",
    align: "start"
  }}
/>

// 自定义分割函数 - 仅支持换行分隔
<Poptextarea
  customSplitBatchValue={(input) =>
    input.split('\n')
         .map(line => line.trim())
         .filter(Boolean)
  }
  placeholder="每行一个，不支持其他分隔符"
  onConfirm={(items) => {
    // 使用自定义分割后的结果
    console.log('自定义分割结果:', items)
  }}
/>

// 监听弹窗状态
<Poptextarea
  onOpenChange={(open) => {
    if (open) {
      console.log('弹窗打开')
    } else {
      console.log('弹窗关闭')
    }
  }}
/>
```

## 示例

### 自定义分割函数

<ExamplePreview
  componentName="pro-poptextarea"
  demoName="custom-split-demo"
  description="自定义分割函数"
/>

### 受控模式

<ExamplePreview
  componentName="pro-poptextarea"
  demoName="control-demo"
  description="可控模式批量搜索"
/>

## 属性

### Poptextarea

<PropsTable
  data={[
    {
      name: 'value',
      type: 'string',
      description: '受控模式下的输入值',
    },
    {
      name: 'defaultValue',
      type: 'string',
      description: '非受控模式下的默认值',
    },
    {
      name: 'placeholder',
      type: 'string',
      description: 'textarea 的占位文本',
    },
    {
      name: 'trigger',
      type: 'React.ReactNode',
      description: '自定义触发器。如不提供，使用默认的展开图标按钮',
    },
    {
      name: 'onConfirm',
      type: '(items: string[], value: string) => void',
      description: '确认按钮回调。items: 分割后的字符串数组，value: 原始输入值',
    },
    {
      name: 'onCancel',
      type: '() => void',
      description: '取消按钮回调',
    },
    {
      name: 'onClear',
      type: '() => void',
      description: '清除按钮回调。在清空 textarea 内容后触发',
    },
    {
      name: 'clearText',
      type: 'string',
      description: '清除按钮文本',
      default: '清空',
    },
    {
      name: 'cancelText',
      type: 'string',
      description: '取消按钮文本',
      default: '取消',
    },
    {
      name: 'confirmText',
      type: 'string',
      description: '确认按钮文本',
      default: '确认',
    },
    {
      name: 'onChange',
      type: '(e: React.ChangeEvent<HTMLTextAreaElement>) => void',
      description: 'textarea 内容变化回调',
    },
    {
      name: 'customSplitBatchValue',
      type: '(input: string) => string[]',
      description:
        '自定义分割函数。优先级高于默认分割，用于将输入字符串转为数组',
    },
    {
      name: 'popoverContentProps',
      type: 'PopoverContentProps',
      description: '传递给 PopoverContent 的属性，用于自定义弹窗位置、大小等',
    },
    {
      name: 'onOpenChange',
      type: '(open: boolean) => void',
      description: '弹窗开关状态变化回调',
    },
  ]}
/>

## 快捷键支持

- 在输入框中按下 **Ctrl+Enter**（Windows/Linux）或 **Command+Enter**（Mac）可直接确认输入，无需点击确认按钮。

## 文本分割机制

### 默认分割规则

```tsx
const splitBatchValue = (input: string): string[] => {
  return input
    .split(/,+|;+|；+|，+|\n+|\s+/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}
```

### 自定义分割

通过 `customSplitBatchValue` 属性可以自定义分割逻辑：

```tsx
// 示例：仅支持换行分隔
const customSplitBatchValue={(input) => {
  return input
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
}}

// 示例：支持特殊分隔符
const customSplitBatchValue={(input) => {
  return input
    .split(/[|｜]/)
    .map(item => item.trim())
    .filter(Boolean)
}}
```

## 无障碍访问

组件遵循 WAI-ARIA 标准：

- 弹窗设置了 `role="dialog"` 和 `aria-label`
- Textarea 设置了 `aria-label` 和 `aria-describedby`
- 提供屏幕阅读器友好的帮助文本
- 防止弹窗关闭时的自动聚焦干扰
