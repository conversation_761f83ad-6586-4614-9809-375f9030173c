---
title: TextField 文本输入框
description: 文本输入框组件，支持前后缀、清除、受控/非受控、禁用、尺寸、变体等丰富功能，适用于表单和复杂场景。
component: true
---

<ExamplePreview
  componentName="pro-textField"
  demoName="default-demo"
  description="基本的 ProTextField 组件示例，支持前缀和后缀。"
/>

## 安装

### CLI

```imd-add
npx imd add pro-textField
```

### 手动

<Steps>
  <Step>
    <h4>将以下代码复制并粘贴到项目中:</h4>
    <SourceTabs src="pro-textField" />
  </Step>
</Steps>

## 用法

```tsx
import { TextField } from '@/components/ui/pro-textField'
```

```tsx
<TextField placeholder="请输入" prefix="@" suffix=".com" />
```

## 示例

### 尺寸

ProTextField 提供不同尺寸选项，适应不同的界面设计需求。

<ExamplePreview
  componentName="pro-textField"
  demoName="size-demo"
  description="展示不同尺寸的 ProTextField 组件"
/>

### 形态变体

ProTextField 组件提供不同的形态变体，满足不同的界面设计需求。

<ExamplePreview
  componentName="pro-textField"
  demoName="variant-demo"
  description="展示不同形态变体的 ProTextField 组件"
/>

### 禁用

禁用状态的文本输入框，用户无法与之交互。

<ExamplePreview
  componentName="pro-textField"
  demoName="disabled-demo"
  description="展示禁用状态的 ProTextField 组件"
/>

### 清除功能

提供清除按钮，方便用户快速清空输入内容。

<ExamplePreview
  componentName="pro-textField"
  demoName="clear-demo"
  description="带有清除功能的 ProTextField 示例"
/>

### 前缀后缀

输入框前后可添加前缀及后缀内容。

<ExamplePreview
  componentName="pro-textField"
  demoName="prefix-suffix-demo"
  description="输入框前后添加前缀及后缀示例"
/>

### 前后标签

输入框前后可添加前后标签内容。

<ExamplePreview
  componentName="pro-textField"
  demoName="add-before-after-demo"
  description="输入框前后添加前缀及后缀示例"
/>

### 受控模式

在 React 中使用受控模式管理输入框的值。

<ExamplePreview
  componentName="pro-textField"
  demoName="control-demo"
  description="受控模式的 ProTextField 示例"
/>

### 搜索框

TextField.Search 是一个专门为搜索场景设计的组件，内置了搜索按钮，并提供了简洁的API。

<ExamplePreview
  componentName="pro-textField"
  demoName="search-demo"
  description="内置搜索按钮的文本输入组件，支持图标或文本按钮，可自定义按钮样式。"
/>

### 密码输入框

TextField.Password 是一个专门为密码输入场景设计的组件，内置了显示/隐藏密码的切换功能。

<ExamplePreview
  componentName="pro-textField"
  demoName="password-demo"
  description="内置密码显示/隐藏切换功能的密码输入组件。"
/>

#### 受控模式

Password 组件支持受控模式，可以控制密码的显示状态。

<ExamplePreview
  componentName="pro-textField"
  demoName="password-controlled-demo"
  description="受控模式的密码输入组件，可以程序化控制密码显示状态。"
/>

#### 尺寸变体

Password 组件支持不同的尺寸，与标准 TextField 组件保持一致。

<ExamplePreview
  componentName="pro-textField"
  demoName="password-size-demo"
  description="不同尺寸的密码输入组件示例。"
/>

#### 样式变体

Password 组件支持不同的样式变体，与标准 TextField 组件保持一致。

<ExamplePreview
  componentName="pro-textField"
  demoName="password-variant-demo"
  description="不同样式变体的密码输入组件示例。"
/>
### 表单集成

ProTextField 可以与 ProForm 完美结合，提供更简洁的表单开发体验。以下示例展示了如何在用户注册表单中使用 ProTextField，包括验证、错误处理和高级功能。

<ExamplePreview
  componentName="pro-textField"
  demoName="form-demo"
  description="展示 ProTextField 与 ProForm 结合的高级表单使用场景，包含多种输入类型和验证规则。"
/>

通过使用 ProForm，你可以：

1. **简化语法**：使用 `Form.Item` 组件包装字段，自动处理验证和错误状态
2. **自动验证**：验证错误会自动通过 `hasError` 属性传递给 ProTextField
3. **统一管理**：所有表单状态和验证规则统一在 form schema 中定义
4. **类型安全**：完整的 TypeScript 支持，确保字段类型安全

```tsx
import { Form } from '@/components/ui/pro-form'
import { TextField } from '@/components/ui/pro-textField'

// 在 Form.Item 中使用 ProTextField

;<Form.Item name="username" label="用户名" required>
  <TextField placeholder="请输入用户名" prefix={<UserIcon />} allowClear />
</Form.Item>
```

## API

### TextField

高级文本输入框组件，支持前后缀、清除、受控/非受控、禁用、尺寸、变体等。

<PropsTable
  data={[
    {
      name: 'className',
      type: 'string',
      description: '应用于组件根元素的自定义 CSS 类',
    },
    {
      name: 'size',
      type: '"small" | "medium" | "large"',
      default: 'medium',
      description: '输入框的尺寸',
    },
    {
      name: 'variant',
      type: '"outlined" | "filled" | "ghost"',
      default: 'filled',
      description: '形态变体',
    },
    {
      name: 'allowClear',
      type: 'boolean',
      default: 'false',
      description: '是否显示清空图标',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: '是否禁用输入',
    },
    {
      name: 'prefix',
      type: 'React.ReactNode',
      description: '前缀内容',
    },
    {
      name: 'suffix',
      type: 'React.ReactNode',
      description: '后缀内容',
    },
    {
      name: 'addBefore',
      type: 'React.ReactNode',
      description: '前缀标签内容',
    },
    {
      name: 'addAfter',
      type: 'React.ReactNode',
      description: '后缀标签内容，可用于添加按钮等元素',
    },
    {
      name: 'value',
      type: 'string | number',
      description: '输入框的值（受控模式）',
    },
    {
      name: 'defaultValue',
      type: 'string | number',
      description: '输入框的默认值（非受控模式）',
    },
    {
      name: 'placeholder',
      type: 'string',
      description: '占位符',
    },
    {
      name: 'onChange',
      type: '(e: React.ChangeEvent<HTMLInputElement>) => void',
      typeSimple: 'function',
      description: '值变化时的回调函数',
    },
    {
      name: 'hasError',
      type: 'boolean',
      default: 'false',
      description: '是否显示错误状态',
    },
    {
      name: 'data-form-item-id',
      type: 'string',
      description: '表单项 ID',
    },
  ]}
/>

### TextField.Search

搜索框组件，内置搜索按钮，专为搜索场景设计。

<PropsTable
  data={[
    {
      name: 'searchText',
      type: 'string',
      description: '搜索按钮文本，默认为空则显示搜索图标',
    },
    {
      name: 'onSearch',
      type: '(value: string | number) => void',
      typeSimple: 'function',
      description: '点击搜索按钮时的回调函数',
    },
    {
      name: 'buttonProps',
      type: 'ButtonProps',
      typeSimple: 'object',
      description: '搜索按钮的属性，可用于自定义按钮样式、颜色等',
    },
    {
      name: '...TextField的所有属性',
      type: '',
      description: 'TextField组件的所有属性都可以在这里使用',
    },
  ]}
/>

### TextField.Password

密码输入框组件，内置显示/隐藏密码的切换功能，专为密码输入场景设计。

<PropsTable
  data={[
    {
      name: 'defaultVisibility',
      type: 'boolean',
      default: 'false',
      description: '初始显示状态，true为显示密码，false为隐藏密码',
    },
    {
      name: 'visibility',
      type: 'boolean',
      description: '受控的显示状态，当提供此属性时组件将以受控模式运行',
    },
    {
      name: 'onVisibilityChange',
      type: '(visible: boolean) => void',
      typeSimple: 'function',
      description: '显示状态变化时的回调函数',
    },
    {
      name: '...TextField的所有属性',
      type: '',
      description:
        'TextField组件的所有属性都可以在这里使用（除了type和suffix，这两个属性由组件内部管理）',
    },
  ]}
/>
