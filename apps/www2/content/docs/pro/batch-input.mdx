---
title: BatchInput 批量输入
description: 结合 TextField 和 Poptextarea 的批量输入组件。
component: true
---

<ExamplePreview
  componentName="pro-batch-input"
  demoName="default-demo"
  description="ProBatchInput 组件的基本用法示例"
/>

## 安装

### CLI

```bash
npx imd@latest add pro-batch-input
```

### 手动

<Steps>

<Step>
<h4>将以下代码复制并粘贴到项目中:</h4>
<SourceTabs src="pro-batch-input" />
</Step>
</Steps>

## 用法

```tsx
import { ProBatchInput, splitBatchValue } from '@/components/ui/pro-batch-input'

// 非受控模式 - 基础用法
<ProBatchInput
  variant="outlined"
  placeholder="请输入内容或点击右侧按钮批量输入"
  defaultValue="默认值"
  poptextareaProps={{
    placeholder: "请输入多个内容，支持换行、逗号、分号分隔",
    popoverContentProps: {
      side: "bottom",
      align: "end"
    }
  }}
  onBatchConfirm={(items, rawValue) => {
    console.log('分割后的数组:', items)
    console.log('原始输入值:', rawValue)
  }}
/>

// 受控模式
const [value, setValue] = useState('')
<ProBatchInput
  variant="outlined"
  value={value}
  onChange={(e) => setValue(e.target.value)}
  onBatchConfirm={(items, rawValue) => {
    console.log('批量输入确认:', items, rawValue)
    // 受控模式下，批量输入会自动更新主输入框的值
  }}
  poptextareaProps={{
    placeholder: "请输入多个内容"
  }}
/>

// 自定义分割函数 - 仅支持换行分隔
<ProBatchInput
  customSplitBatchValue={(input) =>
    input.split('\n')
         .map(line => line.trim())
         .filter(Boolean)
  }
  poptextareaProps={{
    placeholder: "每行一个，不支持其他分隔符"
  }}
/>
```

## 示例

### 受控与非受控模式

<ExamplePreview
  componentName="pro-batch-input"
  demoName="controlled-uncontrolled-demo"
  description="展示受控模式和非受控模式的区别和用法"
/>

### 自定义分割函数

<ExamplePreview
  componentName="pro-batch-input"
  demoName="custom-split-demo"
  description="使用自定义分割函数，仅支持换行分割"
/>

## 属性

### ProBatchInput

<PropsTable
  data={[
    {
      name: 'value',
      type: 'string | number',
      description: '输入框的值（受控模式）',
    },
    {
      name: 'defaultValue',
      type: 'string | number',
      description: '输入框的默认值（非受控模式）',
    },
    {
      name: 'placeholder',
      type: 'string',
      description: '占位符',
    },
    {
      name: 'onChange',
      type: '(e: React.ChangeEvent<HTMLInputElement>) => void',
      typeSimple: 'function',
      description: '值变化时的回调函数',
    },
    {
      name: 'onConfirm',
      type: '(items: string[], rawValue: string) => void',
      description:
        '批量输入确认回调。items: 分割后的字符串数组，rawValue: 用户输入的原始字符串',
    },
    {
      name: 'onCancel',
      type: '() => void',
      description: '批量输入取消回调',
    },
    {
      name: 'onBatchClear',
      type: '() => void',
      description:
        '批量输入清除回调。注意：清除操作只会清空弹窗内容，不会清空主输入框',
    },
    {
      name: 'customSplitBatchValue',
      type: '(input: string) => string[]',
      description: '自定义分割函数，将输入字符串转为数组',
    },
    {
      name: 'poptextareaProps',
      type: 'Pick<PoptextareaProps, | "trigger" | "placeholder" | "popoverContentProps" | "clearText" | "cancelText" | "confirmText">',
      description:
        '传递给 Poptextarea 组件的属性，包括 placeholder 和 popoverContentProps',
    },
  ]}
/>

继承 `TextField` 的所有属性，除了 `suffix` 属性会被批量输入按钮占用。

## 快捷键支持

- 在弹窗输入框中按下 **Ctrl+Enter**（Windows/Linux）或 **Command+Enter**（Mac）可直接确认输入，无需点击确认按钮。

### 状态同步机制

1. **主输入框 → 弹窗**：弹窗打开时自动回显主输入框的值
2. **弹窗 → 主输入框**：点击确认后，原始输入值会更新到主输入框
3. **清除操作**：只清空弹窗内容，不影响主输入框（通过 `onBatchClear` 回调可自定义行为）

## 文本分割机制

### 默认分割规则

```tsx
const splitBatchValue = (input: string): string[] => {
  return input
    .split(/,+|;+|；+|，+|\n+|\s+/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}
```

### 自定义分割

通过 `customSplitBatchValue` 属性可以自定义分割逻辑：

```tsx
// 示例：仅支持换行分隔
const customSplitBatchValue={(input) => {
  return input
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
}}

// 示例：支持特殊分隔符
const customSplitBatchValue={(input) => {
  return input
    .split(/[|｜]/)
    .map(item => item.trim())
    .filter(Boolean)
}}
```
