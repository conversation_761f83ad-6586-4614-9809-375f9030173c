'use client'

/**
 * 批量输入组件，结合 TextField 和 Poptextarea
 */
import React from 'react'

import {
  Poptextarea,
  splitBatchValue,
  type PoptextareaProps,
} from '@/registry/default/ui/pro-poptextarea'
import {
  TextField,
  type TextFieldProps,
} from '@/registry/default/ui/pro-textField'

interface ProBatchInputProps extends Omit<TextFieldProps, 'suffix'> {
  /** 批量输入确认回调 */
  onConfirm?: (items: string[], rawValue: string) => void
  /** 批量输入取消回调 */
  onCancel?: () => void
  /** 批量输入清除回调 */
  onBatchClear?: () => void
  /** 自定义分割函数 */
  customSplitBatchValue?: (input: string) => string[]
  /** 弹窗文本域属性 */
  poptextareaProps?: Pick<
    PoptextareaProps,
    | 'trigger'
    | 'placeholder'
    | 'popoverContentProps'
    | 'clearText'
    | 'cancelText'
    | 'confirmText'
  >
}

const ProBatchInput = React.forwardRef<HTMLInputElement, ProBatchInputProps>(
  (props, ref) => {
    const {
      onConfirm,
      onCancel,
      onBatchClear,
      customSplitBatchValue,
      value,
      defaultValue,
      onChange,
      poptextareaProps,
      ...textFieldProps
    } = props

    const isControlled = value !== undefined
    const [internalBatchValue, setInternalBatchValue] = React.useState(
      defaultValue?.toString() || ''
    )
    const [popoverValue, setPopoverValue] = React.useState('')

    const finalTextFieldValue = isControlled
      ? value?.toString() || ''
      : internalBatchValue

    // 当弹窗打开时，将 TextField 的值回显到弹窗 textarea
    const handlePopoverOpen = (open: boolean) => {
      if (open) {
        setPopoverValue(finalTextFieldValue)
      }
    }

    // 处理批量输入确认
    const handleBatchConfirm = (items: string[], rawValue: string) => {
      onConfirm?.(items, rawValue)

      if (isControlled) {
        const syntheticEvent = {
          target: { value: rawValue },
          currentTarget: { value: rawValue },
        } as React.ChangeEvent<HTMLInputElement>
        onChange?.(syntheticEvent)
      } else {
        setInternalBatchValue(rawValue)
      }
    }

    // 处理批量输入取消
    const handleBatchCancel = () => {
      onCancel?.()
    }

    // 处理批量输入清除
    const handleBatchClear = () => {
      onBatchClear?.()
      // 清空弹窗内容
      setPopoverValue('')
    }

    // 处理批量输入值变化
    const handleBatchChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      // 只更新弹窗的状态，不影响 TextField
      setPopoverValue(e.target.value)
    }

    // 处理 TextField 值变化
    const handleTextFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(e)

      // 非受控模式下同步更新内部状态
      if (!isControlled) {
        setInternalBatchValue(e.target.value)
      }
    }

    return (
      <TextField
        {...textFieldProps}
        value={finalTextFieldValue}
        onChange={handleTextFieldChange}
        ref={ref}
        suffix={
          <Poptextarea
            value={popoverValue}
            onChange={handleBatchChange}
            onConfirm={handleBatchConfirm}
            onCancel={handleBatchCancel}
            onClear={handleBatchClear}
            customSplitBatchValue={customSplitBatchValue}
            onOpenChange={handlePopoverOpen}
            {...poptextareaProps}
          />
        }
      />
    )
  }
)

ProBatchInput.displayName = 'ProBatchInput'

export { ProBatchInput, splitBatchValue }
export type { ProBatchInputProps }
