'use client'

import * as React from 'react'
import * as BaseButton from '@imd/base-button'
import { tv } from 'tailwind-variants'

import { cn } from '@/lib/utils'

const CHINESE_CHAR_REGEX = /[\u4e00-\u9fff]/

const isChinese = (char: string) => CHINESE_CHAR_REGEX.test(char)

// 检查文本是否为两个中文字符
const isChineseTwoChars = (text: string) => {
  const trimmed = text.trim()
  return trimmed.length === 2 && isChinese(trimmed[0]) && isChinese(trimmed[1])
}

// 按钮的样式
const buttonVariants = tv({
  base: [
    // 布局相关
    'inline-flex items-center justify-center',
    // 文本相关
    'whitespace-nowrap font-semibold',
    // 交互相关
    'cursor-pointer transition-colors',
    // 形状相关
    'rounded-sm',
    // 焦点相关
    'focus-visible:ring-primary focus-visible:outline-none focus-visible:ring-2',
    // 禁用状态
    'pointer-events-auto disabled:cursor-not-allowed',
  ],
  variants: {
    variant: {
      contained: 'bg-primary text-primary-foreground hover:bg-primary/90',
      outlined: 'border border-solid',
      text: 'hover:bg-accent hover:text-accent-foreground',
    },
    color: {
      primary: '',
      secondary: '',
      default: '',
      error: '',
      'light-error': '',
      'linear-gradient': '',
      search: '',
      reset: '',
    },
    size: {
      small: 'h-6 px-2 py-0.5 text-xs leading-5',
      medium: 'h-8 px-4 py-1.5 text-xs leading-5',
      large: 'h-9.5 px-6 py-2 text-sm leading-[22px]',
    },
    onlyIcon: {
      true: '',
      false: '',
    },
  },
  compoundVariants: [
    {
      color: 'primary',
      variant: 'contained',
      class: [
        'bg-primary text-white',
        'hover:bg-primary-7',
        'focus:bg-primary-8',
        'disabled:bg-primary-4',
      ],
    },
    {
      color: 'secondary',
      variant: 'contained',
      class: [
        'bg-gray-2 text-gray-13 [&_.ButtonIcon]:text-gray-10',
        'hover:bg-gray-3 hover:[&_.ButtonIcon]:text-gray-10',
        'focus:bg-gray-4 focus:[&_.ButtonIcon]:text-gray-10',
        'disabled:bg-gray-2 disabled:text-gray-5 [&:disabled_.ButtonIcon]:text-gray-5',
      ],
    },
    {
      color: 'primary',
      variant: 'outlined',
      class: [
        'border-primary text-primary',
        'hover:border-primary-7 hover:text-primary-7 hover:bg-white',
        'focus:border-primary-8 focus:text-primary-8',
        'disabled:border-primary-4 disabled:text-primary-4',
      ],
    },
    {
      color: 'primary',
      variant: 'text',
      class: [
        'text-primary',
        'hover:text-primary-7 hover:bg-gray-2',
        'focus:text-primary focus:bg-primary-1',
        'disabled:text-primary-4',
      ],
    },
    {
      color: 'error',
      variant: 'contained',
      class: [
        'bg-red-6 text-white',
        'hover:bg-red-7',
        'focus:bg-red-8',
        'disabled:bg-red-4',
      ],
    },
    {
      color: 'light-error',
      variant: 'contained',
      class: [
        'bg-red-1 text-red-6',
        'hover:bg-red-2',
        'focus:bg-red-3',
        'disabled:bg-red-1 disabled:text-red-4',
      ],
    },
    {
      color: 'error',
      variant: 'outlined',
      class: [
        'border-red-6 text-red-6',
        'hover:border-red-7 hover:text-red-7 hover:bg-white',
        'focus:border-red-8 focus:text-red-8',
        'disabled:border-red-4 disabled:text-red-4',
      ],
    },
    {
      color: 'error',
      variant: 'text',
      class: [
        'text-red-6',
        'hover:text-red-6 hover:bg-gray-2',
        'focus:text-red-6 focus:bg-red-1',
        'disabled:text-red-4',
      ],
    },
    {
      color: 'default',
      variant: 'text',
      class: [
        'text-gray-13 [&_.ButtonIcon]:text-gray-10 group',
        'hover:text-gray-13 hover:bg-gray-2',
        'focus:text-primary focus:bg-primary-1',
        'disabled:text-gray-5 [&:disabled_.ButtonIcon]:text-gray-5',
      ],
    },
    {
      color: 'linear-gradient',
      variant: 'contained',
      class: [
        'text-white [background:linear-gradient(104deg,#459cff_1.04%,#2546ff_98.96%)]',
        'hover:[background:linear-gradient(102deg,#2546ff_2.66%,#459cff_98.01%)]',
        'focus:[background:linear-gradient(104deg,#377dcc_1.04%,#1e38cc_98.96%)]',
        'disabled:[background:linear-gradient(104deg,#adcff6_1.04%,#c5cdf7_98.96%)]',
      ],
    },
    {
      color: 'default',
      size: 'medium',
      variant: 'text',
      class: ['text-gray-10', 'hover:text-gray-10'],
    },
    {
      color: 'search',
      variant: 'contained',
      class: [
        'bg-gray-13 text-white',
        'hover:bg-gray-12',
        'focus:bg-gray-11',
        'disabled:bg-gray-13/50',
      ],
    },
    {
      color: 'reset',
      variant: 'outlined',
      class: [
        'border-gray-3 text-gray-13',
        'hover:border-gray-4',
        'focus:border-gray-5',
        'disabled:border-gray-3/50 disabled:text-gray-5',
      ],
    },
    {
      onlyIcon: true,
      size: 'small',
      class: 'h-6 w-6 p-[5px]',
    },
    {
      onlyIcon: true,
      size: 'medium',
      class: 'h-8 w-8 p-2',
    },
    {
      onlyIcon: true,
      size: 'large',
      class: 'h-9.5 w-9.5 p-2.5',
    },
  ],
  defaultVariants: {
    variant: 'contained',
    color: 'primary',
    size: 'medium',
    onlyIcon: false,
  },
})

const iconVariants = tv({
  base: 'ButtonIcon group-focus:text-primary group-disabled:text-gray-5  inline-flex',
  variants: {
    size: {
      medium: '[&>svg]:size-[16px] h-4 w-4',
      small: '[&>svg]:size-[16px] h-4 w-4',
      large: 'h-4.5 w-4.5 [&>svg]:size-[18px]',
    },
    position: {
      left: 'mr-2',
      middle: '',
      right: 'ml-2',
    },
  },
  compoundVariants: [
    {
      position: 'left',
      size: 'small',
      class: 'mr-1',
    },
    {
      position: 'right',
      size: 'small',
      class: 'ml-1',
    },
  ],
  defaultVariants: {
    size: 'medium',
    position: 'middle',
  },
})

interface ButtonIconProps extends React.HTMLAttributes<HTMLDivElement> {
  position?: 'left' | 'middle' | 'right'
  children: React.ReactNode
}

interface ButtonContextValue {
  size?: ButtonSize
  variant?: ButtonVariant
  color?: ButtonColor
  disabled?: boolean
}

export const ButtonContext = React.createContext<ButtonContextValue>({})

const ButtonIcon = React.forwardRef<
  React.ElementRef<typeof BaseButton.Icon>,
  ButtonIconProps
>(({ position = 'middle', className, children, ...props }, ref) => {
  const ctx = React.useContext(ButtonContext)

  return (
    <BaseButton.Icon
      ref={ref}
      data-ui-slot="button-icon"
      className={cn(
        iconVariants({
          position,
          size: ctx.size,
        }),
        className
      )}
      {...props}
    >
      {children}
    </BaseButton.Icon>
  )
})

ButtonIcon.displayName = 'ButtonIcon'

type ButtonColor =
  | 'primary'
  | 'secondary'
  | 'default'
  | 'error'
  | 'light-error'
  | 'linear-gradient'
  | 'search'
  | 'reset'
type ButtonVariant = 'contained' | 'outlined' | 'text'
type ButtonSize = 'small' | 'medium' | 'large'

interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'color'> {
  /**
   * 启用插槽模式，允许将按钮行为应用到自定义元素上
   * @default false
   */
  asChild?: boolean
  /**
   * 按钮变体风格
   * @default 'contained'
   */
  variant?: ButtonVariant
  /**
   * 按钮颜色主题
   * - primary: 主要颜色，用于重要操作
   * - secondary: 次要颜色，用于辅助操作
   * - default: 默认颜色，用于一般操作
   * - error: 错误颜色，用于危险操作
   * - light-error: 浅错误颜色，用于错误提示
   * - linear-gradient: 渐变色，用于特殊场景
   * - search: 搜索颜色，用于搜索相关操作
   * - reset: 重置颜色，用于重置操作
   * @default 'primary'
   */
  color?: ButtonColor
  /**
   * 按钮尺寸
   * @default 'medium'
   */
  size?: ButtonSize
}

type ButtonType = React.ForwardRefExoticComponent<
  ButtonProps & React.RefAttributes<HTMLButtonElement>
> & {
  Icon: typeof ButtonIcon
}

const Button: ButtonType = React.forwardRef(
  (
    {
      children,
      className,
      variant = 'contained',
      color,
      size,
      disabled,
      ...props
    },
    ref
  ) => {
    const contextValue = React.useMemo(
      () => ({ size, variant, color, disabled }),
      [size, variant, color, disabled]
    )

    const onlyIcon = React.useMemo(() => {
      const childrenArray = React.Children.toArray(children).filter(Boolean)
      return (
        childrenArray.length === 1 &&
        React.isValidElement(childrenArray[0]) &&
        childrenArray[0].type === ButtonIcon
      )
    }, [children])

    const chineseSpacing = React.useMemo(() => {
      return typeof children === 'string' && isChineseTwoChars(children)
    }, [children])

    return (
      <ButtonContext.Provider value={contextValue}>
        <BaseButton.Root
          ref={ref}
          data-ui-slot="button"
          data-variant={variant}
          disabled={disabled}
          className={cn(
            buttonVariants({ variant, color, size, onlyIcon }),
            className
          )}
          {...props}
        >
          {chineseSpacing && typeof children === 'string'
            ? children.split('').join(' ')
            : children}
        </BaseButton.Root>
      </ButtonContext.Provider>
    )
  }
) as ButtonType

Button.Icon = ButtonIcon
Button.displayName = 'Button'

export {
  Button,
  ButtonIcon,
  type ButtonContextValue,
  type ButtonIconProps,
  type ButtonProps,
}
