'use client'

/**
 * 用于批量输入的 popover + textarea 的小部件
 */
import React from 'react'

import { cn } from '@/lib/utils'
import { useTranslation } from '@/registry/default/hooks/use-translation'
import { Button, ButtonIcon } from '@/registry/default/ui/ui-button'
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverContentProps,
  PopoverPortal,
  PopoverTrigger,
} from '@/registry/default/ui/ui-popover'
import { Textarea } from '@/registry/default/ui/ui-textarea'

const ExpandIcon = React.memo(() => {
  return (
    <svg
      fill="currentColor"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M10.2584 2.91667H8.16667V1.75H12.25V5.83333H11.0833V3.74162L8.57914 6.24581L7.75419 5.42085L10.2584 2.91667ZM1.75 8.16667H2.91667V10.2584L5.42085 7.75419L6.24581 8.57914L3.74162 11.0833H5.83333V12.25H1.75V8.16667Z" />
    </svg>
  )
})

ExpandIcon.displayName = 'ExpandIcon'

interface PoptextareaProps {
  /** 受控 value */
  value?: string
  /** 非受控 defaultValue */
  defaultValue?: string
  /** placeholder */
  placeholder?: string
  /** 触发器 */
  trigger?: React.ReactNode
  /** 确认回调，将多行字符串转为去除空行和首尾空格的数组 和 当前输入框的原始值 现有格式化不满足的情况下，可以自行处理 */
  onConfirm?: (items: string[], value: string) => void
  /** 取消回调 */
  onCancel?: () => void
  /** 清除回调 */
  onClear?: () => void
  /** onChange */
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  /** PopoverContentProps */
  popoverContentProps?: PopoverContentProps
  /**
   * 自定义分割函数，将输入字符串转为数组
   * @param value 输入字符串
   * @returns 分割后的字符串数组
   */
  customSplitBatchValue?: (value: string) => string[]
  /** 打开状态变化回调 */
  onOpenChange?: (open: boolean) => void
  /** 清除按钮文本 */
  clearText?: string
  /** 取消按钮文本 */
  cancelText?: string
  /** 确认按钮文本 */
  confirmText?: string
}

/**
 * 按照分隔符将字符串转为去除空行和首尾空格的数组
 */
const splitBatchValue = (input: string): string[] => {
  return input
    .split(/,+|;+|；+|，+|\n+|\s+/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}

const Poptextarea = React.memo<PoptextareaProps>((props) => {
  const { t } = useTranslation()
  const {
    trigger,
    onConfirm,
    onCancel,
    onClear,
    value,
    defaultValue,
    onChange,
    placeholder,
    popoverContentProps = {},
    customSplitBatchValue,
    onOpenChange,
    clearText,
    cancelText,
    confirmText,
  } = props
  const isControlled = value !== undefined
  const [innerValue, setInnerValue] = React.useState(defaultValue ?? '')
  const [open, setOpen] = React.useState(false)

  const finalValue = isControlled ? value : innerValue

  // 优先使用自定义 customSplitBatchValue，否则用默认
  const splitFn = customSplitBatchValue ?? splitBatchValue

  const total = React.useMemo(() => {
    try {
      return splitFn(finalValue).length
    } catch (error) {
      console.warn('Parse function error:', error)
      return 0
    }
  }, [finalValue, splitFn])

  // 处理 confirm - 使用 useCallback 优化
  const handleConfirm = () => {
    try {
      const items = splitFn(finalValue)
      onConfirm?.(items, finalValue)
      setOpen(false)
    } catch (error) {
      console.warn('Parse function error in confirm:', error)
      onConfirm?.([], finalValue)
      setOpen(false)
    }
  }

  // 处理 clear - 使用 useCallback 优化
  const handleClear = () => {
    if (isControlled) {
      // 触发 onChange 事件清空值
      onChange?.({
        target: { value: '' },
      } as React.ChangeEvent<HTMLTextAreaElement>)
    } else {
      setInnerValue('')
    }
    onClear?.()
  }

  // 处理 cancel
  const handleCancel = () => {
    onCancel?.()
  }

  // 受控/非受控 onChange
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (isControlled) {
      onChange?.(e)
    } else {
      setInnerValue(e.target.value)
    }
  }

  return (
    <Popover
      action={['click']}
      open={open}
      onOpenChange={(v) => {
        setOpen(v)
        onOpenChange?.(v)
      }}
    >
      <PopoverTrigger asChild>
        {trigger ? (
          trigger
        ) : (
          <Button size="small" className="mr-[-8px]" color="secondary">
            <ButtonIcon>
              <ExpandIcon />
            </ButtonIcon>
          </Button>
        )}
      </PopoverTrigger>
      <PopoverPortal>
        <PopoverContent
          role="dialog"
          aria-label={t('Batch input popover')}
          side="bottom"
          align="end"
          sideOffset={12}
          alignOffset={-4}
          onCloseAutoFocus={(e) => {
            // 阻止自动聚焦到触发器按钮
            e.preventDefault()
          }}
          {...popoverContentProps}
          className={cn(
            'block w-[432px] max-w-none',
            popoverContentProps?.className
          )}
        >
          <Textarea
            aria-label={t('Batch input Textarea')}
            aria-describedby="batch-search-help"
            className="h-[200px] resize-none"
            value={finalValue}
            onChange={handleChange}
            placeholder={placeholder}
            onKeyDown={(e) => {
              if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault()
                handleConfirm()
              }
            }}
          />
          <div id="batch-search-help" className="sr-only">
            {t('Enter multiple search terms, one per line')}
          </div>
          <div className="mt-3 flex justify-between">
            <div className="flex items-center">
              <span className="typography-body-small text-gray-10 mr-1">
                {t('Total')} :{' '}
              </span>
              <span className="typography-body-small-plus text-gray-13">
                {total}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="text" onClick={handleClear}>
                {clearText ?? t('Clear')}
              </Button>
              <PopoverClose asChild>
                <Button color="secondary" onClick={handleCancel}>
                  {cancelText ?? t('Cancel')}
                </Button>
              </PopoverClose>
              <PopoverClose asChild>
                <Button onClick={handleConfirm}>
                  {confirmText ?? t('Confirm')}
                </Button>
              </PopoverClose>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  )
})

Poptextarea.displayName = 'ProPoptextarea'

export { Poptextarea, splitBatchValue }
export type { PoptextareaProps }
