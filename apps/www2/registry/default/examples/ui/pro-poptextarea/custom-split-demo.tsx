'use client'

import React from 'react'

import { Button } from '@/registry/default/ui/pro-button'
import { TextField } from '@/registry/default/ui/pro-textField'
import { Poptextarea } from '@/registry/default/ui/pro-poptextarea'

import { ExpandIcon } from './icon'

export default function Demo() {
  const [items, setItems] = React.useState<string[]>([])
  const [rawValue, setRawValue] = React.useState<string>('')
  // 仅支持换行分隔，不支持逗号
  const customSplitBatchValue = (input: string) => {
    return input
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
  }

  return (
    <div className="w-[400px] space-y-8">
      <TextField
        variant="outlined"
        placeholder="请输入（仅支持换行分隔）"
        value={rawValue}
        suffix={
          <Poptextarea
            customSplitBatchValue={customSplitBatchValue}
            placeholder="每行输入一个内容，不支持逗号分隔"
            onConfirm={(items, rawValue) => {
              setItems(items)
              setRawValue(rawValue)
            }}
            trigger={
              <Button
                className="mr-[-8px]"
                size="small"
                color="secondary"
                icon={<ExpandIcon />}
              />
            }
          />
        }
      />
      <div className="mb-2 text-sm text-gray-500">分割结果（仅换行分隔）</div>
      <div className="rounded-md border border-gray-200 p-3">
        <pre className="overflow-auto text-sm text-gray-700">
          {JSON.stringify(items, null, 2)}
        </pre>
      </div>
    </div>
  )
}
