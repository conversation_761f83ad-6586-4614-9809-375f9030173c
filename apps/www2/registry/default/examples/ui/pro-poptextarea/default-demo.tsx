'use client'

import React from 'react'

import { But<PERSON> } from '@/registry/default/ui/pro-button'
import { Poptextarea } from '@/registry/default/ui/pro-poptextarea'
import { TextField } from '@/registry/default/ui/pro-textField'

import { ExpandIcon } from './icon'

export default function Demo() {
  return (
    <div className="space-y-8">
      <div>
        <div className="mb-1 text-sm text-gray-500">默认触发器</div>
        <Poptextarea />
      </div>
      <div>
        <div className="mb-1 text-sm text-gray-500">结合TextField使用</div>
        <TextField
          variant="outlined"
          placeholder="请输入"
          suffix={
            <Poptextarea
              trigger={
                <Button
                  className="mr-[-8px]"
                  size="small"
                  color="secondary"
                  icon={<ExpandIcon />}
                />
              }
            />
          }
        />
      </div>
    </div>
  )
}
