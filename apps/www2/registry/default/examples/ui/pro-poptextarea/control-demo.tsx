'use client'

import React, { useState } from 'react'

import { Poptextarea } from '@/registry/default/ui/pro-poptextarea'
import { TextField } from '@/registry/default/ui/pro-textField'

export default function Demo() {
  const [value, setValue] = useState(
    'SF123456789012\nSF234567890123\nSF345678901234\nSF456789012345\nSF567890123456'
  )
  const [items, setItems] = useState<string[]>([
    'SF123456789012',
    'SF234567890123',
    'SF345678901234',
    'SF456789012345',
    'SF567890123456',
  ])
  return (
    <div className="w-[400px] space-y-8">
      <TextField
        variant="outlined"
        placeholder="请输入"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        suffix={
          <Poptextarea
            placeholder="多行搜索输入"
            onConfirm={(items, value) => {
              setValue(value)
              setItems(items)
            }}
            value={value}
            onChange={(e) => setValue(e.target.value)}
          />
        }
      />
      <div className="mb-2 text-sm text-gray-500">分割结果（受控模式）</div>
      <div className="rounded-md border border-gray-200 p-3">
        <pre className="overflow-auto text-sm text-gray-700">
          {JSON.stringify(items, null, 2)}
        </pre>
      </div>
    </div>
  )
}
