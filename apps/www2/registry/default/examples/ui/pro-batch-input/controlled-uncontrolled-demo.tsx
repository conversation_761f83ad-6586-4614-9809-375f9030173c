'use client'

import React from 'react'

import { ProBatchInput } from '@/registry/default/ui/pro-batch-input'

export default function Demo() {
  // 受控模式状态
  const [controlledValue, setControlledValue] =
    React.useState('初始值,示例数据,测试内容')
  const [controlledResult, setControlledResult] = React.useState<string[]>([])

  // 非受控模式结果
  const [uncontrolledResult, setUncontrolledResult] = React.useState<string[]>(
    []
  )

  // 受控模式处理函数
  const handleControlledChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setControlledValue(e.target.value)
    },
    []
  )

  const handleControlledBatchConfirm = React.useCallback(
    (items: string[], rawValue: string) => {
      console.log('受控模式批量输入:', items, rawValue)
      setControlledResult(items)
      // 可以选择将批量输入的内容设置到主输入框
      if (items.length > 0) {
        setControlledValue(items.join(', '))
      }
    },
    []
  )

  // 非受控模式处理函数
  const handleUncontrolledBatchConfirm = React.useCallback(
    (items: string[], rawValue: string) => {
      console.log('非受控模式批量输入:', items, rawValue)
      setUncontrolledResult(items)
    },
    []
  )

  return (
    <div className="space-y-8">
      {/* 受控模式示例 */}
      <div className="space-y-4">
        <div>
          <div className="mb-2 text-sm font-medium text-gray-700">受控模式</div>
          <div className="mb-2 text-xs text-gray-500">
            组件值由父组件的 state 管理，通过 value 和 onChange 控制
          </div>
          <ProBatchInput
            variant="outlined"
            placeholder="受控模式 - 当前值由父组件管理"
            value={controlledValue}
            onChange={handleControlledChange}
            onConfirm={handleControlledBatchConfirm}
          />
          <div className="mt-2 text-xs text-gray-400">
            当前值: &quot;{controlledValue}&quot; - 点击批量输入按钮查看回显效果
          </div>
        </div>

        {controlledResult.length > 0 && (
          <div>
            <div className="mb-2 text-sm text-gray-500">
              受控模式分割结果 ({controlledResult.length} 项):
            </div>
            <div className="rounded-md border border-gray-200 p-3">
              <pre className="overflow-auto text-sm text-gray-700">
                {JSON.stringify(controlledResult, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>

      {/* 非受控模式示例 */}
      <div className="space-y-4">
        <div>
          <div className="mb-2 text-sm font-medium text-gray-700">
            非受控模式
          </div>
          <div className="mb-2 text-xs text-gray-500">
            使用 defaultValue 设置初始值，组件内部管理状态
          </div>
          <ProBatchInput
            variant="outlined"
            placeholder="非受控模式 - 内部管理状态"
            defaultValue="默认值,可以回显到弹窗"
            onConfirm={handleUncontrolledBatchConfirm}
          />
        </div>

        {uncontrolledResult.length > 0 && (
          <div>
            <div className="mb-2 text-sm text-gray-500">
              非受控模式分割结果 ({uncontrolledResult.length} 项):
            </div>
            <div className="rounded-md border border-gray-200 p-3">
              <pre className="overflow-auto text-sm text-gray-700">
                {JSON.stringify(uncontrolledResult, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>

      {/* 说明文档 */}
      <div className="rounded border bg-gray-50 p-4">
        <div className="mb-2 text-sm font-medium text-gray-700">使用说明</div>
        <ul className="space-y-1 text-xs text-gray-600">
          <li>
            <strong>受控模式：</strong>使用 <code>value</code> 和{' '}
            <code>onChange</code> 属性，
            父组件管理所有状态，包括主输入框和批量输入弹窗的内容
          </li>
          <li>
            <strong>非受控模式：</strong>使用 <code>defaultValue</code>{' '}
            属性设置初值， 组件内部管理状态，批量输入确认后会自动清空弹窗内容
          </li>
          <li>
            <strong>值回显：</strong>点击批量输入按钮时，TextField
            的当前值会自动回显到弹窗中， 方便用户在现有内容基础上进行编辑
          </li>
          <li>
            <strong>确认更新：</strong>在批量输入过程中不会实时更新 TextField，
            只有点击确认时才会将分割后的第一个项目设置到主输入框
          </li>
        </ul>
      </div>
    </div>
  )
}
