'use client'

import React from 'react'

import {
  ProBatchInput,
  splitBatchValue,
} from '@/registry/default/ui/pro-batch-input'

export default function Demo() {
  const [value, setValue] = React.useState('')
  const [result, setResult] = React.useState<string[]>([])

  const handleBatchConfirm = React.useCallback(
    (items: string[], rawValue: string) => {
      console.log('批量输入确认:', items, rawValue)
      setResult(items)
      // 可以选择将第一个项目设置为主输入框的值
      if (items.length > 0) {
        setValue(items[0])
      }
    },
    []
  )

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      console.log('批量输入值变化:', e.target.value)
      setValue(e.target.value)
      setResult(splitBatchValue(e.target.value))
    },
    []
  )

  return (
    <div className="space-y-6">
      <div>
        <div className="mb-2 text-sm text-gray-500">基础用法</div>
        <ProBatchInput
          className="w-[300px]"
          variant="outlined"
          placeholder="请输入内容或点击右侧按钮批量输入"
          onChange={handleChange}
          onConfirm={handleBatchConfirm}
          poptextareaProps={{
            placeholder: '请输入',
          }}
        />
      </div>

      {result.length > 0 && (
        <div>
          <div className="mb-2 text-sm text-gray-500">
            分割结果 ({result.length} 项):
          </div>
          <div className="rounded-md border border-gray-200 p-3">
            <pre className="overflow-auto text-sm text-gray-700">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  )
}
