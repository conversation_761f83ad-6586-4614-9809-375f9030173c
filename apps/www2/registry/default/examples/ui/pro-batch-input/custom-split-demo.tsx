'use client'

import React from 'react'

import { ProBatchInput } from '@/registry/default/ui/pro-batch-input'

export default function Demo() {
  const [value, setValue] = React.useState('')
  const [result, setResult] = React.useState<string[]>([])

  // 自定义分割函数：只支持换行分割，不支持逗号和分号
  const customSplitBatchValue = React.useCallback((input: string): string[] => {
    return input
      .split(/\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
  }, [])

  const handleBatchConfirm = React.useCallback(
    (items: string[], rawValue: string) => {
      console.log('自定义分割结果:', items, rawValue)
      setResult(items)
      if (items.length > 0) {
        setValue(items[0])
      }
    },
    []
  )

  const handleChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setValue(e.target.value)
    },
    []
  )

  return (
    <div className="space-y-6">
      <div>
        <div className="mb-2 text-sm text-gray-500">
          自定义分割函数（仅支持换行分割）
        </div>
        <ProBatchInput
          variant="outlined"
          placeholder="使用自定义分割规则"
          value={value}
          onChange={handleChange}
          onConfirm={handleBatchConfirm}
          customSplitBatchValue={customSplitBatchValue}
          poptextareaProps={{
            placeholder: '请输入内容，每行一个，不支持逗号、分号分割',
          }}
        />
        <div className="mt-1 text-xs text-gray-400">
          此示例使用自定义分割函数，仅支持换行分割，不支持逗号、分号等其他分隔符
        </div>
      </div>

      {result.length > 0 && (
        <div>
          <div className="mb-2 text-sm text-gray-500">
            分割结果 ({result.length} 项):
          </div>
          <div className="rounded-md border border-gray-200 p-3">
            <pre className="overflow-auto text-sm text-gray-700">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  )
}
