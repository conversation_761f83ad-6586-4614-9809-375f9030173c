'use client'

import { useCallback, useMemo } from 'react'

import { useTranslation as useIMDTranslation } from '@/components/i18n-provider'
import type {
  TranslationParams,
  UseTranslationReturn,
} from '@/registry/default/types/i18n'

// 默认翻译
const translations = {
  'zh-CN': {
    Cancel: '取消',
    Confirm: '确认',
    'No Data': '暂无数据',
    PleaseSelect: '请选择',
    More: '查看更多',
    Less: '收起',
    Loading: '加载中',
    Filter: '筛选',
    Clear: '清除',
    Search: '搜索',
    Total: '统计',
  },
  'en-US': {
    Cancel: 'Cancel',
    Confirm: 'Confirm',
    'No Data': 'No Data',
    PleaseSelect: 'Please Select',
    More: 'More',
    Less: 'Less',
    Loading: 'Loading',
    Filter: 'Filter',
    Clear: 'Clear',
    Search: 'Search',
    Total: 'Total',
  },
} as const satisfies Record<string, Record<string, string>>

export function getDefaultTranslations(locale = 'zh-CN') {
  return translations[locale]
}

function useMockI18nextTranslation() {
  const { language, changeLanguage } = useIMDTranslation()

  const resources = useMemo(
    () => ({
      'zh-CN': {
        welcome: '欢迎使用我们的应用',
        'button.save': '保存',
        'form.validation.required': '此字段为必填项',
        'user.greeting': '你好，{{username}}！',
        'items.count': '共 {{count}} 个项目',
      },
      'en-US': {
        welcome: 'Welcome to our app',
        'button.save': 'Save',
        'form.validation.required': 'This field is required',
        'user.greeting': 'Hello, {{username}}!',
        'items.count': '{{count}} items in total',
      },
    }),
    []
  )

  const t = useCallback(
    (key: string, params?: TranslationParams) => {
      try {
        const template =
          resources[language as keyof typeof resources]?.[key] || key

        if (!params) return template

        return template.replace(
          /\{\{(\w+)\}\}/g,
          (match: string, paramKey: string) => {
            return params[paramKey] !== undefined
              ? String(params[paramKey])
              : match
          }
        )
      } catch (error) {
        console.warn(`Translation error for key "${key}":`, error)
        return key
      }
    },
    [resources, language]
  )

  const handleChangeLanguage = useCallback(
    (lang: string) => {
      try {
        changeLanguage?.(lang)
      } catch (error) {
        console.error('Failed to change language:', error)
      }
    },
    [changeLanguage]
  )

  return {
    t,
    i18n: {
      language,
      changeLanguage: handleChangeLanguage,
    },
  }
}

export function useTranslation(): UseTranslationReturn {
  const { t: imdT, language, changeLanguage } = useIMDTranslation()
  const { t: i18nextT, i18n } = useMockI18nextTranslation()

  return useMemo(() => {
    const t = (key: string, params?: TranslationParams): string => {
      try {
        const i18nextResult = i18nextT(key, params)
        if (i18nextResult === key) {
          return imdT(key, params)
        }
        return i18nextResult
      } catch (error) {
        console.warn(`Translation fallback error for key "${key}":`, error)
        return key
      }
    }

    const handleChangeLanguage = (lang: string) => {
      try {
        i18n.changeLanguage(lang)
        changeLanguage?.(lang)
      } catch (error) {
        console.error('Failed to change language in integrated hook:', error)
      }
    }

    return {
      t,
      language: language || i18n.language,
      changeLanguage: handleChangeLanguage,
    }
  }, [imdT, i18nextT, language, changeLanguage, i18n])
}

export const i18nextConfig = {
  example: `
    import i18n from 'i18next'
    import { initReactI18next } from 'react-i18next'
    import { getDefaultTranslations } from '@/registry/default/hooks/use-translation'

    const resources = {
      'zh-CN': {
        translation: {
          ...getDefaultTranslations('zh-CN'),
          'welcome': '欢迎使用我们的应用',
          'user.greeting': '你好，{{username}}！',
        }
      },
      'en-US': {
        translation: {
          ...getDefaultTranslations('en-US'),
          'welcome': 'Welcome to our app',
          'user.greeting': 'Hello, {{username}}!',
        }
      }
    }

    i18n.use(initReactI18next).init({
      resources,
      fallbackLng: 'en-US',
      lng: 'zh-CN',
      interpolation: { escapeValue: false }
    })
  `,
}
